// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentBrushStyleBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final HorizontalScrollView HoriZontalImageButton;

  @NonNull
  public final LinearLayout llRecycleView;

  @NonNull
  public final ImageButton ptn1;

  @NonNull
  public final ImageButton ptn10;

  @NonNull
  public final ImageButton ptn11;

  @NonNull
  public final ImageButton ptn12;

  @NonNull
  public final ImageButton ptn2;

  @NonNull
  public final ImageButton ptn3;

  @NonNull
  public final ImageButton ptn4;

  @NonNull
  public final ImageButton ptn5;

  @NonNull
  public final ImageButton ptn6;

  @NonNull
  public final ImageButton ptn7;

  @NonNull
  public final ImageButton ptn8;

  @NonNull
  public final ImageButton ptn9;

  @NonNull
  public final RecyclerView recycle;

  private FragmentBrushStyleBinding(@NonNull FrameLayout rootView,
      @NonNull HorizontalScrollView HoriZontalImageButton, @NonNull LinearLayout llRecycleView,
      @NonNull ImageButton ptn1, @NonNull ImageButton ptn10, @NonNull ImageButton ptn11,
      @NonNull ImageButton ptn12, @NonNull ImageButton ptn2, @NonNull ImageButton ptn3,
      @NonNull ImageButton ptn4, @NonNull ImageButton ptn5, @NonNull ImageButton ptn6,
      @NonNull ImageButton ptn7, @NonNull ImageButton ptn8, @NonNull ImageButton ptn9,
      @NonNull RecyclerView recycle) {
    this.rootView = rootView;
    this.HoriZontalImageButton = HoriZontalImageButton;
    this.llRecycleView = llRecycleView;
    this.ptn1 = ptn1;
    this.ptn10 = ptn10;
    this.ptn11 = ptn11;
    this.ptn12 = ptn12;
    this.ptn2 = ptn2;
    this.ptn3 = ptn3;
    this.ptn4 = ptn4;
    this.ptn5 = ptn5;
    this.ptn6 = ptn6;
    this.ptn7 = ptn7;
    this.ptn8 = ptn8;
    this.ptn9 = ptn9;
    this.recycle = recycle;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentBrushStyleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentBrushStyleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_brush_style, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentBrushStyleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.HoriZontalImageButton;
      HorizontalScrollView HoriZontalImageButton = ViewBindings.findChildViewById(rootView, id);
      if (HoriZontalImageButton == null) {
        break missingId;
      }

      id = R.id.llRecycleView;
      LinearLayout llRecycleView = ViewBindings.findChildViewById(rootView, id);
      if (llRecycleView == null) {
        break missingId;
      }

      id = R.id.ptn1;
      ImageButton ptn1 = ViewBindings.findChildViewById(rootView, id);
      if (ptn1 == null) {
        break missingId;
      }

      id = R.id.ptn10;
      ImageButton ptn10 = ViewBindings.findChildViewById(rootView, id);
      if (ptn10 == null) {
        break missingId;
      }

      id = R.id.ptn11;
      ImageButton ptn11 = ViewBindings.findChildViewById(rootView, id);
      if (ptn11 == null) {
        break missingId;
      }

      id = R.id.ptn12;
      ImageButton ptn12 = ViewBindings.findChildViewById(rootView, id);
      if (ptn12 == null) {
        break missingId;
      }

      id = R.id.ptn2;
      ImageButton ptn2 = ViewBindings.findChildViewById(rootView, id);
      if (ptn2 == null) {
        break missingId;
      }

      id = R.id.ptn3;
      ImageButton ptn3 = ViewBindings.findChildViewById(rootView, id);
      if (ptn3 == null) {
        break missingId;
      }

      id = R.id.ptn4;
      ImageButton ptn4 = ViewBindings.findChildViewById(rootView, id);
      if (ptn4 == null) {
        break missingId;
      }

      id = R.id.ptn5;
      ImageButton ptn5 = ViewBindings.findChildViewById(rootView, id);
      if (ptn5 == null) {
        break missingId;
      }

      id = R.id.ptn6;
      ImageButton ptn6 = ViewBindings.findChildViewById(rootView, id);
      if (ptn6 == null) {
        break missingId;
      }

      id = R.id.ptn7;
      ImageButton ptn7 = ViewBindings.findChildViewById(rootView, id);
      if (ptn7 == null) {
        break missingId;
      }

      id = R.id.ptn8;
      ImageButton ptn8 = ViewBindings.findChildViewById(rootView, id);
      if (ptn8 == null) {
        break missingId;
      }

      id = R.id.ptn9;
      ImageButton ptn9 = ViewBindings.findChildViewById(rootView, id);
      if (ptn9 == null) {
        break missingId;
      }

      id = R.id.recycle;
      RecyclerView recycle = ViewBindings.findChildViewById(rootView, id);
      if (recycle == null) {
        break missingId;
      }

      return new FragmentBrushStyleBinding((FrameLayout) rootView, HoriZontalImageButton,
          llRecycleView, ptn1, ptn10, ptn11, ptn12, ptn2, ptn3, ptn4, ptn5, ptn6, ptn7, ptn8, ptn9,
          recycle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
