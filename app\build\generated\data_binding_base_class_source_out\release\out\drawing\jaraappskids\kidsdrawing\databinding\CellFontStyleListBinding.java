// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CellFontStyleListBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cvChild;

  @NonNull
  public final AppCompatTextView tvFontStyle;

  private CellFontStyleListBinding(@NonNull CardView rootView, @NonNull CardView cvChild,
      @NonNull AppCompatTextView tvFontStyle) {
    this.rootView = rootView;
    this.cvChild = cvChild;
    this.tvFontStyle = tvFontStyle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static CellFontStyleListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CellFontStyleListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.cell_font_style_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CellFontStyleListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cvChild = (CardView) rootView;

      id = R.id.tvFontStyle;
      AppCompatTextView tvFontStyle = ViewBindings.findChildViewById(rootView, id);
      if (tvFontStyle == null) {
        break missingId;
      }

      return new CellFontStyleListBinding((CardView) rootView, cvChild, tvFontStyle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
