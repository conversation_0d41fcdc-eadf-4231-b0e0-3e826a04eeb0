// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFragmentLevelBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final LinearLayout btnEasy;

  @NonNull
  public final LinearLayout btnHard;

  @NonNull
  public final AppCompatImageView ivClose;

  @NonNull
  public final LinearLayout llLevel;

  @NonNull
  public final TextView tvDialogTitle;

  private DialogFragmentLevelBinding(@NonNull CardView rootView, @NonNull LinearLayout btnEasy,
      @NonNull LinearLayout btnHard, @NonNull AppCompatImageView ivClose,
      @NonNull LinearLayout llLevel, @NonNull TextView tvDialogTitle) {
    this.rootView = rootView;
    this.btnEasy = btnEasy;
    this.btnHard = btnHard;
    this.ivClose = ivClose;
    this.llLevel = llLevel;
    this.tvDialogTitle = tvDialogTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFragmentLevelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFragmentLevelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_fragment_level, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFragmentLevelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnEasy;
      LinearLayout btnEasy = ViewBindings.findChildViewById(rootView, id);
      if (btnEasy == null) {
        break missingId;
      }

      id = R.id.btnHard;
      LinearLayout btnHard = ViewBindings.findChildViewById(rootView, id);
      if (btnHard == null) {
        break missingId;
      }

      id = R.id.ivClose;
      AppCompatImageView ivClose = ViewBindings.findChildViewById(rootView, id);
      if (ivClose == null) {
        break missingId;
      }

      id = R.id.llLevel;
      LinearLayout llLevel = ViewBindings.findChildViewById(rootView, id);
      if (llLevel == null) {
        break missingId;
      }

      id = R.id.tvDialogTitle;
      TextView tvDialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDialogTitle == null) {
        break missingId;
      }

      return new DialogFragmentLevelBinding((CardView) rootView, btnEasy, btnHard, ivClose, llLevel,
          tvDialogTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
