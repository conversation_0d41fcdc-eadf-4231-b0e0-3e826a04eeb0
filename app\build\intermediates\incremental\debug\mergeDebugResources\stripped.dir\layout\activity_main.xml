<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_colorful_gradient">

    <!-- Animated Background Elements -->
    <!-- Floating Clouds - Repositioned for better visibility -->
    <ImageView
        android:id="@+id/ivCloud1"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginTop="80dp"
        android:layout_marginStart="40dp"
        android:src="@android:drawable/ic_notification_overlay"
        android:alpha="0.7"
        android:tint="#B3E5FC"
        android:contentDescription="Floating cloud decoration" />

    <ImageView
        android:id="@+id/ivCloud2"
        android:layout_width="60dp"
        android:layout_height="30dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="140dp"
        android:layout_marginEnd="50dp"
        android:src="@android:drawable/ic_notification_overlay"
        android:alpha="0.6"
        android:tint="#81D4FA"
        android:contentDescription="Floating cloud decoration" />

    <!-- Twinkling Stars - Better positioning -->
    <ImageView
        android:id="@+id/ivStar1"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="60dp"
        android:src="@android:drawable/btn_star_big_on"
        android:alpha="0.8"
        android:contentDescription="Twinkling star decoration" />

    <ImageView
        android:id="@+id/ivStar2"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="100dp"
        android:src="@android:drawable/btn_star_big_on"
        android:alpha="0.6"
        android:contentDescription="Twinkling star decoration" />
        
    <!-- Additional decorative stars - Safe positioning -->
    <ImageView
        android:id="@+id/ivStar3"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginTop="180dp"
        android:layout_marginStart="80dp"
        android:src="@android:drawable/btn_star_big_on"
        android:alpha="0.5"
        android:contentDescription="Twinkling star decoration" />

    <ImageView
        android:id="@+id/ivStar4"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="120dp"
        android:layout_marginStart="100dp"
        android:src="@android:drawable/btn_star_big_on"
        android:alpha="0.4"
        android:contentDescription="Twinkling star decoration" />

    <!-- Main Content Container - Anti-clipping settings -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/llAdView"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:clipChildren="false"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="20dp"
            android:clipToPadding="false"
            android:clipChildren="false">

            <!-- App Title with Fun Typography - Anti-clipping design -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginTop="60dp"
                android:layout_marginBottom="40dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:paddingTop="15dp"
                android:paddingBottom="15dp">

                <TextView
                    android:id="@+id/tvAppTitle1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="DRAWING FOR"
                    android:textSize="26sp"
                    android:textColor="#4ECDC4"
                    android:textStyle="bold"
                    android:letterSpacing="0.1"
                    android:elevation="4dp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvAppTitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="KIDS!"
                    android:textSize="32sp"
                    android:textColor="#FF6B9D"
                    android:textStyle="bold"
                    android:letterSpacing="0.15"
                    android:elevation="4dp"
                    android:layout_marginBottom="8dp" />

                <!-- Fun Emoji Decoration -->
                <TextView
                    android:id="@+id/tvEmojiDecoration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎨 ✨ 🌈"
                    android:textSize="24sp"
                    android:layout_marginTop="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="8dp" />

            </LinearLayout>

            <!-- Main Action Buttons with 3D Effect -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="20dp">

                <!-- Let's Play Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardLetsPlay"
                    android:layout_width="280dp"
                    android:layout_height="70dp"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="35dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="#FF6B9D">

                    <RelativeLayout
                        android:id="@+id/ivLetsPlay"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_button_play"
                        android:clickable="true"
                        android:focusable="true">

<!-- ImageView removed for clean build -->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="🎮 Let's Play"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold" />

                    </RelativeLayout>
                </androidx.cardview.widget.CardView>

                <!-- My Creation Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardMyCreation"
                    android:layout_width="280dp"
                    android:layout_height="70dp"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="35dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="#4FC3F7">

                    <RelativeLayout
                        android:id="@+id/ivMyCreation"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_button_gallery"
                        android:clickable="true"
                        android:focusable="true">

<!--                        <ImageView-->
<!--                            android:layout_width="40dp"-->
<!--                            android:layout_height="40dp"-->
<!--                            android:layout_centerVertical="true"-->
<!--                            android:layout_marginStart="25dp"-->
<!--                            android:src="@drawable/ic_gallery_fun"-->
<!--                            android:tint="@color/white" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="🖼️ My Creation"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold" />

                    </RelativeLayout>
                </androidx.cardview.widget.CardView>

                <!-- Share Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardShare"
                    android:layout_width="280dp"
                    android:layout_height="70dp"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="35dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="#81C784">

                    <RelativeLayout
                        android:id="@+id/ivShare"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_button_share"
                        android:clickable="true"
                        android:focusable="true">

<!--                        <ImageView-->
<!--                            android:layout_width="40dp"-->
<!--                            android:layout_height="40dp"-->
<!--                            android:layout_centerVertical="true"-->
<!--                            android:layout_marginStart="25dp"-->
<!--                            android:src="@drawable/ic_share_fun"-->
<!--                            android:tint="@color/white" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="📤 Share"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold" />

                    </RelativeLayout>
                </androidx.cardview.widget.CardView>

                <!-- Rate Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardRate"
                    android:layout_width="280dp"
                    android:layout_height="70dp"
                    android:layout_marginBottom="20dp"
                    app:cardCornerRadius="35dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="#FFB74D">

                    <RelativeLayout
                        android:id="@+id/ivRate"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_button_rate"
                        android:clickable="true"
                        android:focusable="true">

<!--                        <ImageView-->
<!--                            android:layout_width="40dp"-->
<!--                            android:layout_height="40dp"-->
<!--                            android:layout_centerVertical="true"-->
<!--                            android:layout_marginStart="25dp"-->
<!--                            android:src="@drawable/ic_star_fun"-->
<!--                            android:tint="@color/white" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="⭐ Rate"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold" />

                    </RelativeLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- Ad Free Button with Special Design -->
            <androidx.cardview.widget.CardView
                android:layout_width="200dp"
                android:layout_height="55dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="27dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="#BA68C8">

                <RelativeLayout
                    android:id="@+id/btnAdFree"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_button_premium"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="✨ Ad Free"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>

    <!-- Banner Ad Container -->
    <RelativeLayout
        android:id="@+id/llAdView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:layout_gravity="center"
        android:gravity="center"
        android:elevation="4dp"
        android:visibility="visible" />

</RelativeLayout>