{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeReleaseResources-60:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c3a5dd56922f0d9d5fe7ff90c31db109\\transformed\\core-1.13.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,354,355,362,366,556,559", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1406,1470,1537,25955,26071,26528,26822,38280,38452", "endLines": "2,17,18,19,354,355,362,366,558,563", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1465,1532,1596,26066,26192,26649,26945,38447,38799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4886f4714567b788bedb48de3c472517\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,42,43,44,45,47,49,50,51,52,53,55,57,59,61,63,65,66,71,73,75,76,77,79,81,82,83,84,89,101,144,147,190,205,217,219,221,223,226,230,233,234,235,238,239,240,241,242,243,246,247,249,251,253,255,259,261,262,263,264,266,270,272,274,275,276,277,278,279,310,311,312,322,323,324,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2038,2129,2232,2335,2440,2547,2656,2765,2874,2983,3092,3199,3302,3421,3576,3731,3836,3957,4058,4205,4346,4449,4568,4675,4778,4933,5104,5253,5418,5575,5726,5845,6196,6345,6494,6606,6753,6906,7053,7128,7217,7304,7829,8921,11679,11864,14634,15767,16619,16742,16865,16978,17161,17416,17617,17706,17817,18050,18151,18246,18369,18498,18615,18792,18891,19026,19169,19304,19423,19624,19743,19836,19947,20003,20110,20305,20416,20549,20644,20735,20826,20919,21036,23469,23540,23623,24246,24303,24361,24985", "endLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,39,41,42,43,44,46,48,49,50,51,52,54,56,58,60,62,64,65,70,72,74,75,76,78,80,81,82,83,84,89,143,146,189,192,207,218,220,222,225,229,232,233,234,237,238,239,240,241,242,245,246,248,250,252,254,258,260,261,262,263,265,269,271,273,274,275,276,277,278,280,310,311,321,322,323,335,347", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2124,2227,2330,2435,2542,2651,2760,2869,2978,3087,3194,3297,3416,3571,3726,3831,3952,4053,4200,4341,4444,4563,4670,4773,4928,5099,5248,5413,5570,5721,5840,6191,6340,6489,6601,6748,6901,7048,7123,7212,7299,7400,7927,11674,11859,14629,14826,15961,16737,16860,16973,17156,17411,17612,17701,17812,18045,18146,18241,18364,18493,18610,18787,18886,19021,19164,19299,19418,19619,19738,19831,19942,19998,20105,20300,20411,20544,20639,20730,20821,20914,21031,21170,23535,23618,24241,24298,24356,24980,25616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\11557ed537c408f8869650a01b4f980f\\transformed\\media-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "356,359,363,367", "startColumns": "4,4,4,4", "startOffsets": "26197,26365,26654,26950", "endLines": "358,361,365,369", "endColumns": "12,12,12,12", "endOffsets": "26360,26523,26817,27112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56b21b1e2df5e7a9e2bbd796d8a365f1\\transformed\\material-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,248,344,441,509,588,676,764,852,940,1027,1114,1201,1288,1384,1474,1570,1660,1753,1860,1965,2084,2209,2330,2543,2802,3073,3291,3523,3759,4009,4222,4431,4662,4863,4979,5149,5470,6499,6956,7460,7968,8477,8991,9496,10000,10505,11011,11513,12019,12528,13036,13535,14042,14550,14842,15136,15436,15736,16065,16406,16544,16688,16844,17237,17455,17677,17903,18119,18229,18399,18589,18830,19089", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,243,339,436,504,583,671,759,847,935,1022,1109,1196,1283,1379,1469,1565,1655,1748,1855,1960,2079,2204,2325,2538,2797,3068,3286,3518,3754,4004,4217,4426,4657,4858,4974,5144,5465,6494,6951,7455,7963,8472,8986,9491,9995,10500,11006,11508,12014,12523,13031,13530,14037,14545,14837,15131,15431,15731,16060,16401,16539,16683,16839,17232,17450,17672,17898,18114,18224,18394,18584,18825,19084,19261"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,21,22,23,24,85,86,87,88,90,91,92,95,98,193,196,199,202,208,211,214,281,284,285,288,293,304,370,379,388,397,406,415,424,433,442,451,460,469,478,487,496,505,511,517,523,529,533,537,538,539,540,544,547,550,553,564,565,568,571,575,579", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,366,462,559,627,706,794,882,970,1058,1145,1232,1319,1666,1762,1852,1948,7405,7498,7605,7710,7932,8057,8178,8391,8650,14831,15049,15281,15517,15966,16179,16388,21175,21376,21492,21662,21983,23012,27117,27621,28129,28638,29152,29657,30161,30666,31172,31674,32180,32689,33197,33696,34203,34711,35003,35297,35597,35897,36226,36567,36705,36849,37005,37398,37616,37838,38064,38804,38914,39084,39274,39515,39774", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,21,22,23,24,85,86,87,88,90,91,94,97,100,195,198,201,204,210,213,216,283,284,287,292,303,309,378,387,396,405,414,423,432,441,450,459,468,477,486,495,504,510,516,522,528,532,536,537,538,539,543,546,549,552,555,564,567,570,574,578,581", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,361,457,554,622,701,789,877,965,1053,1140,1227,1314,1401,1757,1847,1943,2033,7493,7600,7705,7824,8052,8173,8386,8645,8916,15044,15276,15512,15762,16174,16383,16614,21371,21487,21657,21978,23007,23464,27616,28124,28633,29147,29652,30156,30661,31167,31669,32175,32684,33192,33691,34198,34706,34998,35292,35592,35892,36221,36562,36700,36844,37000,37393,37611,37833,38059,38275,38909,39079,39269,39510,39769,39946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4e3970f58cff9116df172ae1e89ec5d\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "20,348,351", "startColumns": "4,4,4", "startOffsets": "1601,25621,25777", "endLines": "20,350,353", "endColumns": "64,12,12", "endOffsets": "1661,25772,25950"}}]}]}