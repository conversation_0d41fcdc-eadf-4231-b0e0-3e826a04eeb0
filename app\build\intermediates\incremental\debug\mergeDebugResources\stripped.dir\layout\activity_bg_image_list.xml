<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#FFD1DC"
        android:clipChildren="false"
        android:clipToPadding="false">

    <!-- Fun Header with Gradient Background -->
    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:gravity="bottom"
            android:paddingBottom="@dimen/twenty_dp"
            android:paddingStart="@dimen/fifteen_dp"
            android:paddingEnd="@dimen/fifteen_dp"
            android:background="#FFD1DC"
            android:clipChildren="false">

        <!-- Floating Cloud Decorations -->
        <TextView
                android:id="@+id/cloudLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="☁️"
                android:textSize="24sp"
                android:layout_alignParentStart="true"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:alpha="0.8"/>

        <TextView
                android:id="@+id/cloudRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="☁️"
                android:textSize="20sp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="30dp"
                android:layout_marginTop="25dp"
                android:alpha="0.7"/>

        <!-- Fun Back Button with Card Background -->
        <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="25dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/kids_primary_pink"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp">

            <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_edit_back"


                    />

        </androidx.cardview.widget.CardView>

        <!-- Enhanced Title with Fun Typography -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingStart="80dp"
                android:paddingEnd="80dp">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎨"
                    android:textSize="28sp"
                    android:layout_marginEnd="8dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_select_your_image"
                    android:textSize="@dimen/twenty_four_sp"
                    android:textStyle="bold"
                    android:textColor="@color/colorWhite"
                    android:gravity="center"
                    android:letterSpacing="0.05"
                    android:shadowColor="@color/kids_shadow_color"
                    android:shadowDx="2"
                    android:shadowDy="2"
                    android:shadowRadius="4"/>

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🖼️"
                    android:textSize="28sp"
                    android:layout_marginStart="8dp"/>

        </LinearLayout>

        <!-- Sparkle Decorations -->
        <TextView
                android:id="@+id/sparkle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✨"
                android:textSize="18sp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="60dp"
                android:layout_marginTop="10dp"
                android:alpha="0.9"/>

        <TextView
                android:id="@+id/sparkle2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⭐"
                android:textSize="16sp"
                android:layout_alignParentStart="true"
                android:layout_marginStart="60dp"
                android:layout_marginTop="8dp"
                android:alpha="0.8"/>

    </RelativeLayout>

    <!-- Fun Instruction Text -->
    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:paddingStart="@dimen/twenty_dp"
            android:paddingEnd="@dimen/twenty_dp"
            android:paddingTop="@dimen/fifteen_dp"
            android:paddingBottom="@dimen/ten_dp">

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🌈"
                android:textSize="20sp"
                android:layout_marginEnd="8dp"/>

        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Pick your favorite background to start creating amazing art!"
                android:textSize="16sp"
                android:textColor="@color/kids_text_secondary"
                android:gravity="center"
                android:textStyle="italic"/>

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎯"
                android:textSize="20sp"
                android:layout_marginStart="8dp"/>

    </LinearLayout>

    <!-- Enhanced RecyclerView with Fun Container -->
    <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_margin="@dimen/fifteen_dp"
            app:cardCornerRadius="@dimen/twenty_dp"
            app:cardElevation="@dimen/ten_dp"
            app:cardBackgroundColor="@color/colorWhite"
            android:clipChildren="false">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/ten_dp">

            <!-- Fun Grid Header -->
            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:paddingBottom="@dimen/ten_dp">

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🖼️"
                        android:textSize="24sp"/>

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Choose Your Canvas!"
                        android:textSize="18sp"
                        android:textColor="@color/kids_primary_purple"
                        android:textStyle="bold"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"/>

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎨"
                        android:textSize="24sp"/>

            </LinearLayout>

            <!-- RecyclerView with Enhanced Padding -->
            <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvBGImageList"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:padding="@dimen/eight_dp"
                    android:clipToPadding="false"
                    android:clipChildren="false"
                    android:scrollbars="none"
                    android:overScrollMode="never"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Fun Encouragement Footer -->
    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:paddingStart="@dimen/twenty_dp"
            android:paddingEnd="@dimen/twenty_dp"
            android:paddingTop="@dimen/five_dp"
            android:paddingBottom="@dimen/ten_dp">

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💡"
                android:textSize="16sp"
                android:layout_marginEnd="6dp"/>

        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tap any image to start your artistic adventure!"
                android:textSize="14sp"
                android:textColor="@color/kids_text_hint"
                android:gravity="center"
                android:alpha="0.9"/>

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🚀"
                android:textSize="16sp"
                android:layout_marginStart="6dp"/>

    </LinearLayout>

    <!-- Ad Container with Kid-Friendly Design -->
    <androidx.cardview.widget.CardView
            android:id="@+id/llAdView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/ten_dp"
            app:cardCornerRadius="@dimen/fifteen_dp"
            app:cardElevation="@dimen/five_dp"
            app:cardBackgroundColor="@color/kids_soft_orange"
            android:visibility="visible">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="@dimen/ten_dp">

            <!-- Ad content will be inserted here -->

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Facebook Ad Container -->
    <LinearLayout
            android:id="@+id/llAdViewFacebook"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="@dimen/five_dp"
            android:visibility="visible" />

</LinearLayout>