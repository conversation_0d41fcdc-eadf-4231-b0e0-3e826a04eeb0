// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import drawing.jaraappskids.kidsdrawing.custom.SquareImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFullScreenBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatImageView ivBack;

  @NonNull
  public final AppCompatImageView ivShare;

  @NonNull
  public final SquareImageView ivSharePreview;

  @NonNull
  public final RelativeLayout llAdView;

  @NonNull
  public final LinearLayout llAdViewFacebook;

  @NonNull
  public final AppCompatTextView tvTitle;

  private ActivityFullScreenBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatImageView ivBack, @NonNull AppCompatImageView ivShare,
      @NonNull SquareImageView ivSharePreview, @NonNull RelativeLayout llAdView,
      @NonNull LinearLayout llAdViewFacebook, @NonNull AppCompatTextView tvTitle) {
    this.rootView = rootView;
    this.ivBack = ivBack;
    this.ivShare = ivShare;
    this.ivSharePreview = ivSharePreview;
    this.llAdView = llAdView;
    this.llAdViewFacebook = llAdViewFacebook;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFullScreenBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFullScreenBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_full_screen, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFullScreenBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivBack;
      AppCompatImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivShare;
      AppCompatImageView ivShare = ViewBindings.findChildViewById(rootView, id);
      if (ivShare == null) {
        break missingId;
      }

      id = R.id.ivSharePreview;
      SquareImageView ivSharePreview = ViewBindings.findChildViewById(rootView, id);
      if (ivSharePreview == null) {
        break missingId;
      }

      id = R.id.llAdView;
      RelativeLayout llAdView = ViewBindings.findChildViewById(rootView, id);
      if (llAdView == null) {
        break missingId;
      }

      id = R.id.llAdViewFacebook;
      LinearLayout llAdViewFacebook = ViewBindings.findChildViewById(rootView, id);
      if (llAdViewFacebook == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      AppCompatTextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityFullScreenBinding((LinearLayout) rootView, ivBack, ivShare, ivSharePreview,
          llAdView, llAdViewFacebook, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
