package drawing.jaraappskids.kidsdrawing.di;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideDrawingRepositoryFactory implements Factory<DrawingRepository> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideDrawingRepositoryFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DrawingRepository get() {
    return provideDrawingRepository(contextProvider.get());
  }

  public static AppModule_ProvideDrawingRepositoryFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_ProvideDrawingRepositoryFactory(contextProvider);
  }

  public static DrawingRepository provideDrawingRepository(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideDrawingRepository(context));
  }
}
