package drawing.jaraappskids.kidsdrawing.interfaces;

/**
 * Enhanced Ads Callback Interface with improved functionality
 * Created by <PERSON><PERSON><PERSON> on 06-Apr-19.
 * Updated for better ads management
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\b\u0010\u0004\u001a\u00020\u0003H&J\b\u0010\u0005\u001a\u00020\u0003H\u0016J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0016J\b\u0010\t\u001a\u00020\u0003H\u0016J\b\u0010\n\u001a\u00020\u0003H\u0016J\b\u0010\u000b\u001a\u00020\u0003H&J\u0018\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016J\b\u0010\u0010\u001a\u00020\u0003H&\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/interfaces/AdsCallback;", "", "adClose", "", "adLoadingFailed", "onAdClicked", "onAdFailedToShow", "errorMessage", "", "onAdImpression", "onAdShown", "onLoaded", "onRewardEarned", "type", "amount", "", "startNextScreen", "app_debug"})
public abstract interface AdsCallback {
    
    public abstract void adLoadingFailed();
    
    public abstract void adClose();
    
    public abstract void startNextScreen();
    
    public abstract void onLoaded();
    
    public abstract void onAdShown();
    
    public abstract void onAdClicked();
    
    public abstract void onAdImpression();
    
    public abstract void onRewardEarned(@org.jetbrains.annotations.NotNull()
    java.lang.String type, int amount);
    
    public abstract void onAdFailedToShow(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage);
    
    /**
     * Enhanced Ads Callback Interface with improved functionality
     * Created by Naynesh Patel on 06-Apr-19.
     * Updated for better ads management
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
        
        public static void onAdShown(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback $this) {
        }
        
        public static void onAdClicked(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback $this) {
        }
        
        public static void onAdImpression(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback $this) {
        }
        
        public static void onRewardEarned(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback $this, @org.jetbrains.annotations.NotNull()
        java.lang.String type, int amount) {
        }
        
        public static void onAdFailedToShow(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback $this, @org.jetbrains.annotations.NotNull()
        java.lang.String errorMessage) {
        }
    }
}