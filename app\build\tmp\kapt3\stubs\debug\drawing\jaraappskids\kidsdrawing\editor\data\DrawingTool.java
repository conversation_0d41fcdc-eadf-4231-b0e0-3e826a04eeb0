package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "", "(Ljava/lang/String;I)V", "BRUSH", "ERASER", "SHAPE_RECTANGLE", "SHAPE_CIRCLE", "SHAPE_LINE", "TEXT", "STICKER", "EYEDROPPER", "app_debug"})
public enum DrawingTool {
    /*public static final*/ BRUSH /* = new BRUSH() */,
    /*public static final*/ ERASER /* = new ERASER() */,
    /*public static final*/ SHAPE_RECTANGLE /* = new SHAPE_RECTANGLE() */,
    /*public static final*/ SHAPE_CIRCLE /* = new SHAPE_CIRCLE() */,
    /*public static final*/ SHAPE_LINE /* = new SHAPE_LINE() */,
    /*public static final*/ TEXT /* = new TEXT() */,
    /*public static final*/ STICKER /* = new STICKER() */,
    /*public static final*/ EYEDROPPER /* = new EYEDROPPER() */;
    
    DrawingTool() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool> getEntries() {
        return null;
    }
}