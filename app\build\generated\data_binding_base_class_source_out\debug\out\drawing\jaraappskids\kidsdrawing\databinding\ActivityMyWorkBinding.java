// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMyWorkBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final GridView gridOfMyWork;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final RelativeLayout llAdView;

  @NonNull
  public final LinearLayout llAdViewFacebook;

  @NonNull
  public final RelativeLayout rltMain;

  @NonNull
  public final RelativeLayout rltTop;

  @NonNull
  public final AppCompatTextView tvTitle;

  @NonNull
  public final TextView txtNoItem;

  private ActivityMyWorkBinding(@NonNull LinearLayout rootView, @NonNull GridView gridOfMyWork,
      @NonNull ImageView ivBack, @NonNull RelativeLayout llAdView,
      @NonNull LinearLayout llAdViewFacebook, @NonNull RelativeLayout rltMain,
      @NonNull RelativeLayout rltTop, @NonNull AppCompatTextView tvTitle,
      @NonNull TextView txtNoItem) {
    this.rootView = rootView;
    this.gridOfMyWork = gridOfMyWork;
    this.ivBack = ivBack;
    this.llAdView = llAdView;
    this.llAdViewFacebook = llAdViewFacebook;
    this.rltMain = rltMain;
    this.rltTop = rltTop;
    this.tvTitle = tvTitle;
    this.txtNoItem = txtNoItem;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMyWorkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMyWorkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_my_work, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMyWorkBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.gridOfMyWork;
      GridView gridOfMyWork = ViewBindings.findChildViewById(rootView, id);
      if (gridOfMyWork == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.llAdView;
      RelativeLayout llAdView = ViewBindings.findChildViewById(rootView, id);
      if (llAdView == null) {
        break missingId;
      }

      id = R.id.llAdViewFacebook;
      LinearLayout llAdViewFacebook = ViewBindings.findChildViewById(rootView, id);
      if (llAdViewFacebook == null) {
        break missingId;
      }

      id = R.id.rltMain;
      RelativeLayout rltMain = ViewBindings.findChildViewById(rootView, id);
      if (rltMain == null) {
        break missingId;
      }

      id = R.id.rltTop;
      RelativeLayout rltTop = ViewBindings.findChildViewById(rootView, id);
      if (rltTop == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      AppCompatTextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.txtNoItem;
      TextView txtNoItem = ViewBindings.findChildViewById(rootView, id);
      if (txtNoItem == null) {
        break missingId;
      }

      return new ActivityMyWorkBinding((LinearLayout) rootView, gridOfMyWork, ivBack, llAdView,
          llAdViewFacebook, rltMain, rltTop, tvTitle, txtNoItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
