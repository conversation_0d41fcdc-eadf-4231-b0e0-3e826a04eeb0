<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".mywork.FullScreenActivity">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

        <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="110dp"
                android:background="#FFD1DC">

            <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_edit_back"
                    android:padding="@dimen/ten_dp"
                    android:layout_centerVertical="true" />

            <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/ten_dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Large"
                    android:textStyle="bold"
                    android:text="Share"
                    android:textColor="@color/colorWhite" />

        </RelativeLayout>

        <drawing.jaraappskids.kidsdrawing.custom.SquareImageView
                android:id="@+id/ivSharePreview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scaleType="fitCenter"
                android:layout_margin="16dp"
                android:background="@color/colorWhite" />

        <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivShare"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/btn_home_share"
                android:layout_margin="16dp"
                android:layout_gravity="center_horizontal" />

    </LinearLayout>

    <RelativeLayout
            android:id="@+id/llAdView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:visibility="visible" />

    <LinearLayout
            android:id="@+id/llAdViewFacebook"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible" />


</LinearLayout>