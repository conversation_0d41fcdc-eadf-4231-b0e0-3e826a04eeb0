// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CellEditorItemListBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatImageView ivEditorItem;

  @NonNull
  public final LinearLayout llEditorItem;

  @NonNull
  public final AppCompatTextView tvEditorItem;

  private CellEditorItemListBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatImageView ivEditorItem, @NonNull LinearLayout llEditorItem,
      @NonNull AppCompatTextView tvEditorItem) {
    this.rootView = rootView;
    this.ivEditorItem = ivEditorItem;
    this.llEditorItem = llEditorItem;
    this.tvEditorItem = tvEditorItem;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CellEditorItemListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CellEditorItemListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.cell_editor_item_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CellEditorItemListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivEditorItem;
      AppCompatImageView ivEditorItem = ViewBindings.findChildViewById(rootView, id);
      if (ivEditorItem == null) {
        break missingId;
      }

      LinearLayout llEditorItem = (LinearLayout) rootView;

      id = R.id.tvEditorItem;
      AppCompatTextView tvEditorItem = ViewBindings.findChildViewById(rootView, id);
      if (tvEditorItem == null) {
        break missingId;
      }

      return new CellEditorItemListBinding((LinearLayout) rootView, ivEditorItem, llEditorItem,
          tvEditorItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
