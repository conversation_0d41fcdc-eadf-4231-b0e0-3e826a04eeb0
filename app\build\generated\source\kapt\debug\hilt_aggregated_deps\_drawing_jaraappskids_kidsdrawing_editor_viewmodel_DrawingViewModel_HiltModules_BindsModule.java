package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ViewModelComponent",
    modules = "drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingViewModel_HiltModules.BindsModule"
)
@Generated("dagger.hilt.processor.internal.aggregateddeps.AggregatedDepsGenerator")
public class _drawing_jaraappskids_kidsdrawing_editor_viewmodel_DrawingViewModel_HiltModules_BindsModule {
}
