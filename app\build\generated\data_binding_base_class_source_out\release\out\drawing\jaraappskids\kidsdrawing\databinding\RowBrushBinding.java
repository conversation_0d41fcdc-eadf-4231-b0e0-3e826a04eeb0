// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.viewbinding.ViewBinding;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class RowBrushBinding implements ViewBinding {
  @NonNull
  private final AppCompatImageView rootView;

  @NonNull
  public final AppCompatImageView ivPaintBrush;

  private RowBrushBinding(@NonNull AppCompatImageView rootView,
      @NonNull AppCompatImageView ivPaintBrush) {
    this.rootView = rootView;
    this.ivPaintBrush = ivPaintBrush;
  }

  @Override
  @NonNull
  public AppCompatImageView getRoot() {
    return rootView;
  }

  @NonNull
  public static RowBrushBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RowBrushBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.row_brush, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RowBrushBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    AppCompatImageView ivPaintBrush = (AppCompatImageView) rootView;

    return new RowBrushBinding((AppCompatImageView) rootView, ivPaintBrush);
  }
}
