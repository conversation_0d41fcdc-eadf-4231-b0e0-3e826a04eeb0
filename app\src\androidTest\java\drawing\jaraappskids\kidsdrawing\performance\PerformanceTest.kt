package drawing.jaraappskids.kidsdrawing.performance

import android.content.Intent
import android.graphics.Color
import androidx.benchmark.junit4.BenchmarkRule
import androidx.benchmark.junit4.measureRepeated
import androidx.test.core.app.ActivityScenario
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.activities.EditorActivity
import drawing.jaraappskids.kidsdrawing.common.CommonConstants
import drawing.jaraappskids.kidsdrawing.drwaing.WTDrawingView
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Performance and memory tests for drawing functionality
 * Tests drawing performance, memory usage, and system stability
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class PerformanceTest {

    @get:Rule
    val benchmarkRule = BenchmarkRule()

    @Test
    fun benchmarkDrawingPerformance() {
        val intent = Intent(InstrumentationRegistry.getInstrumentation().targetContext, EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                benchmarkRule.measureRepeated {
                    // Setup drawing configuration
                    drawingView.setStrokeColor(Color.BLUE)
                    drawingView.strokeWidth = 15f
                    drawingView.setEraserMode(false)
                    
                    // Simulate drawing stroke
                    val events = TestUtils.simulateDrawingStroke(
                        drawingView, 10f, 10f, 100f, 100f, 20
                    )
                    
                    events.forEach { event ->
                        drawingView.onTouchEvent(event)
                    }
                }
            }
        }
    }

    @Test
    fun benchmarkToolSwitchingPerformance() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                benchmarkRule.measureRepeated {
                    // Rapid tool switching
                    drawingView.setEraserMode(false)
                    drawingView.setStrokeColor(Color.RED)
                    drawingView.strokeWidth = 10f
                    
                    drawingView.setEraserMode(true)
                    drawingView.eraserWidth = 20f
                    
                    drawingView.setEraserMode(false)
                    drawingView.setPencilView(true)
                    
                    drawingView.setPencilView(false)
                    drawingView.setStrokeColor(Color.GREEN)
                }
            }
        }
    }

    @Test
    fun benchmarkUndoRedoPerformance() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                // Setup some drawing operations first
                repeat(10) { i ->
                    drawingView.setStrokeColor(Color.rgb(i * 25, (i * 35) % 255, (i * 45) % 255))
                    drawingView.strokeWidth = 10f + (i % 10)
                    
                    val events = TestUtils.simulateDrawingStroke(
                        drawingView, 
                        i * 10f, i * 10f, 
                        (i + 1) * 10f, (i + 1) * 10f, 
                        5
                    )
                    events.forEach { event -> drawingView.onTouchEvent(event) }
                }
                
                benchmarkRule.measureRepeated {
                    // Benchmark undo/redo operations
                    repeat(5) { drawingView.undo() }
                    repeat(3) { drawingView.redo() }
                    repeat(2) { drawingView.undo() }
                }
            }
        }
    }

    @Test
    fun testMemoryUsageDuringIntensiveDrawing() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                // Measure initial memory
                val initialMemory = TestUtils.measureMemoryUsage()
                
                // Perform intensive drawing operations
                repeat(100) { i ->
                    drawingView.setStrokeColor(Color.rgb(
                        (i * 7) % 255,
                        (i * 11) % 255,
                        (i * 13) % 255
                    ))
                    drawingView.strokeWidth = 5f + (i % 20)
                    drawingView.setEraserMode(i % 5 == 0)
                    
                    // Create complex drawing pattern
                    val points = TestUtils.createComplexDrawingPattern()
                    points.take(30).forEachIndexed { index, (x, y) ->
                        val action = when (index) {
                            0 -> android.view.MotionEvent.ACTION_DOWN
                            29 -> android.view.MotionEvent.ACTION_UP
                            else -> android.view.MotionEvent.ACTION_MOVE
                        }
                        val event = TestUtils.createMockMotionEvent(action, x + (i % 50), y + (i % 50))
                        drawingView.onTouchEvent(event)
                    }
                    
                    // Perform some undo/redo operations
                    if (i % 10 == 0) {
                        repeat(3) { drawingView.undo() }
                        repeat(2) { drawingView.redo() }
                    }
                }
                
                // Measure final memory
                val finalMemory = TestUtils.measureMemoryUsage()
                val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
                
                // Memory increase should be reasonable (under 50MB for intensive operations)
                val maxAllowedIncrease = 50 * 1024 * 1024 // 50MB
                assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
                
                // Available memory should still be sufficient
                assertThat(finalMemory.getAvailableMemoryMB()).isGreaterThan(20f) // At least 20MB available
            }
        }
    }

    @Test
    fun testMemoryLeakDetection() {
        // Test for memory leaks by creating and destroying activities
        repeat(5) {
            val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
                putExtra(CommonConstants.KeyBGImageType, true)
                putExtra(CommonConstants.KeyItemPos, 0)
            }

            ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
                scenario.onActivity { activity ->
                    val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                    
                    // Perform some operations
                    repeat(20) { i ->
                        drawingView.setStrokeColor(Color.rgb(i * 12, (i * 18) % 255, (i * 24) % 255))
                        drawingView.strokeWidth = 8f + (i % 12)
                        
                        val events = TestUtils.simulateDrawingStroke(
                            drawingView, 
                            (i * 5f) % 100f, (i * 7f) % 100f,
                            ((i + 3) * 5f) % 100f, ((i + 3) * 7f) % 100f,
                            8
                        )
                        events.forEach { event -> drawingView.onTouchEvent(event) }
                    }
                    
                    // Cleanup
                    drawingView.cleanup()
                }
            }
            
            // Force garbage collection between iterations
            System.gc()
            Thread.sleep(100)
        }
        
        // After multiple activity cycles, memory should be stable
        val finalMemory = TestUtils.measureMemoryUsage()
        assertThat(finalMemory.getUsedMemoryMB()).isLessThan(100f) // Should not exceed 100MB
    }

    @Test
    fun testLargeCanvasPerformance() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, false) // Hard mode (larger canvas)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                val startTime = System.currentTimeMillis()
                
                // Test performance on larger canvas
                repeat(50) { i ->
                    drawingView.setStrokeColor(Color.rgb((i * 5) % 255, (i * 7) % 255, (i * 11) % 255))
                    drawingView.strokeWidth = 10f + (i % 15)
                    
                    // Draw across larger area
                    val events = TestUtils.simulateDrawingStroke(
                        drawingView,
                        (i * 15f) % 300f, (i * 20f) % 400f,
                        ((i + 10) * 15f) % 300f, ((i + 10) * 20f) % 400f,
                        15
                    )
                    events.forEach { event -> drawingView.onTouchEvent(event) }
                }
                
                val executionTime = System.currentTimeMillis() - startTime
                
                // Performance should be acceptable even on large canvas (under 3 seconds)
                assertThat(executionTime).isLessThan(3000L)
            }
        }
    }

    @Test
    fun testConcurrentOperationsStability() {
        val intent = Intent(ApplicationProvider.getApplicationContext(), EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true)
            putExtra(CommonConstants.KeyItemPos, 0)
        }

        ActivityScenario.launch<EditorActivity>(intent).use { scenario ->
            scenario.onActivity { activity ->
                val drawingView = activity.findViewById<WTDrawingView>(drawing.jaraappskids.kidsdrawing.R.id.drawing_view)
                
                // Simulate concurrent-like operations (rapid switching and drawing)
                repeat(100) { i ->
                    // Rapid tool configuration changes
                    drawingView.setStrokeColor(Color.rgb((i * 3) % 255, (i * 5) % 255, (i * 7) % 255))
                    drawingView.strokeWidth = 5f + (i % 20)
                    drawingView.setEraserMode(i % 3 == 0)
                    
                    if (i % 4 == 0) {
                        drawingView.setPencilView(true)
                    } else {
                        drawingView.setPencilView(false)
                    }
                    
                    // Quick drawing operation
                    val event1 = TestUtils.createMockMotionEvent(
                        android.view.MotionEvent.ACTION_DOWN, 
                        (i * 2f) % 150f, (i * 3f) % 150f
                    )
                    val event2 = TestUtils.createMockMotionEvent(
                        android.view.MotionEvent.ACTION_UP, 
                        ((i + 5) * 2f) % 150f, ((i + 5) * 3f) % 150f
                    )
                    
                    drawingView.onTouchEvent(event1)
                    drawingView.onTouchEvent(event2)
                    
                    // Occasional undo/redo
                    if (i % 10 == 0) {
                        drawingView.undo()
                    }
                    if (i % 15 == 0) {
                        drawingView.redo()
                    }
                }
                
                // System should remain stable
                assertThat(drawingView.isEnabled).isTrue()
            }
        }
    }
}
