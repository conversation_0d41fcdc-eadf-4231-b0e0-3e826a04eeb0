package drawing.jaraappskids.kidsdrawing.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u0012\u0010\r\u001a\u00020\n2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0016J&\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u00152\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0016J\b\u0010\u0016\u001a\u00020\nH\u0016R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u0005\u001a\u0016\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006j\n\u0012\u0004\u0012\u00020\u0007\u0018\u0001`\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/fragments/StickerCategoryFragment;", "Landroidx/fragment/app/Fragment;", "()V", "adapterItemTypeCallback", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemTypeCallback;", "stickerList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/StickerClass;", "Lkotlin/collections/ArrayList;", "onAttach", "", "context", "Landroid/content/Context;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDetach", "Companion", "app_debug"})
public final class StickerCategoryFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.Nullable()
    private java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.StickerClass> stickerList;
    @org.jetbrains.annotations.Nullable()
    private drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemTypeCallback adapterItemTypeCallback;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ARG_STICKER_LIST = "sticker_list";
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.fragments.StickerCategoryFragment.Companion Companion = null;
    
    public StickerCategoryFragment() {
        super();
    }
    
    @java.lang.Override()
    public void onAttach(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onDetach() {
    }
    
    @kotlin.jvm.JvmStatic()
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.fragments.StickerCategoryFragment newInstance(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.StickerClass> stickerList) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\u0005\u001a\u00020\u00062\u0016\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\t0\bj\b\u0012\u0004\u0012\u00020\t`\nH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/fragments/StickerCategoryFragment$Companion;", "", "()V", "ARG_STICKER_LIST", "", "newInstance", "Ldrawing/jaraappskids/kidsdrawing/fragments/StickerCategoryFragment;", "stickerList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/StickerClass;", "Lkotlin/collections/ArrayList;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @kotlin.jvm.JvmStatic()
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.fragments.StickerCategoryFragment newInstance(@org.jetbrains.annotations.NotNull()
        java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.StickerClass> stickerList) {
            return null;
        }
    }
}