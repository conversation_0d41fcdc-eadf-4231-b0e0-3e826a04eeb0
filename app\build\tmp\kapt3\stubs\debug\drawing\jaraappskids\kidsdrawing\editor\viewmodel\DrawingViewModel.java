package drawing.jaraappskids.kidsdrawing.editor.viewmodel;

/**
 * ViewModel for the modern drawing editor
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u009e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019J\u000e\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001d\u001a\u00020\u00172\u0006\u0010\u001e\u001a\u00020\u001fJ\u000e\u0010 \u001a\u00020\u00172\u0006\u0010!\u001a\u00020\"J\u000e\u0010#\u001a\u00020\u00172\u0006\u0010$\u001a\u00020%J\u0006\u0010&\u001a\u00020\'J\u0006\u0010(\u001a\u00020\'J\u0006\u0010)\u001a\u00020\u0017J\u0010\u0010*\u001a\u00020\u00172\u0006\u0010+\u001a\u00020\u0007H\u0002J\u0006\u0010,\u001a\u00020-J\u0006\u0010.\u001a\u00020\u0017J\u0006\u0010/\u001a\u00020\u0017J\u0006\u00100\u001a\u00020\u0017J\u0006\u00101\u001a\u00020\u0017J\u000e\u00102\u001a\u00020\u00172\u0006\u00103\u001a\u00020\u0019J\u0018\u00104\u001a\u00020\u00172\u0006\u00105\u001a\u00020\u00192\b\b\u0002\u00106\u001a\u000207J\u000e\u00108\u001a\u00020\u00172\u0006\u00103\u001a\u00020\u0019J\u000e\u00109\u001a\u00020\u00172\u0006\u0010:\u001a\u00020;J\u000e\u0010<\u001a\u00020\u00172\u0006\u0010=\u001a\u00020>J\u000e\u0010?\u001a\u00020\u00172\u0006\u0010@\u001a\u00020>J\u000e\u0010A\u001a\u00020\u00172\u0006\u0010B\u001a\u00020CJ\u000e\u0010D\u001a\u00020\u00172\u0006\u0010E\u001a\u00020\'J\u000e\u0010F\u001a\u00020\u00172\u0006\u0010G\u001a\u00020HJ\u0006\u0010I\u001a\u00020\u0017J\u0006\u0010J\u001a\u00020\u0017J\u0006\u0010K\u001a\u00020\u0017J\u0006\u0010L\u001a\u00020\u0017R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000f\u00a8\u0006M"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/viewmodel/DrawingViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Ldrawing/jaraappskids/kidsdrawing/editor/repository/DrawingRepository;", "(Ldrawing/jaraappskids/kidsdrawing/editor/repository/DrawingRepository;)V", "_events", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "Ldrawing/jaraappskids/kidsdrawing/editor/viewmodel/DrawingEvent;", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Ldrawing/jaraappskids/kidsdrawing/editor/viewmodel/DrawingUiState;", "drawingState", "Lkotlinx/coroutines/flow/StateFlow;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingState;", "getDrawingState", "()Lkotlinx/coroutines/flow/StateFlow;", "events", "Lkotlinx/coroutines/flow/SharedFlow;", "getEvents", "()Lkotlinx/coroutines/flow/SharedFlow;", "uiState", "getUiState", "addLayer", "", "name", "", "addShape", "shape", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingShape;", "addSticker", "sticker", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingSticker;", "addStroke", "stroke", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "addText", "text", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingText;", "canRedo", "", "canUndo", "clearCanvas", "emitEvent", "event", "getPerformanceStats", "Ldrawing/jaraappskids/kidsdrawing/editor/viewmodel/PerformanceStats;", "hideBrushSettings", "hideColorPicker", "hideLayerPanel", "redo", "removeLayer", "layerId", "saveDrawing", "filename", "format", "Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;", "setActiveLayer", "setBrushColor", "color", "", "setBrushOpacity", "opacity", "", "setBrushSize", "size", "setBrushType", "type", "Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushType;", "setDrawingState", "isDrawing", "setSelectedTool", "tool", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "showBrushSettings", "showColorPicker", "showLayerPanel", "undo", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class DrawingViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.data.DrawingState> drawingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEvent> _events = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.SharedFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEvent> events = null;
    
    @javax.inject.Inject()
    public DrawingViewModel(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.data.DrawingState> getDrawingState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.SharedFlow<drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEvent> getEvents() {
        return null;
    }
    
    public final void setBrushType(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BrushType type) {
    }
    
    public final void setBrushColor(int color) {
    }
    
    public final void setBrushSize(float size) {
    }
    
    public final void setBrushOpacity(float opacity) {
    }
    
    public final void setSelectedTool(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool tool) {
    }
    
    public final void addStroke(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke) {
    }
    
    public final void undo() {
    }
    
    public final void redo() {
    }
    
    public final boolean canUndo() {
        return false;
    }
    
    public final boolean canRedo() {
        return false;
    }
    
    public final void clearCanvas() {
    }
    
    public final void addLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    public final void removeLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String layerId) {
    }
    
    public final void setActiveLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String layerId) {
    }
    
    public final void saveDrawing(@org.jetbrains.annotations.NotNull()
    java.lang.String filename, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat format) {
    }
    
    public final void showColorPicker() {
    }
    
    public final void hideColorPicker() {
    }
    
    public final void showBrushSettings() {
    }
    
    public final void hideBrushSettings() {
    }
    
    public final void showLayerPanel() {
    }
    
    public final void hideLayerPanel() {
    }
    
    public final void setDrawingState(boolean isDrawing) {
    }
    
    private final void emitEvent(drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEvent event) {
    }
    
    public final void addShape(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape shape) {
    }
    
    public final void addText(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingText text) {
    }
    
    public final void addSticker(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker sticker) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.viewmodel.PerformanceStats getPerformanceStats() {
        return null;
    }
}