package drawing.jaraappskids.kidsdrawing.mywork;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\u0018\u00002\u00020\u00012\u00020\u0002:\u00016B\u0005\u00a2\u0006\u0002\u0010\u0003J\u000e\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u0007J\b\u0010&\u001a\u00020\'H\u0002J\b\u0010(\u001a\u00020\'H\u0002J\u0012\u0010)\u001a\u00020\'2\b\u0010*\u001a\u0004\u0018\u00010+H\u0014J,\u0010,\u001a\u00020\'2\n\u0010-\u001a\u0006\u0012\u0002\b\u00030.2\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u0002022\u0006\u00103\u001a\u000204H\u0016J\b\u00105\u001a\u00020\'H\u0014J\b\u0010\u001c\u001a\u00020\'H\u0002R,\u0010\u0004\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u00060\u0005X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000bR\u001a\u0010\f\u001a\u00020\rX\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0012\u001a\u00020\u0013X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u001e\u0010\u0018\u001a\u00060\u0019R\u00020\u0000X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u001a\u0010\u001e\u001a\u00020\u001fX\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010!\"\u0004\b\"\u0010#\u00a8\u00067"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Landroid/widget/AdapterView$OnItemClickListener;", "()V", "arrOfAllImages", "Ljava/util/ArrayList;", "Ljava/util/HashMap;", "", "getArrOfAllImages", "()Ljava/util/ArrayList;", "setArrOfAllImages", "(Ljava/util/ArrayList;)V", "context", "Landroid/content/Context;", "getContext", "()Landroid/content/Context;", "setContext", "(Landroid/content/Context;)V", "gridOfMyWork", "Landroid/widget/GridView;", "getGridOfMyWork", "()Landroid/widget/GridView;", "setGridOfMyWork", "(Landroid/widget/GridView;)V", "gridViewAdapter", "Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity$GridViewAdapter;", "getGridViewAdapter", "()Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity$GridViewAdapter;", "setGridViewAdapter", "(Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity$GridViewAdapter;)V", "txtNoItem", "Landroid/widget/TextView;", "getTxtNoItem", "()Landroid/widget/TextView;", "setTxtNoItem", "(Landroid/widget/TextView;)V", "getDate", "filePath", "getImagesFromSdCard", "", "initDefine", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onItemClick", "parent", "Landroid/widget/AdapterView;", "view", "Landroid/view/View;", "position", "", "id", "", "onResume", "GridViewAdapter", "app_debug"})
public final class MyWorkActivity extends androidx.appcompat.app.AppCompatActivity implements android.widget.AdapterView.OnItemClickListener {
    public android.content.Context context;
    public android.widget.TextView txtNoItem;
    public java.util.ArrayList<java.util.HashMap<java.lang.String, java.lang.String>> arrOfAllImages;
    public android.widget.GridView gridOfMyWork;
    public drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity.GridViewAdapter gridViewAdapter;
    
    public MyWorkActivity() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext() {
        return null;
    }
    
    public final void setContext(@org.jetbrains.annotations.NotNull()
    android.content.Context p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.widget.TextView getTxtNoItem() {
        return null;
    }
    
    public final void setTxtNoItem(@org.jetbrains.annotations.NotNull()
    android.widget.TextView p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<java.util.HashMap<java.lang.String, java.lang.String>> getArrOfAllImages() {
        return null;
    }
    
    public final void setArrOfAllImages(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.HashMap<java.lang.String, java.lang.String>> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.widget.GridView getGridOfMyWork() {
        return null;
    }
    
    public final void setGridOfMyWork(@org.jetbrains.annotations.NotNull()
    android.widget.GridView p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity.GridViewAdapter getGridViewAdapter() {
        return null;
    }
    
    public final void setGridViewAdapter(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity.GridViewAdapter p0) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initDefine() {
    }
    
    private final void getImagesFromSdCard() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDate(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath) {
        return null;
    }
    
    private final void setGridViewAdapter() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    @java.lang.Override()
    public void onItemClick(@org.jetbrains.annotations.NotNull()
    android.widget.AdapterView<?> parent, @org.jetbrains.annotations.NotNull()
    android.view.View view, int position, long id) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0016J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0004H\u0016J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u0004H\u0016J$\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0007\u001a\u00020\u00042\b\u0010\f\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\r\u001a\u00020\u000eH\u0016\u00a8\u0006\u000f"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity$GridViewAdapter;", "Landroid/widget/BaseAdapter;", "(Ldrawing/jaraappskids/kidsdrawing/mywork/MyWorkActivity;)V", "getCount", "", "getItem", "", "position", "getItemId", "", "getView", "Landroid/view/View;", "convertView", "parent", "Landroid/view/ViewGroup;", "app_debug"})
    public final class GridViewAdapter extends android.widget.BaseAdapter {
        
        public GridViewAdapter() {
            super();
        }
        
        @java.lang.Override()
        public int getCount() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.Object getItem(int position) {
            return null;
        }
        
        @java.lang.Override()
        public long getItemId(int position) {
            return 0L;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public android.view.View getView(int position, @org.jetbrains.annotations.Nullable()
        android.view.View convertView, @org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent) {
            return null;
        }
    }
}