package drawing.jaraappskids.kidsdrawing.pojo;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002R\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR\u001a\u0010\u000f\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u0011\"\u0004\b\u0012\u0010\u0013\u00a8\u0006\u0014"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/pojo/EditorItemClass;", "", "()V", "editorIcon", "Landroid/graphics/drawable/Drawable;", "getEditorIcon", "()Landroid/graphics/drawable/Drawable;", "setEditorIcon", "(Landroid/graphics/drawable/Drawable;)V", "editorTitle", "", "getEditorTitle", "()Ljava/lang/String;", "setEditorTitle", "(Ljava/lang/String;)V", "isSelected", "", "()Z", "setSelected", "(Z)V", "app_debug"})
public final class EditorItemClass {
    @org.jetbrains.annotations.Nullable()
    private android.graphics.drawable.Drawable editorIcon;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String editorTitle = "";
    private boolean isSelected = false;
    
    public EditorItemClass() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.drawable.Drawable getEditorIcon() {
        return null;
    }
    
    public final void setEditorIcon(@org.jetbrains.annotations.Nullable()
    android.graphics.drawable.Drawable p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEditorTitle() {
        return null;
    }
    
    public final void setEditorTitle(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
}