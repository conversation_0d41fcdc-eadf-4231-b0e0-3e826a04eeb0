<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res"><file name="isb_selector_tick_marks_color" path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\color\isb_selector_tick_marks_color.xml" qualifiers="" type="color"/><file name="isb_selector_tick_texts_color" path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\color\isb_selector_tick_texts_color.xml" qualifiers="" type="color"/><file name="isb_indicator_rounded_corners" path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\drawable\isb_indicator_rounded_corners.xml" qualifiers="" type="drawable"/><file name="isb_indicator_square_corners" path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\drawable\isb_indicator_square_corners.xml" qualifiers="" type="drawable"/><file name="isb_indicator" path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\layout\isb_indicator.xml" qualifiers="" type="layout"/><file path="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\values\attr.xml" qualifiers=""><declare-styleable name="IndicatorSeekBar">
        //seekBar
        <attr format="float" name="isb_max"/>
        <attr format="float" name="isb_min"/>
        <attr format="float" name="isb_progress"/>
        <attr format="boolean" name="isb_progress_value_float"/>
        <attr format="boolean" name="isb_seek_smoothly"/>
        <attr format="boolean" name="isb_r2l"/>
        <attr format="integer" name="isb_ticks_count"/>
        <attr format="boolean" name="isb_user_seekable"/>
        <attr format="boolean" name="isb_clear_default_padding"/>
        <attr format="boolean" name="isb_only_thumb_draggable"/>
        //indicator
        <attr name="isb_show_indicator">
            <enum name="none" value="0"/>
            <enum name="circular_bubble" value="1"/>
            <enum name="rounded_rectangle" value="2"/>
            <enum name="rectangle" value="3"/>
            <enum name="custom" value="4"/>
        </attr>
        <attr format="color|reference" name="isb_indicator_color"/>
        <attr format="color|reference" name="isb_indicator_text_color"/>
        <attr format="dimension|reference" name="isb_indicator_text_size"/>
        <attr format="reference" name="isb_indicator_content_layout"/>
        <attr format="reference" name="isb_indicator_top_content_layout"/> 
        //track
        <attr format="dimension|reference" name="isb_track_background_size"/>
        <attr format="color|reference" name="isb_track_background_color"/>
        <attr format="dimension|reference" name="isb_track_progress_size"/>
        <attr format="color|reference" name="isb_track_progress_color"/>
        <attr format="boolean" name="isb_track_rounded_corners"/>
        //thumb text
        <attr format="color|reference" name="isb_thumb_text_color"/>
        <attr format="boolean" name="isb_show_thumb_text"/>
        //thumb
        <attr format="dimension|reference" name="isb_thumb_size"/>
        <attr format="color|reference" name="isb_thumb_color"/>
        <attr format="reference" name="isb_thumb_drawable"/>
        <attr format="boolean" name="isb_thumb_adjust_auto"/>
        //tickMarks
        <attr format="color|reference" name="isb_tick_marks_color"/>
        <attr format="dimension|reference" name="isb_tick_marks_size"/>
        <attr format="reference" name="isb_tick_marks_drawable"/>
        <attr format="boolean" name="isb_tick_marks_ends_hide"/>
        <attr format="boolean" name="isb_tick_marks_swept_hide"/>
        <attr name="isb_show_tick_marks_type">
            <enum name="none" value="0"/>
            <enum name="oval" value="1"/>
            <enum name="square" value="2"/>
            <enum name="divider" value="3"/> 
        </attr>
        //tickTexts
        <attr format="boolean" name="isb_show_tick_texts"/>
        <attr format="reference|color" name="isb_tick_texts_color"/>
        <attr format="dimension|reference" name="isb_tick_texts_size"/>
        <attr format="reference" name="isb_tick_texts_array"/>
        <attr name="isb_tick_texts_typeface">
            <enum name="normal" value="0"/>
            <enum name="monospace" value="1"/>
            <enum name="sans" value="2"/>
            <enum name="serif" value="3"/>
        </attr>
    </declare-styleable></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\KidsDrawingApp\indicatorseekbar\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="IndicatorSeekBar">
        //seekBar
        <attr format="float" name="isb_max"/>
        <attr format="float" name="isb_min"/>
        <attr format="float" name="isb_progress"/>
        <attr format="boolean" name="isb_progress_value_float"/>
        <attr format="boolean" name="isb_seek_smoothly"/>
        <attr format="boolean" name="isb_r2l"/>
        <attr format="integer" name="isb_ticks_count"/>
        <attr format="boolean" name="isb_user_seekable"/>
        <attr format="boolean" name="isb_clear_default_padding"/>
        <attr format="boolean" name="isb_only_thumb_draggable"/>
        //indicator
        <attr name="isb_show_indicator">
            <enum name="none" value="0"/>
            <enum name="circular_bubble" value="1"/>
            <enum name="rounded_rectangle" value="2"/>
            <enum name="rectangle" value="3"/>
            <enum name="custom" value="4"/>
        </attr>
        <attr format="color|reference" name="isb_indicator_color"/>
        <attr format="color|reference" name="isb_indicator_text_color"/>
        <attr format="dimension|reference" name="isb_indicator_text_size"/>
        <attr format="reference" name="isb_indicator_content_layout"/>
        <attr format="reference" name="isb_indicator_top_content_layout"/> 
        //track
        <attr format="dimension|reference" name="isb_track_background_size"/>
        <attr format="color|reference" name="isb_track_background_color"/>
        <attr format="dimension|reference" name="isb_track_progress_size"/>
        <attr format="color|reference" name="isb_track_progress_color"/>
        <attr format="boolean" name="isb_track_rounded_corners"/>
        //thumb text
        <attr format="color|reference" name="isb_thumb_text_color"/>
        <attr format="boolean" name="isb_show_thumb_text"/>
        //thumb
        <attr format="dimension|reference" name="isb_thumb_size"/>
        <attr format="color|reference" name="isb_thumb_color"/>
        <attr format="reference" name="isb_thumb_drawable"/>
        <attr format="boolean" name="isb_thumb_adjust_auto"/>
        //tickMarks
        <attr format="color|reference" name="isb_tick_marks_color"/>
        <attr format="dimension|reference" name="isb_tick_marks_size"/>
        <attr format="reference" name="isb_tick_marks_drawable"/>
        <attr format="boolean" name="isb_tick_marks_ends_hide"/>
        <attr format="boolean" name="isb_tick_marks_swept_hide"/>
        <attr name="isb_show_tick_marks_type">
            <enum name="none" value="0"/>
            <enum name="oval" value="1"/>
            <enum name="square" value="2"/>
            <enum name="divider" value="3"/> 
        </attr>
        //tickTexts
        <attr format="boolean" name="isb_show_tick_texts"/>
        <attr format="reference|color" name="isb_tick_texts_color"/>
        <attr format="dimension|reference" name="isb_tick_texts_size"/>
        <attr format="reference" name="isb_tick_texts_array"/>
        <attr name="isb_tick_texts_typeface">
            <enum name="normal" value="0"/>
            <enum name="monospace" value="1"/>
            <enum name="sans" value="2"/>
            <enum name="serif" value="3"/>
        </attr>
    </declare-styleable></configuration></mergedItems></merger>