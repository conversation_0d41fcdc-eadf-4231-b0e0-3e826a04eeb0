<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_editor" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_editor.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_editor_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="727" endOffset="14"/></Target><Target id="@+id/llHeader" view="LinearLayout"><Expressions/><location startLine="14" startOffset="4" endLine="174" endOffset="18"/></Target><Target id="@+id/llBack" view="LinearLayout"><Expressions/><location startLine="25" startOffset="8" endLine="47" endOffset="22"/></Target><Target id="@+id/llRedo" view="LinearLayout"><Expressions/><location startLine="49" startOffset="8" endLine="72" endOffset="22"/></Target><Target id="@+id/ivRedo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="57" startOffset="12" endLine="61" endOffset="57"/></Target><Target id="@+id/llUndo" view="LinearLayout"><Expressions/><location startLine="74" startOffset="8" endLine="97" endOffset="22"/></Target><Target id="@+id/ivUndo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="82" startOffset="12" endLine="86" endOffset="57"/></Target><Target id="@+id/llEraser" view="LinearLayout"><Expressions/><location startLine="99" startOffset="8" endLine="122" endOffset="22"/></Target><Target id="@+id/ivEraser" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="107" startOffset="12" endLine="111" endOffset="59"/></Target><Target id="@+id/llClear" view="LinearLayout"><Expressions/><location startLine="124" startOffset="8" endLine="148" endOffset="22"/></Target><Target id="@+id/ivClear" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="133" startOffset="12" endLine="137" endOffset="58"/></Target><Target id="@+id/llSave" view="LinearLayout"><Expressions/><location startLine="150" startOffset="8" endLine="172" endOffset="22"/></Target><Target id="@+id/flMainLayout" view="RelativeLayout"><Expressions/><location startLine="176" startOffset="4" endLine="229" endOffset="20"/></Target><Target id="@+id/rlMain" view="RelativeLayout"><Expressions/><location startLine="188" startOffset="8" endLine="218" endOffset="24"/></Target><Target id="@+id/drawing_view" view="drawing.jaraappskids.kidsdrawing.drwaing.WTDrawingView"><Expressions/><location startLine="195" startOffset="12" endLine="199" endOffset="68"/></Target><Target id="@+id/drawingViewBitmap1" view="drawing.jaraappskids.kidsdrawing.magicbrush.OverlayBrushView"><Expressions/><location startLine="202" startOffset="12" endLine="207" endOffset="68"/></Target><Target id="@+id/ivBackground" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="209" startOffset="12" endLine="215" endOffset="57"/></Target><Target id="@+id/sticker_view" view="drawing.jaraappskids.kidsdrawing.sticker.StickerView"><Expressions/><location startLine="220" startOffset="8" endLine="226" endOffset="37"/></Target><Target id="@+id/llFooter" view="LinearLayout"><Expressions/><location startLine="231" startOffset="4" endLine="708" endOffset="18"/></Target><Target id="@+id/llButton" view="LinearLayout"><Expressions/><location startLine="244" startOffset="12" endLine="373" endOffset="26"/></Target><Target id="@+id/llBrush" view="LinearLayout"><Expressions/><location startLine="253" startOffset="16" endLine="275" endOffset="30"/></Target><Target id="@+id/llMagicBrush" view="LinearLayout"><Expressions/><location startLine="277" startOffset="16" endLine="299" endOffset="30"/></Target><Target id="@+id/llPencil" view="LinearLayout"><Expressions/><location startLine="301" startOffset="16" endLine="323" endOffset="30"/></Target><Target id="@+id/llSticker" view="LinearLayout"><Expressions/><location startLine="325" startOffset="16" endLine="347" endOffset="30"/></Target><Target id="@+id/llAddText" view="LinearLayout"><Expressions/><location startLine="349" startOffset="16" endLine="371" endOffset="30"/></Target><Target id="@+id/llBrushView" view="LinearLayout"><Expressions/><location startLine="375" startOffset="12" endLine="518" endOffset="26"/></Target><Target id="@+id/ivBackBrush" view="ImageView"><Expressions/><location startLine="392" startOffset="20" endLine="398" endOffset="54"/></Target><Target id="@+id/ivSettingBrush" view="ImageView"><Expressions/><location startLine="401" startOffset="20" endLine="407" endOffset="54"/></Target><Target id="@+id/rvPaintBrush" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="416" startOffset="20" endLine="421" endOffset="68"/></Target><Target id="@+id/llBrushControl" view="LinearLayout"><Expressions/><location startLine="423" startOffset="20" endLine="514" endOffset="34"/></Target><Target id="@+id/sbSmoothness" view="drawing.warkiz.widget.IndicatorSeekBar"><Expressions/><location startLine="448" startOffset="28" endLine="469" endOffset="73"/></Target><Target id="@+id/sbBrushSize" view="drawing.warkiz.widget.IndicatorSeekBar"><Expressions/><location startLine="488" startOffset="28" endLine="509" endOffset="73"/></Target><Target id="@+id/llPencilView" view="LinearLayout"><Expressions/><location startLine="520" startOffset="12" endLine="564" endOffset="26"/></Target><Target id="@+id/ivBackPencil" view="ImageView"><Expressions/><location startLine="537" startOffset="20" endLine="543" endOffset="54"/></Target><Target id="@+id/rvPencil" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="555" startOffset="20" endLine="560" endOffset="57"/></Target><Target id="@+id/llStickerView" view="LinearLayout"><Expressions/><location startLine="566" startOffset="12" endLine="606" endOffset="26"/></Target><Target id="@+id/ivBackSticker" view="ImageView"><Expressions/><location startLine="580" startOffset="20" endLine="585" endOffset="67"/></Target><Target id="@+id/tabLayoutStickerCategories" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="590" startOffset="16" endLine="598" endOffset="66"/></Target><Target id="@+id/viewPagerStickerCategories" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="600" startOffset="16" endLine="604" endOffset="48"/></Target><Target id="@+id/llMagicBrushView" view="LinearLayout"><Expressions/><location startLine="608" startOffset="12" endLine="648" endOffset="26"/></Target><Target id="@+id/ivBackMagicBrush" view="ImageView"><Expressions/><location startLine="622" startOffset="20" endLine="627" endOffset="67"/></Target><Target id="@+id/rvMGButton" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="638" startOffset="20" endLine="643" endOffset="54"/></Target><Target id="@+id/llTextView" view="LinearLayout"><Expressions/><location startLine="650" startOffset="12" endLine="703" endOffset="26"/></Target><Target id="@+id/ivBackTextView" view="ImageView"><Expressions/><location startLine="668" startOffset="20" endLine="674" endOffset="54"/></Target><Target id="@+id/ivFontColor" view="ImageView"><Expressions/><location startLine="677" startOffset="20" endLine="683" endOffset="54"/></Target><Target id="@+id/rvFontStyle" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="693" startOffset="20" endLine="698" endOffset="54"/></Target><Target id="@+id/llAdView" view="RelativeLayout"><Expressions/><location startLine="712" startOffset="4" endLine="718" endOffset="42"/></Target><Target id="@+id/llAdViewFacebook" view="LinearLayout"><Expressions/><location startLine="720" startOffset="4" endLine="725" endOffset="42"/></Target></Targets></Layout>