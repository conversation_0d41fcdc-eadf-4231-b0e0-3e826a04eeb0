<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.2" type="incidents">

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Indicator&quot;, should use `@string` resource">
        <location
            file="${:indicatorseekbar*debug*MAIN*sourceProvider*0*resDir*0}/layout/isb_indicator.xml"
            line="24"
            column="13"
            startOffset="847"
            endLine="24"
            endColumn="37"
            endOffset="871"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:indicatorseekbar*debug*MAIN*sourceProvider*0*javaDir*0}/drawing/warkiz/widget/Indicator.java"
            line="77"
            column="66"
            startOffset="2945"
            endLine="77"
            endColumn="79"
            endOffset="2958"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:indicatorseekbar*debug*MAIN*sourceProvider*0*javaDir*0}/drawing/warkiz/widget/Indicator.java"
            line="120"
            column="70"
            startOffset="5822"
            endLine="120"
            endColumn="83"
            endOffset="5835"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:indicatorseekbar*projectDir}/build.gradle"
            line="35"
            column="9"
            startOffset="1052"
            endLine="35"
            endColumn="28"
            endOffset="1071"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.support:appcompat-v7 than 27.1.1 is available: 28.0.0">
        <fix-replace
            description="Change to 28.0.0"
            family="Update versions"
            oldString="27.1.1"
            replacement="28.0.0"
            priority="0"/>
        <location
            file="${:indicatorseekbar*projectDir}/build.gradle"
            line="48"
            column="17"
            startOffset="1345"
            endLine="48"
            endColumn="58"
            endOffset="1386"/>
    </incident>

</incidents>
