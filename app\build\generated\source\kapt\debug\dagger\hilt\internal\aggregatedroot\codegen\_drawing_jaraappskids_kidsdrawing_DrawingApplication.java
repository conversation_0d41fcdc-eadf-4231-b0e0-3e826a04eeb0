package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "drawing.jaraappskids.kidsdrawing.DrawingApplication",
    rootPackage = "drawing.jaraappskids.kidsdrawing",
    originatingRoot = "drawing.jaraappskids.kidsdrawing.DrawingApplication",
    originatingRootPackage = "drawing.jaraappskids.kidsdrawing",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "DrawingApplication",
    originatingRootSimpleNames = "DrawingApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _drawing_jaraappskids_kidsdrawing_DrawingApplication {
}
