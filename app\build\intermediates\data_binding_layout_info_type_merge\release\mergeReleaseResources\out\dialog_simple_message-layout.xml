<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_simple_message" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\dialog_simple_message.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_simple_message_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="37" endOffset="14"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="44"/></Target><Target id="@+id/tv_message" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="44"/></Target><Target id="@+id/btn_ok" view="Button"><Expressions/><location startLine="29" startOffset="4" endLine="35" endOffset="32"/></Target></Targets></Layout>