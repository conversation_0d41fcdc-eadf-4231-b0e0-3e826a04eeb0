// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final RelativeLayout btnAdFree;

  @NonNull
  public final CardView cardLetsPlay;

  @NonNull
  public final CardView cardMyCreation;

  @NonNull
  public final CardView cardRate;

  @NonNull
  public final CardView cardShare;

  @NonNull
  public final ImageView ivCloud1;

  @NonNull
  public final ImageView ivCloud2;

  @NonNull
  public final RelativeLayout ivLetsPlay;

  @NonNull
  public final RelativeLayout ivMyCreation;

  @NonNull
  public final RelativeLayout ivRate;

  @NonNull
  public final RelativeLayout ivShare;

  @NonNull
  public final ImageView ivStar1;

  @NonNull
  public final ImageView ivStar2;

  @NonNull
  public final ImageView ivStar3;

  @NonNull
  public final ImageView ivStar4;

  @NonNull
  public final RelativeLayout llAdView;

  @NonNull
  public final TextView tvAppTitle1;

  @NonNull
  public final TextView tvAppTitle2;

  @NonNull
  public final TextView tvEmojiDecoration;

  private ActivityMainBinding(@NonNull RelativeLayout rootView, @NonNull RelativeLayout btnAdFree,
      @NonNull CardView cardLetsPlay, @NonNull CardView cardMyCreation, @NonNull CardView cardRate,
      @NonNull CardView cardShare, @NonNull ImageView ivCloud1, @NonNull ImageView ivCloud2,
      @NonNull RelativeLayout ivLetsPlay, @NonNull RelativeLayout ivMyCreation,
      @NonNull RelativeLayout ivRate, @NonNull RelativeLayout ivShare, @NonNull ImageView ivStar1,
      @NonNull ImageView ivStar2, @NonNull ImageView ivStar3, @NonNull ImageView ivStar4,
      @NonNull RelativeLayout llAdView, @NonNull TextView tvAppTitle1,
      @NonNull TextView tvAppTitle2, @NonNull TextView tvEmojiDecoration) {
    this.rootView = rootView;
    this.btnAdFree = btnAdFree;
    this.cardLetsPlay = cardLetsPlay;
    this.cardMyCreation = cardMyCreation;
    this.cardRate = cardRate;
    this.cardShare = cardShare;
    this.ivCloud1 = ivCloud1;
    this.ivCloud2 = ivCloud2;
    this.ivLetsPlay = ivLetsPlay;
    this.ivMyCreation = ivMyCreation;
    this.ivRate = ivRate;
    this.ivShare = ivShare;
    this.ivStar1 = ivStar1;
    this.ivStar2 = ivStar2;
    this.ivStar3 = ivStar3;
    this.ivStar4 = ivStar4;
    this.llAdView = llAdView;
    this.tvAppTitle1 = tvAppTitle1;
    this.tvAppTitle2 = tvAppTitle2;
    this.tvEmojiDecoration = tvEmojiDecoration;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAdFree;
      RelativeLayout btnAdFree = ViewBindings.findChildViewById(rootView, id);
      if (btnAdFree == null) {
        break missingId;
      }

      id = R.id.cardLetsPlay;
      CardView cardLetsPlay = ViewBindings.findChildViewById(rootView, id);
      if (cardLetsPlay == null) {
        break missingId;
      }

      id = R.id.cardMyCreation;
      CardView cardMyCreation = ViewBindings.findChildViewById(rootView, id);
      if (cardMyCreation == null) {
        break missingId;
      }

      id = R.id.cardRate;
      CardView cardRate = ViewBindings.findChildViewById(rootView, id);
      if (cardRate == null) {
        break missingId;
      }

      id = R.id.cardShare;
      CardView cardShare = ViewBindings.findChildViewById(rootView, id);
      if (cardShare == null) {
        break missingId;
      }

      id = R.id.ivCloud1;
      ImageView ivCloud1 = ViewBindings.findChildViewById(rootView, id);
      if (ivCloud1 == null) {
        break missingId;
      }

      id = R.id.ivCloud2;
      ImageView ivCloud2 = ViewBindings.findChildViewById(rootView, id);
      if (ivCloud2 == null) {
        break missingId;
      }

      id = R.id.ivLetsPlay;
      RelativeLayout ivLetsPlay = ViewBindings.findChildViewById(rootView, id);
      if (ivLetsPlay == null) {
        break missingId;
      }

      id = R.id.ivMyCreation;
      RelativeLayout ivMyCreation = ViewBindings.findChildViewById(rootView, id);
      if (ivMyCreation == null) {
        break missingId;
      }

      id = R.id.ivRate;
      RelativeLayout ivRate = ViewBindings.findChildViewById(rootView, id);
      if (ivRate == null) {
        break missingId;
      }

      id = R.id.ivShare;
      RelativeLayout ivShare = ViewBindings.findChildViewById(rootView, id);
      if (ivShare == null) {
        break missingId;
      }

      id = R.id.ivStar1;
      ImageView ivStar1 = ViewBindings.findChildViewById(rootView, id);
      if (ivStar1 == null) {
        break missingId;
      }

      id = R.id.ivStar2;
      ImageView ivStar2 = ViewBindings.findChildViewById(rootView, id);
      if (ivStar2 == null) {
        break missingId;
      }

      id = R.id.ivStar3;
      ImageView ivStar3 = ViewBindings.findChildViewById(rootView, id);
      if (ivStar3 == null) {
        break missingId;
      }

      id = R.id.ivStar4;
      ImageView ivStar4 = ViewBindings.findChildViewById(rootView, id);
      if (ivStar4 == null) {
        break missingId;
      }

      id = R.id.llAdView;
      RelativeLayout llAdView = ViewBindings.findChildViewById(rootView, id);
      if (llAdView == null) {
        break missingId;
      }

      id = R.id.tvAppTitle1;
      TextView tvAppTitle1 = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle1 == null) {
        break missingId;
      }

      id = R.id.tvAppTitle2;
      TextView tvAppTitle2 = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle2 == null) {
        break missingId;
      }

      id = R.id.tvEmojiDecoration;
      TextView tvEmojiDecoration = ViewBindings.findChildViewById(rootView, id);
      if (tvEmojiDecoration == null) {
        break missingId;
      }

      return new ActivityMainBinding((RelativeLayout) rootView, btnAdFree, cardLetsPlay,
          cardMyCreation, cardRate, cardShare, ivCloud1, ivCloud2, ivLetsPlay, ivMyCreation, ivRate,
          ivShare, ivStar1, ivStar2, ivStar3, ivStar4, llAdView, tvAppTitle1, tvAppTitle2,
          tvEmojiDecoration);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
