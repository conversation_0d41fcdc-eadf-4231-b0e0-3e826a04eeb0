# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.5.2"
  }
  digests {
    sha256: "+\243\312\347\345\237\207\024\272\273\025\301\201F\247\257\217\356Kx,\026%Nz\002\f\345\026\204g\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.0"
  }
  digests {
    sha256: "$\t8\304\252\270\347>\210\207\003\343\347\323\370s\203\377\345\275Smm^<\020\rL\3207\237\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.0.0"
  }
  digests {
    sha256: "\335\225\200d\371F\260\037tSe\302\357\241\036LX\022\242.&\266\312\244C? \177\265\264\245\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.8.7"
  }
  digests {
    sha256: "\024P\347.\352.\310\b\260\211\271j\275\307s\312\003\244\024>&\177z\255\331\256\271\247\303\232\376\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.14.2"
  }
  digests {
    sha256: "\3542\303?[(\237\327\260\245D\205\342s\222\370\226\2629\316\375S3\205\342b\336\0250\031\f?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.14.2"
  }
  digests {
    sha256: "\320!\356\341\254\032\003o\315\303w\266\334;!\217J\f\302\274/\tmi\264t\031\213c^\203\002"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.14.2"
  }
  digests {
    sha256: "L\260\202\0021\211\220\207\243d+I\371\273g\246\345>\201\373\254o\376\330\027b`wZO\316M"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.14.2"
  }
  digests {
    sha256: "\204\031\277&+\347\016\336\266\271X+8eF\276f\322\350e\234z\256e\375i\251\355\340,Hw"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.3"
  }
  digests {
    sha256: "\231h\024\230L\263=\220\222\020d\310g\320\254A\337\372\020\241\004\212\332e(\201\302\023&`#\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.yukuku"
    artifactId: "ambilwarna"
    version: "2.0.1"
  }
  digests {
    sha256: "\270\300Y\366\314\326\320\201\204xj`\342|>\212\024E\261\356$O\353\234H8g\223U\360\342\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.intuit.sdp"
    artifactId: "sdp-android"
    version: "1.1.0"
  }
  digests {
    sha256: "\245\205\037J\277(\324B\360\237*\222\356\361\371\232i\226\366\234:\306N\213\262\314\256`W\241\3701"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.QuadFlask"
    artifactId: "colorpicker"
    version: "0.0.13"
  }
  digests {
    sha256: "D\b6em\3151\202a\024g-\236(\034\207\232\266*\344\"\nY\347\323#\233\226\345\026D\""
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "com.google.ads.interactivemedia.v3"
    artifactId: "interactivemedia"
    version: "3.36.0"
  }
  digests {
    sha256: "\\\323^T\023\314\003\376\357F\345\234\006\326\257\261R\352\021l\306\301\265\332y\241 \004\271\254\027\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.3.6"
  }
  digests {
    sha256: "?\204\240\023\375\353\213\254\222\324\253`z\353\363\232O\371E\364XZcY`\355v\234\320%]\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.11.0-alpha02"
  }
  digests {
    sha256: ";\037^#n\221\231\2518\375\vP{\306\316q\034F\315\002\206\275@\246\330r\336@\232\267\331N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.2.0"
  }
  digests {
    sha256: "pIy\273\340\226\306\230D62\376\321\201R\257\177k\361\204\022k\312B\243\330\200\214\323\321\001\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.2"
  }
  digests {
    sha256: "t\330\030I\254\311\353N\202\323\314\224-\245Z\330S|\373s7R6\355i\260v\331\327\255\206\031"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-pal"
    version: "20.0.1"
  }
  digests {
    sha256: "\t\370\340\346_\223\277\231\277\377\354\222\345]\302\a\257\2420\236\256\177#\355\236\345!6#/\264\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.tv"
    artifactId: "tv-ads"
    version: "1.0.0"
  }
  digests {
    sha256: "s6\252\204\241,\222\363a\226\314\350\325\027\017\313\034\232\235\3552\246\370\232\264 HiV\220\347\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.0.0"
  }
  digests {
    sha256: "Ni\203\300p;5}\366\361\306\316\254\261\265\337\302\305\000jx\234y\237\354\"\230\262\2653tf"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.8"
  }
  digests {
    sha256: "\341~\335\036\367\375G\\\220\272\244\343\224\"3/\'\b}4\274\264l\264\214\350j\371\245Ja."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.8"
  }
  digests {
    sha256: "\313\223S\357\027\221\256\027\t}\207\214\247\021\342Z\2342\316\311\004*\334I\260\f\255\376\341\247)\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "24.3.0"
  }
  digests {
    sha256: "\032s\370:i\324\264\247\360\325D\276!\235\\\3205\244X\333\240\315\270\364[gV\374\300\362\317\350"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-api"
    version: "24.3.0"
  }
  digests {
    sha256: "\340\2463\363P\003\020;Y\'\375q\206\272\260H_W}>\342\210\343Q\vV\307\236V\276V\206"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.1"
  }
  digests {
    sha256: "\033\347d\206\270\224\257\027\224b\224\0329\312\262B\260\rr5\303\256\264\327V\364Rb\260\302\344\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.5.0"
  }
  digests {
    sha256: "\020\367m\365b\"SL{\370|\230d=j\017\333~\2354#\226\246y\303\025\341\0206\260E\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.0"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\230\202\204\030w\244C\177 \002\355\2355?uzTR\341;J\260\353\361\225\306<\317\016.\bG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.0"
  }
  digests {
    sha256: "\323\323~$\003\305#\245\316\341\230;\'\246\336z\330\334\273P/Dl?v\374\245\016\327<\345b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.0"
  }
  digests {
    sha256: "\213[\323\254\357\001\352x\032\205E\275\367\020\261\300a\202Avi\241\246\214\320\256JSl\332\350\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "\217L\305:\301\207\226e\261M/\027\255O\274\263\004ZKtwBP\271\230)\261\301?{w("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "20.1.2"
  }
  digests {
    sha256: "\315\221\rE\276~\276;p\311\244\301l\233\356e\350t\201\025\355@\0226C\230O5\351\033_8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "20.1.2"
  }
  digests {
    sha256: "\356\224}z\017\342,Z2\357l\233oz\020z\352j\320pDu\207N\326is\bC\034{\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\207*^>\a\300\270\322\034\267wi\253/F\213 \232\266\354\b#\030t@\032\354\376\243\004\250\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.0.0"
  }
  digests {
    sha256: "\327T\0027NToa\333\a\250F|\254u7\265\235\"\340\206\017\376\3557\f\342\241\256\357\272!"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.0.0"
  }
  digests {
    sha256: "x|g\341G\366o\234\'Bg\257p\aP\334\303eG!\344\r\027B2\267\346\364\304{\276\234"
  }
  repo_index {
    value: 1
  }
}
library {
  digests {
    sha256: "\274\v\004\210,&B\232H\335\231\001\312\201\341\027M\0341\354\346Da\317\2404x\203\2376\ro"
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 12
  library_dep_index: 58
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 1
  library_dep_index: 46
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 43
  library_dep_index: 57
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 46
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 45
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 40
}
library_dependencies {
  library_index: 13
  library_dep_index: 3
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
  library_dep_index: 15
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 43
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 36
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 4
  library_dep_index: 25
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 6
}
library_dependencies {
  library_index: 27
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 28
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 36
}
library_dependencies {
  library_index: 29
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 39
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 35
  library_dep_index: 29
  library_dep_index: 38
  library_dep_index: 36
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 36
}
library_dependencies {
  library_index: 35
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 38
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 36
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 12
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 31
  library_dep_index: 15
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 11
}
library_dependencies {
  library_index: 45
  library_dep_index: 10
  library_dep_index: 40
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 3
  library_dep_index: 10
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 9
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 48
  library_dep_index: 47
  library_dep_index: 16
  library_dep_index: 11
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
}
library_dependencies {
  library_index: 50
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 11
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 52
  library_dep_index: 52
}
library_dependencies {
  library_index: 54
  library_dep_index: 10
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 55
  library_dep_index: 41
  library_dep_index: 56
  library_dep_index: 3
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 33
}
library_dependencies {
  library_index: 56
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 51
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 15
}
library_dependencies {
  library_index: 60
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 61
  library_dep_index: 12
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 54
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 63
  library_dep_index: 55
  library_dep_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 62
  library_dep_index: 51
  library_dep_index: 56
  library_dep_index: 67
  library_dep_index: 50
  library_dep_index: 68
  library_dep_index: 16
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 49
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 51
  library_dep_index: 11
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
  library_dep_index: 51
  library_dep_index: 12
  library_dep_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 69
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 62
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 16
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
  library_dep_index: 12
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 10
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 77
  library_dep_index: 67
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 50
  library_dep_index: 71
  library_dep_index: 13
  library_dep_index: 54
  library_dep_index: 17
  library_dep_index: 78
  library_dep_index: 57
  library_dep_index: 70
  library_dep_index: 47
  library_dep_index: 80
}
library_dependencies {
  library_index: 75
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
}
library_dependencies {
  library_index: 78
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 51
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 79
  library_dep_index: 40
  library_dep_index: 3
}
library_dependencies {
  library_index: 80
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 54
  library_dep_index: 78
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 54
  library_dep_index: 48
  library_dep_index: 85
  library_dep_index: 32
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
}
library_dependencies {
  library_index: 85
  library_dep_index: 1
}
library_dependencies {
  library_index: 88
  library_dep_index: 9
}
library_dependencies {
  library_index: 89
  library_dep_index: 1
  library_dep_index: 59
  library_dep_index: 90
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 98
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 99
  library_dep_index: 97
  library_dep_index: 100
}
library_dependencies {
  library_index: 90
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 12
  library_dep_index: 45
  library_dep_index: 91
  library_dep_index: 78
  library_dep_index: 68
  library_dep_index: 11
}
library_dependencies {
  library_index: 91
  library_dep_index: 54
  library_dep_index: 45
  library_dep_index: 40
  library_dep_index: 92
  library_dep_index: 29
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 3
}
library_dependencies {
  library_index: 92
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 93
  library_dep_index: 1
  library_dep_index: 12
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 95
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 54
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 96
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 54
}
library_dependencies {
  library_index: 97
  library_dep_index: 96
}
library_dependencies {
  library_index: 98
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 99
  library_dep_index: 94
  library_dep_index: 98
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 100
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 54
  library_dep_index: 81
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 96
  library_dep_index: 99
  library_dep_index: 97
}
library_dependencies {
  library_index: 101
  library_dep_index: 1
}
library_dependencies {
  library_index: 102
  library_dep_index: 1
  library_dep_index: 101
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 107
}
library_dependencies {
  library_index: 103
  library_dep_index: 1
  library_dep_index: 101
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 104
  library_dep_index: 1
}
library_dependencies {
  library_index: 105
  library_dep_index: 1
  library_dep_index: 104
}
library_dependencies {
  library_index: 107
  library_dep_index: 1
  library_dep_index: 104
}
library_dependencies {
  library_index: 108
  library_dep_index: 59
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 93
  library_dep_index: 116
  library_dep_index: 94
  library_dep_index: 98
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 15
}
library_dependencies {
  library_index: 109
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 110
}
library_dependencies {
  library_index: 110
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 40
  library_dep_index: 109
  library_dep_index: 111
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 109
}
library_dependencies {
  library_index: 111
  library_dep_index: 112
  library_dep_index: 15
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 76
  library_dep_index: 115
}
library_dependencies {
  library_index: 116
  library_dep_index: 59
  library_dep_index: 117
  library_dep_index: 96
  library_dep_index: 124
  library_dep_index: 126
}
library_dependencies {
  library_index: 117
  library_dep_index: 13
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 35
  library_dep_index: 118
  library_dep_index: 122
  library_dep_index: 31
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 123
}
library_dependencies {
  library_index: 118
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 3
  library_dep_index: 26
}
library_dependencies {
  library_index: 119
  library_dep_index: 1
  library_dep_index: 6
}
library_dependencies {
  library_index: 120
  library_dep_index: 13
  library_dep_index: 20
  library_dep_index: 119
  library_dep_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 121
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 122
  library_dep_index: 1
  library_dep_index: 121
  library_dep_index: 3
}
library_dependencies {
  library_index: 123
  library_dep_index: 117
  library_dep_index: 117
}
library_dependencies {
  library_index: 124
  library_dep_index: 96
  library_dep_index: 125
}
library_dependencies {
  library_index: 125
  library_dep_index: 96
}
library_dependencies {
  library_index: 126
  library_dep_index: 1
  library_dep_index: 96
}
library_dependencies {
  library_index: 127
  library_dep_index: 90
  library_dep_index: 40
  library_dep_index: 91
  library_dep_index: 3
}
library_dependencies {
  library_index: 128
  library_dep_index: 3
  library_dep_index: 129
}
library_dependencies {
  library_index: 129
  library_dep_index: 3
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 8
  dependency_index: 59
  dependency_index: 60
  dependency_index: 61
  dependency_index: 9
  dependency_index: 74
  dependency_index: 78
  dependency_index: 77
  dependency_index: 81
  dependency_index: 86
  dependency_index: 87
  dependency_index: 88
  dependency_index: 89
  dependency_index: 108
  dependency_index: 94
  dependency_index: 30
  dependency_index: 123
  dependency_index: 127
  dependency_index: 128
  dependency_index: 130
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
