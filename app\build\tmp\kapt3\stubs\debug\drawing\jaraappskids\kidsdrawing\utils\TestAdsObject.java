package drawing.jaraappskids.kidsdrawing.utils;

/**
 * Test object to verify compilation
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/utils/TestAdsObject;", "", "()V", "TAG", "", "testMethod", "", "context", "Landroid/content/Context;", "app_debug"})
public final class TestAdsObject {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "TestAdsObject";
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.utils.TestAdsObject INSTANCE = null;
    
    private TestAdsObject() {
        super();
    }
    
    public final void testMethod(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
}