package drawing.jaraappskids.kidsdrawing.editor.repository;

/**
 * Repository for managing drawing data and operations
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011J\u0018\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u000eH\u0002J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0018J\u0006\u0010\u001a\u001a\u00020\u001bJ\u0018\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u0015\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\u0018\u0010 \u001a\u00020\u001b2\u0006\u0010\u0015\u001a\u00020\u001d2\u0006\u0010!\u001a\u00020\"H\u0002J\u000e\u0010#\u001a\u00020\u001b2\u0006\u0010\u0016\u001a\u00020\u000eJ\u0006\u0010$\u001a\u00020\u0018J\u000e\u0010%\u001a\u00020\u001b2\u0006\u0010&\u001a\u00020\u0011J\u0010\u0010\'\u001a\u00020(2\u0006\u0010\u0015\u001a\u00020\u0014H\u0002J.\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00110*2\u0006\u0010+\u001a\u00020\u00112\b\b\u0002\u0010,\u001a\u00020-H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b.\u0010/J\u000e\u00100\u001a\u00020\u001b2\u0006\u0010&\u001a\u00020\u0011J\u000e\u00101\u001a\u00020\u001b2\u0006\u00102\u001a\u00020\u0018J\u000e\u00103\u001a\u00020\u001b2\u0006\u00104\u001a\u000205J\u0006\u00106\u001a\u00020\u0018J\u000e\u00107\u001a\u00020\u001b2\u0006\u00108\u001a\u000209J\u000e\u0010:\u001a\u00020\u001b2\u0006\u00108\u001a\u00020;R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006<"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/repository/DrawingRepository;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_drawingState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingState;", "drawingState", "Lkotlinx/coroutines/flow/StateFlow;", "getDrawingState", "()Lkotlinx/coroutines/flow/StateFlow;", "redoStack", "", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "undoStack", "addLayer", "", "name", "applyActionToCanvas", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;", "canvas", "action", "canRedo", "", "canUndo", "clearCanvas", "", "drawLayer", "Landroid/graphics/Canvas;", "layer", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "drawStroke", "stroke", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "executeAction", "redo", "removeLayer", "layerId", "renderCanvasToBitmap", "Landroid/graphics/Bitmap;", "saveDrawing", "Lkotlin/Result;", "filename", "format", "Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;", "saveDrawing-0E7RQCE", "(Ljava/lang/String;Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setActiveLayer", "setDrawingState", "isDrawing", "setSelectedTool", "tool", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "undo", "updateBrushSettings", "settings", "Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushSettings;", "updateCanvasSettings", "Ldrawing/jaraappskids/kidsdrawing/editor/data/CanvasSettings;", "app_debug"})
public final class DrawingRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<drawing.jaraappskids.kidsdrawing.editor.data.DrawingState> _drawingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.data.DrawingState> drawingState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> undoStack = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> redoStack = null;
    
    @javax.inject.Inject()
    public DrawingRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<drawing.jaraappskids.kidsdrawing.editor.data.DrawingState> getDrawingState() {
        return null;
    }
    
    public final void updateBrushSettings(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings settings) {
    }
    
    public final void updateCanvasSettings(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings settings) {
    }
    
    public final void setSelectedTool(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool tool) {
    }
    
    public final void setDrawingState(boolean isDrawing) {
    }
    
    public final void executeAction(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction action) {
    }
    
    public final boolean undo() {
        return false;
    }
    
    public final boolean redo() {
        return false;
    }
    
    public final boolean canUndo() {
        return false;
    }
    
    public final boolean canRedo() {
        return false;
    }
    
    private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas applyActionToCanvas(drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas, drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction action) {
        return null;
    }
    
    private final android.graphics.Bitmap renderCanvasToBitmap(drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas) {
        return null;
    }
    
    private final void drawLayer(android.graphics.Canvas canvas, drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
    }
    
    private final void drawStroke(android.graphics.Canvas canvas, drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke) {
    }
    
    public final void clearCanvas() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String addLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
        return null;
    }
    
    public final void removeLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String layerId) {
    }
    
    public final void setActiveLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String layerId) {
    }
}