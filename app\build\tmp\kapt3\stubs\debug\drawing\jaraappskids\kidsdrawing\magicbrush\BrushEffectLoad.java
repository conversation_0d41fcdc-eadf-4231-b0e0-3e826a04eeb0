package drawing.jaraappskids.kidsdrawing.magicbrush;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0018\u0018\u00002\u00020\u0001:\u0001GB\u0005\u00a2\u0006\u0002\u0010\u0002J\r\u0010%\u001a\u00020\nH\u0000\u00a2\u0006\u0002\b&J\r\u0010\'\u001a\u00020\nH\u0000\u00a2\u0006\u0002\b(J\u0006\u0010)\u001a\u00020*J\u0016\u0010+\u001a\u00020*2\u0006\u0010,\u001a\u00020\u00132\u0006\u0010-\u001a\u00020 J>\u0010.\u001a\u00020*2\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u00020\u001b2\u0006\u00102\u001a\u00020\u001b2\u0006\u00103\u001a\u00020\n2\u0006\u00104\u001a\u00020\n2\u0006\u00105\u001a\u00020\n2\u0006\u00106\u001a\u00020\nJ\u0015\u00107\u001a\u00020*2\u0006\u00103\u001a\u00020\nH\u0000\u00a2\u0006\u0002\b8J\u0015\u00109\u001a\u00020*2\u0006\u0010\u0011\u001a\u00020\nH\u0000\u00a2\u0006\u0002\b:J\u000e\u0010;\u001a\u00020*2\u0006\u0010<\u001a\u00020\u0013J\u0015\u0010=\u001a\u00020*2\u0006\u0010\u000f\u001a\u00020\u0010H\u0000\u00a2\u0006\u0002\b>J\u0015\u0010?\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020 H\u0000\u00a2\u0006\u0002\b@J\u0015\u0010A\u001a\u00020*2\u0006\u0010!\u001a\u00020\"H\u0000\u00a2\u0006\u0002\bBJ\u0015\u0010C\u001a\u00020*2\u0006\u0010#\u001a\u00020\nH\u0000\u00a2\u0006\u0002\bDJ\u0015\u0010E\u001a\u00020*2\u0006\u0010$\u001a\u00020\u001bH\u0000\u00a2\u0006\u0002\bFR\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u000eR\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0012\u001a\u00020\u0013X\u0084.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u000e\u0010\u0018\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006H"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad;", "", "()V", "aBitmap", "Landroid/graphics/Bitmap;", "getABitmap$app_debug", "()Landroid/graphics/Bitmap;", "setABitmap$app_debug", "(Landroid/graphics/Bitmap;)V", "alpha", "", "bitmap", "bitmaps", "", "[Landroid/graphics/Bitmap;", "brushEffectFromResource", "Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad$BrushEffectFromResource;", "column", "contextVal", "Landroid/content/Context;", "getContextVal", "()Landroid/content/Context;", "setContextVal", "(Landroid/content/Context;)V", "deviceHeight", "deviceWidth", "f1440b", "", "f1449k", "Landroid/graphics/Paint;", "f1452n", "path", "", "rotate", "", "row", "unknown", "GetColumn", "GetColumn$app_debug", "GetRow", "GetRow$app_debug", "m2413a", "", "m2416a", "context", "pattern", "onCanvas", "canvas", "Landroid/graphics/Canvas;", "f", "f2", "i", "i2", "i3", "i4", "setAlpha", "setAlpha$app_debug", "setColumn", "setColumn$app_debug", "setContext", "context1", "setFromResource", "setFromResource$app_debug", "setPath", "setPath$app_debug", "setRotate", "setRotate$app_debug", "setRow", "setRow$app_debug", "setUnknown", "setUnknown$app_debug", "BrushEffectFromResource", "app_debug"})
public final class BrushEffectLoad {
    private int alpha = 120;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap bitmap;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap[] bitmaps;
    @org.jetbrains.annotations.Nullable()
    private drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad.BrushEffectFromResource brushEffectFromResource;
    private int column = 1;
    protected android.content.Context contextVal;
    private int deviceWidth = 0;
    private int deviceHeight = 0;
    private float f1440b = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Paint f1449k;
    private float f1452n = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String path;
    private boolean rotate = false;
    private int row = 1;
    private float unknown = 0.15F;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap aBitmap;
    
    public BrushEffectLoad() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    protected final android.content.Context getContextVal() {
        return null;
    }
    
    protected final void setContextVal(@org.jetbrains.annotations.NotNull()
    android.content.Context p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap getABitmap$app_debug() {
        return null;
    }
    
    public final void setABitmap$app_debug(@org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap p0) {
    }
    
    public final void m2413a() {
    }
    
    public final void m2416a(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
    }
    
    public final void onCanvas(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas, float f, float f2, int i, int i2, int i3, int i4) {
    }
    
    public final void setContext(@org.jetbrains.annotations.NotNull()
    android.content.Context context1) {
    }
    
    public final void setFromResource$app_debug(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad.BrushEffectFromResource brushEffectFromResource) {
    }
    
    public final int GetRow$app_debug() {
        return 0;
    }
    
    public final void setRow$app_debug(int row) {
    }
    
    public final int GetColumn$app_debug() {
        return 0;
    }
    
    public final void setColumn$app_debug(int column) {
    }
    
    public final void setRotate$app_debug(boolean rotate) {
    }
    
    public final void setPath$app_debug(@org.jetbrains.annotations.NotNull()
    java.lang.String path) {
    }
    
    public final void setUnknown$app_debug(float unknown) {
    }
    
    public final void setAlpha$app_debug(int i) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad$BrushEffectFromResource;", "", "(Ljava/lang/String;I)V", "RES", "ASSERT", "FILTERED", "ONLINE", "CACHE", "app_debug"})
    public static enum BrushEffectFromResource {
        /*public static final*/ RES /* = new RES() */,
        /*public static final*/ ASSERT /* = new ASSERT() */,
        /*public static final*/ FILTERED /* = new FILTERED() */,
        /*public static final*/ ONLINE /* = new ONLINE() */,
        /*public static final*/ CACHE /* = new CACHE() */;
        
        BrushEffectFromResource() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad.BrushEffectFromResource> getEntries() {
            return null;
        }
    }
}