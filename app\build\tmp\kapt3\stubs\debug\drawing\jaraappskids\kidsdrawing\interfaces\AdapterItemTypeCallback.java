package drawing.jaraappskids.kidsdrawing.interfaces;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH&\u00a8\u0006\n"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemTypeCallback;", "", "onAdapterItemTypeClick", "", "mType", "", "stickerClass", "Ldrawing/jaraappskids/kidsdrawing/pojo/StickerClass;", "mPos", "", "app_debug"})
public abstract interface AdapterItemTypeCallback {
    
    public abstract void onAdapterItemTypeClick(@org.jetbrains.annotations.NotNull()
    java.lang.String mType, int mPos);
    
    public abstract void onAdapterItemTypeClick(@org.jetbrains.annotations.NotNull()
    java.lang.String mType, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.pojo.StickerClass stickerClass);
}