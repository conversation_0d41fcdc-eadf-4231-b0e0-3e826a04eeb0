<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="IndicatorSeekBar">
        //seekBar
        <attr format="float" name="isb_max"/>
        <attr format="float" name="isb_min"/>
        <attr format="float" name="isb_progress"/>
        <attr format="boolean" name="isb_progress_value_float"/>
        <attr format="boolean" name="isb_seek_smoothly"/>
        <attr format="boolean" name="isb_r2l"/>
        <attr format="integer" name="isb_ticks_count"/>
        <attr format="boolean" name="isb_user_seekable"/>
        <attr format="boolean" name="isb_clear_default_padding"/>
        <attr format="boolean" name="isb_only_thumb_draggable"/>
        //indicator
        <attr name="isb_show_indicator"><!-- the type of indicator, default rectangle_rounded_corner/0.-->
            <enum name="none" value="0"/>
            <enum name="circular_bubble" value="1"/>
            <enum name="rounded_rectangle" value="2"/>
            <enum name="rectangle" value="3"/>
            <enum name="custom" value="4"/><!--choose custom type that you can set the custom indicator layout you want.-->
        </attr>
        <attr format="color|reference" name="isb_indicator_color"/>
        <attr format="color|reference" name="isb_indicator_text_color"/>
        <attr format="dimension|reference" name="isb_indicator_text_size"/>
        <attr format="reference" name="isb_indicator_content_layout"/>
        <attr format="reference" name="isb_indicator_top_content_layout"/> 
        //track
        <attr format="dimension|reference" name="isb_track_background_size"/>
        <attr format="color|reference" name="isb_track_background_color"/>
        <attr format="dimension|reference" name="isb_track_progress_size"/>
        <attr format="color|reference" name="isb_track_progress_color"/>
        <attr format="boolean" name="isb_track_rounded_corners"/>
        //thumb text
        <attr format="color|reference" name="isb_thumb_text_color"/>
        <attr format="boolean" name="isb_show_thumb_text"/>
        //thumb
        <attr format="dimension|reference" name="isb_thumb_size"/>
        <attr format="color|reference" name="isb_thumb_color"/>
        <attr format="reference" name="isb_thumb_drawable"/>
        <attr format="boolean" name="isb_thumb_adjust_auto"/>
        //tickMarks
        <attr format="color|reference" name="isb_tick_marks_color"/>
        <attr format="dimension|reference" name="isb_tick_marks_size"/>
        <attr format="reference" name="isb_tick_marks_drawable"/>
        <attr format="boolean" name="isb_tick_marks_ends_hide"/>
        <attr format="boolean" name="isb_tick_marks_swept_hide"/>
        <attr name="isb_show_tick_marks_type"><!--select the tick shape type, default not show： NONE/0-->
            <enum name="none" value="0"/>
            <enum name="oval" value="1"/>
            <enum name="square" value="2"/>
            <enum name="divider" value="3"/> <!--show tickMarks shape as vertical line , line'size is 1 dp.-->
        </attr>
        //tickTexts
        <attr format="boolean" name="isb_show_tick_texts"/>
        <attr format="reference|color" name="isb_tick_texts_color"/>
        <attr format="dimension|reference" name="isb_tick_texts_size"/>
        <attr format="reference" name="isb_tick_texts_array"/>
        <attr name="isb_tick_texts_typeface"><!--select the typeface for tick texts/thumb text, default normal-->
            <enum name="normal" value="0"/>
            <enum name="monospace" value="1"/>
            <enum name="sans" value="2"/>
            <enum name="serif" value="3"/>
        </attr>
    </declare-styleable>
</resources>