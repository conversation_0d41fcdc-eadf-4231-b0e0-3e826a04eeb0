<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_light_blue"
    tools:context=".editor.ui.ModernEditorActivity">

    <!-- App Bar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Drawing Canvas -->
    <drawing.jaraappskids.kidsdrawing.editor.view.ModernDrawingView
        android:id="@+id/drawingView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        android:background="@android:color/white"
        app:layout_constraintBottom_toTopOf="@+id/bottomToolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/drawingView"
        app:layout_constraintEnd_toEndOf="@+id/drawingView"
        app:layout_constraintStart_toStartOf="@+id/drawingView"
        app:layout_constraintTop_toTopOf="@+id/drawingView" />

    <!-- Bottom Toolbar -->
    <LinearLayout
        android:id="@+id/bottomToolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Tool Selection -->
        <Button
            android:id="@+id/btnBrush"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_primary_blue"
            android:text="Brush"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btnEraser"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_primary_pink"
            android:text="Eraser"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btnUndo"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_primary_orange"
            android:text="Undo"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btnRedo"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_primary_green"
            android:text="Redo"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btnClear"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_primary_purple"
            android:text="Clear"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btnSave"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@color/kids_bright_green"
            android:text="Save"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

    </LinearLayout>

    <!-- Color Selection Panel -->
    <LinearLayout
        android:id="@+id/colorPickerPanel"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/kids_soft_blue"
        android:orientation="horizontal"
        android:padding="8dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/bottomToolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/colorBlack"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@android:color/black" />

        <View
            android:id="@+id/colorRed"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@android:color/holo_red_dark" />

        <View
            android:id="@+id/colorGreen"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@android:color/holo_green_dark" />

        <View
            android:id="@+id/colorBlue"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@android:color/holo_blue_dark" />

        <View
            android:id="@+id/colorYellow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@android:color/holo_orange_dark" />

        <View
            android:id="@+id/colorPurple"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:background="@color/kids_primary_purple" />

    </LinearLayout>

    <!-- Brush Settings Panel -->
    <LinearLayout
        android:id="@+id/brushSettingsPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/kids_soft_green"
        android:orientation="horizontal"
        android:padding="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/colorPickerPanel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp"
            android:text="Size:"
            android:textColor="@android:color/black" />

        <SeekBar
            android:id="@+id/brushSizeSlider"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:max="50"
            android:progress="10" />

        <Button
            android:id="@+id/btnBrushSettings"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="8dp"
            android:background="@color/kids_primary_orange"
            android:text="Settings"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Hidden panels for future use -->
    <View
        android:id="@+id/layerPanel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/selectedColorIndicator"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- Dummy views for compatibility -->
    <View
        android:id="@+id/btnShapeRect"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnShapeCircle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnText"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnSticker"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnColorPicker"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnLayers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- Dummy brush type buttons -->
    <View
        android:id="@+id/btnPencil"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnMarker"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnAirbrush"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/btnHighlighter"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- Dummy color views -->
    <View
        android:id="@+id/colorWhite"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorMagenta"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorCyan"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorGray"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorDarkGray"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorLightGray"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/colorOrange"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <View
        android:id="@+id/brushOpacitySlider"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
