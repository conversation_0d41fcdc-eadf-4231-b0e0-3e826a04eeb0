package drawing.jaraappskids.kidsdrawing.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository
import javax.inject.Singleton

/**
 * Hilt module for dependency injection
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideDrawingRepository(
        @ApplicationContext context: Context
    ): DrawingRepository {
        return DrawingRepository(context)
    }
}
