<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_full_screen" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_full_screen.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_full_screen_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="76" endOffset="14"/></Target><Target id="@+id/ivBack" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="20" startOffset="12" endLine="26" endOffset="58"/></Target><Target id="@+id/tvTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="28" startOffset="12" endLine="38" endOffset="59"/></Target><Target id="@+id/ivSharePreview" view="drawing.jaraappskids.kidsdrawing.custom.SquareImageView"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="56"/></Target><Target id="@+id/ivShare" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="50" startOffset="8" endLine="56" endOffset="60"/></Target><Target id="@+id/llAdView" view="RelativeLayout"><Expressions/><location startLine="60" startOffset="4" endLine="66" endOffset="42"/></Target><Target id="@+id/llAdViewFacebook" view="LinearLayout"><Expressions/><location startLine="68" startOffset="4" endLine="73" endOffset="42"/></Target></Targets></Layout>