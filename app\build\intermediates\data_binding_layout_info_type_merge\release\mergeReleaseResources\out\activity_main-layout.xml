<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_main_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="356" endOffset="16"/></Target><Target id="@+id/ivCloud1" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="64"/></Target><Target id="@+id/ivCloud2" view="ImageView"><Expressions/><location startLine="22" startOffset="4" endLine="33" endOffset="64"/></Target><Target id="@+id/ivStar1" view="ImageView"><Expressions/><location startLine="36" startOffset="4" endLine="45" endOffset="64"/></Target><Target id="@+id/ivStar2" view="ImageView"><Expressions/><location startLine="47" startOffset="4" endLine="57" endOffset="64"/></Target><Target id="@+id/ivStar3" view="ImageView"><Expressions/><location startLine="60" startOffset="4" endLine="70" endOffset="64"/></Target><Target id="@+id/ivStar4" view="ImageView"><Expressions/><location startLine="72" startOffset="4" endLine="82" endOffset="64"/></Target><Target id="@+id/tvAppTitle1" view="TextView"><Expressions/><location startLine="117" startOffset="16" endLine="127" endOffset="55"/></Target><Target id="@+id/tvAppTitle2" view="TextView"><Expressions/><location startLine="129" startOffset="16" endLine="139" endOffset="55"/></Target><Target id="@+id/tvEmojiDecoration" view="TextView"><Expressions/><location startLine="142" startOffset="16" endLine="152" endOffset="43"/></Target><Target id="@+id/cardLetsPlay" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="165" startOffset="16" endLine="194" endOffset="51"/></Target><Target id="@+id/ivLetsPlay" view="RelativeLayout"><Expressions/><location startLine="174" startOffset="20" endLine="193" endOffset="36"/></Target><Target id="@+id/cardMyCreation" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="197" startOffset="16" endLine="232" endOffset="51"/></Target><Target id="@+id/ivMyCreation" view="RelativeLayout"><Expressions/><location startLine="206" startOffset="20" endLine="231" endOffset="36"/></Target><Target id="@+id/cardShare" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="235" startOffset="16" endLine="270" endOffset="51"/></Target><Target id="@+id/ivShare" view="RelativeLayout"><Expressions/><location startLine="244" startOffset="20" endLine="269" endOffset="36"/></Target><Target id="@+id/cardRate" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="273" startOffset="16" endLine="308" endOffset="51"/></Target><Target id="@+id/ivRate" view="RelativeLayout"><Expressions/><location startLine="282" startOffset="20" endLine="307" endOffset="36"/></Target><Target id="@+id/btnAdFree" view="RelativeLayout"><Expressions/><location startLine="321" startOffset="16" endLine="338" endOffset="32"/></Target><Target id="@+id/llAdView" view="RelativeLayout"><Expressions/><location startLine="345" startOffset="4" endLine="354" endOffset="38"/></Target></Targets></Layout>