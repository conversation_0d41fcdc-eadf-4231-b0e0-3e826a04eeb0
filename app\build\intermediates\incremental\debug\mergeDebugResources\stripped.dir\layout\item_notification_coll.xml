<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="2dp">

    <RelativeLayout
        android:id="@+id/icon_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/big_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:paddingLeft="4dp"
            android:paddingRight="6dp"
            android:paddingBottom="1dp"
            android:src="@mipmap/ic_launcher" />


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/notification_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="false"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/icon_container"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="6dp">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"

            tools:text="Expand me to see a detailed message!" />

        <TextView
            android:id="@+id/content_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            tools:text="Expand me to see a detailed message!" />
    </LinearLayout>

    <TextView
        android:id="@+id/timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:paddingTop="8dp"
        android:paddingRight="3.5dp"
        android:visibility="gone" />

</RelativeLayout>
