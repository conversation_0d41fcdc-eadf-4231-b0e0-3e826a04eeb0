package drawing.jaraappskids.kidsdrawing.adapters;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u0014B-\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0016\u0010\u0005\u001a\u0012\u0012\u0004\u0012\u00020\u00070\u0006j\b\u0012\u0004\u0012\u00020\u0007`\b\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\f\u001a\u00020\rH\u0016J\u001c\u0010\u000e\u001a\u00020\u000f2\n\u0010\u0010\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\rH\u0016J\u001c\u0010\u0012\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0010\u001a\u00020\u00132\u0006\u0010\u0011\u001a\u00020\rH\u0016R\u001e\u0010\u0005\u001a\u0012\u0012\u0004\u0012\u00020\u00070\u0006j\b\u0012\u0004\u0012\u00020\u0007`\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/adapters/BGImageAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Ldrawing/jaraappskids/kidsdrawing/adapters/BGImageAdapter$AdapterVH;", "context", "Landroid/content/Context;", "BGImageClassArrayList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/BGImageClass;", "Lkotlin/collections/ArrayList;", "adapterItemCallback", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemCallback;", "(Landroid/content/Context;Ljava/util/ArrayList;Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemCallback;)V", "getItemCount", "", "onBindViewHolder", "", "p0", "p1", "onCreateViewHolder", "Landroid/view/ViewGroup;", "AdapterVH", "app_debug"})
public final class BGImageAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<drawing.jaraappskids.kidsdrawing.adapters.BGImageAdapter.AdapterVH> {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> BGImageClassArrayList = null;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback adapterItemCallback = null;
    
    public BGImageAdapter(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> BGImageClassArrayList, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback adapterItemCallback) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public drawing.jaraappskids.kidsdrawing.adapters.BGImageAdapter.AdapterVH onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup p0, int p1) {
        return null;
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.adapters.BGImageAdapter.AdapterVH p0, int p1) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00020\u00012\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0012\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0004H\u0016R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/adapters/BGImageAdapter$AdapterVH;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "Landroid/view/View$OnClickListener;", "view", "Landroid/view/View;", "(Ldrawing/jaraappskids/kidsdrawing/adapters/BGImageAdapter;Landroid/view/View;)V", "cvBGImage", "Ldrawing/jaraappskids/kidsdrawing/custom/SquareCardView;", "getCvBGImage", "()Ldrawing/jaraappskids/kidsdrawing/custom/SquareCardView;", "ivBGImage", "Landroidx/appcompat/widget/AppCompatImageView;", "getIvBGImage", "()Landroidx/appcompat/widget/AppCompatImageView;", "onClick", "", "v", "app_debug"})
    public final class AdapterVH extends androidx.recyclerview.widget.RecyclerView.ViewHolder implements android.view.View.OnClickListener {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.custom.SquareCardView cvBGImage = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.AppCompatImageView ivBGImage = null;
        
        public AdapterVH(@org.jetbrains.annotations.NotNull()
        android.view.View view) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.custom.SquareCardView getCvBGImage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.appcompat.widget.AppCompatImageView getIvBGImage() {
            return null;
        }
        
        @java.lang.Override()
        public void onClick(@org.jetbrains.annotations.Nullable()
        android.view.View v) {
        }
    }
}