<variant
    name="debug"
    package="com.warkiz.widget"
    minSdkVersion="14"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\ab7e709698b5a2baa7679309adbf25bf\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.warkiz.widget.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\ab7e709698b5a2baa7679309adbf25bf\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
