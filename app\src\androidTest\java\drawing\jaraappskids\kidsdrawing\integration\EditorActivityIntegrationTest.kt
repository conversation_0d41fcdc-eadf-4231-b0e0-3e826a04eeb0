package drawing.jaraappskids.kidsdrawing.integration

import android.content.Intent
import androidx.test.core.app.ActivityScenario
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.assertion.ViewAssertions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.R
import drawing.jaraappskids.kidsdrawing.activities.EditorActivity
import drawing.jaraappskids.kidsdrawing.common.CommonConstants
import drawing.jaraappskids.kidsdrawing.drwaing.WTDrawingView
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Integration tests for EditorActivity
 * Tests complete user workflows and UI interactions
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class EditorActivityIntegrationTest {

    private lateinit var scenario: ActivityScenario<EditorActivity>

    @Before
    fun setUp() {
        // Create intent with test data
        val intent = Intent(InstrumentationRegistry.getInstrumentation().targetContext, EditorActivity::class.java).apply {
            putExtra(CommonConstants.KeyBGImageType, true) // Easy mode
            putExtra(CommonConstants.KeyItemPos, 0) // First image
        }
        
        scenario = ActivityScenario.launch(intent)
    }

    @After
    fun tearDown() {
        scenario.close()
    }

    @Test
    fun testEditorActivityLaunch() {
        // Verify that the editor activity launches successfully
        onView(withId(R.id.drawing_view))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.llButton))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testBrushToolSelection() {
        // Test brush tool selection
        onView(withId(R.id.llBrush))
            .perform(click())
        
        // Verify brush view is displayed
        onView(withId(R.id.llBrushView))
            .check(matches(isDisplayed()))
        
        // Verify button panel is hidden
        onView(withId(R.id.llButton))
            .check(matches(withEffectiveVisibility(Visibility.GONE)))
    }

    @Test
    fun testMagicBrushToolSelection() {
        // Test magic brush tool selection
        onView(withId(R.id.llMagicBrush))
            .perform(click())
        
        // Verify magic brush view is displayed
        onView(withId(R.id.llMagicBrushView))
            .check(matches(isDisplayed()))
        
        // Verify clear button is visible for magic brush
        onView(withId(R.id.llClear))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testPencilToolSelection() {
        // Test pencil tool selection
        onView(withId(R.id.llPencil))
            .perform(click())
        
        // Verify pencil view is displayed
        onView(withId(R.id.llPencilView))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testStickerToolSelection() {
        // Test sticker tool selection
        onView(withId(R.id.llSticker))
            .perform(click())
        
        // Verify sticker view is displayed
        onView(withId(R.id.llStickerView))
            .check(matches(isDisplayed()))
        
        // Verify tab layout for sticker categories is displayed
        onView(withId(R.id.tabLayoutStickerCategories))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testTextToolSelection() {
        // Test text tool selection
        onView(withId(R.id.llAddText))
            .perform(click())
        
        // Verify text view is displayed
        onView(withId(R.id.llTextView))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testToolSwitching() {
        // Test switching between different tools
        
        // Start with brush
        onView(withId(R.id.llBrush))
            .perform(click())
        onView(withId(R.id.llBrushView))
            .check(matches(isDisplayed()))
        
        // Switch to magic brush
        onView(withId(R.id.ivBackBrush))
            .perform(click())
        onView(withId(R.id.llMagicBrush))
            .perform(click())
        onView(withId(R.id.llMagicBrushView))
            .check(matches(isDisplayed()))
        
        // Switch to pencil
        onView(withId(R.id.ivBackMagicBrush))
            .perform(click())
        onView(withId(R.id.llPencil))
            .perform(click())
        onView(withId(R.id.llPencilView))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testUndoRedoButtons() {
        // Test undo/redo button functionality
        
        // Select brush tool first
        onView(withId(R.id.llBrush))
            .perform(click())
        
        // Test undo button
        onView(withId(R.id.llUndo))
            .check(matches(isDisplayed()))
            .perform(click())
        
        // Test redo button
        onView(withId(R.id.llRedo))
            .check(matches(isDisplayed()))
            .perform(click())
    }

    @Test
    fun testBrushSettings() {
        // Test brush settings functionality
        
        // Select brush tool
        onView(withId(R.id.llBrush))
            .perform(click())
        
        // Open brush settings
        onView(withId(R.id.ivSettingBrush))
            .perform(click())
        
        // Verify brush control panel is displayed
        onView(withId(R.id.llBrushControl))
            .check(matches(isDisplayed()))
        
        // Verify smoothness and brush size controls
        onView(withId(R.id.sbSmoothness))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.sbBrushSize))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testEraserFunctionality() {
        // Test eraser functionality
        
        // Select brush tool
        onView(withId(R.id.llBrush))
            .perform(click())
        
        // Click eraser button
        onView(withId(R.id.ivEraser))
            .perform(click())
        
        // Verify eraser is activated (this would require checking the drawing view state)
        scenario.onActivity { activity ->
            val drawingView = activity.findViewById<WTDrawingView>(R.id.drawing_view)
            assertThat(drawingView.isDrawingMode()).isTrue()
        }
    }

    @Test
    fun testSaveButtonVisibility() {
        // Test save button is visible and clickable
        onView(withId(R.id.llSave))
            .check(matches(isDisplayed()))
            .check(matches(isClickable()))
    }

    @Test
    fun testBackButtonFunctionality() {
        // Test back button functionality
        onView(withId(R.id.llBack))
            .check(matches(isDisplayed()))
            .check(matches(isClickable()))
    }

    @Test
    fun testDrawingViewInteraction() {
        // Test that drawing view responds to touch
        scenario.onActivity { activity ->
            val drawingView = activity.findViewById<WTDrawingView>(R.id.drawing_view)
            
            // Verify drawing view is properly initialized
            assertThat(drawingView).isNotNull()
            assertThat(drawingView.isEnabled).isTrue()
            assertThat(drawingView.visibility).isEqualTo(android.view.View.VISIBLE)
        }
        
        // Perform touch on drawing view
        onView(withId(R.id.drawing_view))
            .perform(click())
    }

    @Test
    fun testCompleteDrawingWorkflow() {
        // Test a complete drawing workflow
        
        // 1. Select brush tool
        onView(withId(R.id.llBrush))
            .perform(click())
        
        // 2. Select a color (click on paint brush recycler view)
        onView(withId(R.id.rvPaintBrush))
            .check(matches(isDisplayed()))
        
        // 3. Adjust brush size
        onView(withId(R.id.ivSettingBrush))
            .perform(click())
        
        onView(withId(R.id.sbBrushSize))
            .check(matches(isDisplayed()))
        
        // 4. Go back to drawing
        onView(withId(R.id.ivBackBrush))
            .perform(click())
        
        // 5. Switch to eraser
        onView(withId(R.id.ivEraser))
            .perform(click())
        
        // 6. Test undo
        onView(withId(R.id.llUndo))
            .perform(click())
        
        // 7. Test redo
        onView(withId(R.id.llRedo))
            .perform(click())
    }

    @Test
    fun testMemoryStabilityDuringUIInteractions() {
        // Test memory stability during rapid UI interactions
        
        repeat(10) {
            // Rapid tool switching
            onView(withId(R.id.llBrush)).perform(click())
            onView(withId(R.id.ivBackBrush)).perform(click())
            
            onView(withId(R.id.llMagicBrush)).perform(click())
            onView(withId(R.id.ivBackMagicBrush)).perform(click())
            
            onView(withId(R.id.llPencil)).perform(click())
            onView(withId(R.id.ivBackPencil)).perform(click())
            
            onView(withId(R.id.llSticker)).perform(click())
            onView(withId(R.id.ivBackSticker)).perform(click())
        }
        
        // Verify activity is still responsive
        onView(withId(R.id.drawing_view))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testUIResponsivenessUnderLoad() {
        // Test UI responsiveness under load
        
        // Rapid button clicks
        repeat(20) {
            onView(withId(R.id.llUndo)).perform(click())
            onView(withId(R.id.llRedo)).perform(click())
        }
        
        // Verify UI is still responsive
        onView(withId(R.id.llBrush))
            .perform(click())
        
        onView(withId(R.id.llBrushView))
            .check(matches(isDisplayed()))
    }
}
