{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeDebugResources-85:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\61ab77ac73f341a2d1782fb97c5b4eb5\\transformed\\jetified-ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "49,50,71,72,73,85,86,149,150,155,164,167,168,179,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4558,4653,7146,7239,7337,8401,8479,13471,13560,13974,14834,15124,15205,15931,16347,16428,16494", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "4648,4731,7234,7332,7421,8474,8571,13555,13640,14037,14898,15200,15285,15999,16423,16489,16609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6cb5cda3da2f0136ecb83c29cfb693f\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,15684", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,15761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\af892568e4c6055353b79bd60cd12ea7\\transformed\\core-1.13.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "39,40,41,42,43,44,45,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3483,3581,3684,3789,3890,4003,4109,16077", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3576,3679,3784,3885,3998,4104,4231,16173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\003d3540143f1bd8ca35f691a36398bc\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,74,75,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,165,177,178,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3045,3123,3199,3280,3387,4236,4343,4475,7426,7493,7841,8332,8576,8635,8720,8783,8846,8904,8969,9030,9091,9197,9255,9315,9374,9444,9560,9639,9730,9823,9921,10001,10135,10210,10286,10423,10520,10618,10675,10730,10796,10866,10943,11014,11099,11167,11243,11324,11402,11503,11589,11676,11773,11872,11946,12016,12120,12174,12261,12328,12418,12510,12572,12636,12699,12765,12870,12980,13081,13188,13249,14903,15766,15851,16004", "endLines": "5,34,35,36,37,38,46,47,48,74,75,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,165,177,178,180", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "304,3118,3194,3275,3382,3478,4338,4470,4553,7488,7553,7930,8396,8630,8715,8778,8841,8899,8964,9025,9086,9192,9250,9310,9369,9439,9555,9634,9725,9818,9916,9996,10130,10205,10281,10418,10515,10613,10670,10725,10791,10861,10938,11009,11094,11162,11238,11319,11397,11498,11584,11671,11768,11867,11941,12011,12115,12169,12256,12323,12413,12505,12567,12631,12694,12760,12865,12975,13076,13183,13244,13303,14977,15846,15926,16072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e4138bbfd937e2ada2e439038e6cf17f\\transformed\\preference-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,489,658,738", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "173,264,342,484,653,733,811"}, "to": {"startLines": "69,78,151,166,182,186,187", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6967,7750,13645,14982,16178,16614,16694", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "7035,7836,13718,15119,16342,16689,16767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b608133b9f6151e8ab6ea0b248ed861e\\transformed\\jetified-tv-ads-1.0.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,9", "startColumns": "0,0,0,0", "startOffsets": "197,247,372,598", "endColumns": "49,124,58,75", "endOffsets": "246,371,430,673"}, "to": {"startLines": "33,76,77,80", "startColumns": "4,4,4,4", "startOffsets": "2991,7558,7687,7935", "endColumns": "53,128,62,79", "endOffsets": "3040,7682,7745,8010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d465a8673ed4187c6f67df5a33e6ac9a\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,296,350,418,488,589,652,783,920,1038,1092,1148,1268,1349,1390,1486,1525,1563,1610,1674,1715", "endColumns": "48,47,53,67,69,100,62,130,136,117,53,55,119,80,40,95,38,37,46,63,40,55", "endOffsets": "247,295,349,417,487,588,651,782,919,1037,1091,1147,1267,1348,1389,1485,1524,1562,1609,1673,1714,1770"}, "to": {"startLines": "146,147,148,152,153,154,156,157,158,159,160,161,162,163,169,170,171,172,173,174,175,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13308,13361,13413,13723,13795,13869,14042,14109,14244,14385,14507,14565,14625,14749,15290,15335,15435,15478,15520,15571,15639,16772", "endColumns": "52,51,57,71,73,104,66,134,140,121,57,59,123,84,44,99,42,41,50,67,44,59", "endOffsets": "13356,13408,13466,13790,13864,13969,14104,14239,14380,14502,14560,14620,14744,14829,15330,15430,15473,15515,15566,15634,15679,16827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8784e12cfdf22977c2421790741b565f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5747", "endColumns": "145", "endOffsets": "5888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6128e6b97d809c1b0cadcc023b8fe28\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4736,4844,5004,5130,5242,5393,5523,5635,5893,6050,6159,6325,6455,6596,6749,6812,6879", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "4839,4999,5125,5237,5388,5518,5630,5742,6045,6154,6320,6450,6591,6744,6807,6874,6962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2507dffaec27660ae19740e0267a928c\\transformed\\browser-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "70,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7040,8015,8117,8229", "endColumns": "105,101,111,102", "endOffsets": "7141,8112,8224,8327"}}]}]}