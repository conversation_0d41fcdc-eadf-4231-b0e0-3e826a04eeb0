package drawing.jaraappskids.kidsdrawing.adapters;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u0017B-\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0016\u0010\u0005\u001a\u0012\u0012\u0004\u0012\u00020\u00070\u0006j\b\u0012\u0004\u0012\u00020\u0007`\b\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\f\u001a\u00020\rH\u0016J\u001d\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\rH\u0016\u00a2\u0006\u0002\u0010\u0013J\u001d\u0010\u0014\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\rH\u0016\u00a2\u0006\u0002\u0010\u0016R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0005\u001a\u0012\u0012\u0004\u0012\u00020\u00070\u0006j\b\u0012\u0004\u0012\u00020\u0007`\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/adapters/MagicBrushButtonAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Ldrawing/jaraappskids/kidsdrawing/adapters/MagicBrushButtonAdapter$AdapterVH;", "context", "Landroid/content/Context;", "mbButtonClassArrayList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/MagicBrushClass;", "Lkotlin/collections/ArrayList;", "adapterItemTypeCallback", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemTypeCallback;", "(Landroid/content/Context;Ljava/util/ArrayList;Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemTypeCallback;)V", "getItemCount", "", "onBindViewHolder", "", "p0", "error/NonExistentClass", "p1", "(Lerror/NonExistentClass;I)V", "onCreateViewHolder", "Landroid/view/ViewGroup;", "(Landroid/view/ViewGroup;I)Lerror/NonExistentClass;", "AdapterVH", "app_debug"})
public final class MagicBrushButtonAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<drawing.jaraappskids.kidsdrawing.adapters.MagicBrushButtonAdapter.AdapterVH> {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.MagicBrushClass> mbButtonClassArrayList = null;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemTypeCallback adapterItemTypeCallback = null;
    
    public MagicBrushButtonAdapter(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.MagicBrushClass> mbButtonClassArrayList, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemTypeCallback adapterItemTypeCallback) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public error.NonExistentClass onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup p0, int p1) {
        return null;
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    error.NonExistentClass p0, int p1) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u00012\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0012\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0004H\u0016J\u0010\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u000fH\u0002R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0012\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011\u00a8\u0006\u0019"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/adapters/MagicBrushButtonAdapter$AdapterVH;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "Landroid/view/View$OnClickListener;", "view", "Landroid/view/View;", "(Ldrawing/jaraappskids/kidsdrawing/adapters/MagicBrushButtonAdapter;Landroid/view/View;)V", "ivMBButton", "Landroidx/appcompat/widget/AppCompatImageView;", "getIvMBButton", "()Landroidx/appcompat/widget/AppCompatImageView;", "params", "Landroid/widget/LinearLayout$LayoutParams;", "getParams", "()Landroid/widget/LinearLayout$LayoutParams;", "tenDp", "", "getTenDp", "()I", "zeroDP", "getZeroDP", "onClick", "", "v", "updateSelectedAndNotify", "mPos", "app_debug"})
    public final class AdapterVH extends androidx.recyclerview.widget.RecyclerView.ViewHolder implements android.view.View.OnClickListener {
        private final int zeroDP = 0;
        private final int tenDp = 0;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout.LayoutParams params = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.appcompat.widget.AppCompatImageView ivMBButton = null;
        
        public AdapterVH(@org.jetbrains.annotations.NotNull()
        android.view.View view) {
            super(null);
        }
        
        public final int getZeroDP() {
            return 0;
        }
        
        public final int getTenDp() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.LinearLayout.LayoutParams getParams() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.appcompat.widget.AppCompatImageView getIvMBButton() {
            return null;
        }
        
        @java.lang.Override()
        public void onClick(@org.jetbrains.annotations.Nullable()
        android.view.View v) {
        }
        
        private final void updateSelectedAndNotify(int mPos) {
        }
    }
}