<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_text" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\dialog_add_text.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_text_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="57" endOffset="14"/></Target><Target id="@+id/etText" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="13" startOffset="4" endLine="21" endOffset="62"/></Target><Target id="@+id/tvCancel" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="30" startOffset="8" endLine="40" endOffset="48"/></Target><Target id="@+id/tvOK" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="42" startOffset="8" endLine="53" endOffset="48"/></Target></Targets></Layout>