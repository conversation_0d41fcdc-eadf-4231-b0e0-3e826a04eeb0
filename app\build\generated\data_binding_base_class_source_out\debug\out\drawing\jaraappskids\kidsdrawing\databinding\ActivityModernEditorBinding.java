// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import drawing.jaraappskids.kidsdrawing.editor.view.ModernDrawingView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityModernEditorBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout bottomToolbar;

  @NonNull
  public final View brushOpacitySlider;

  @NonNull
  public final LinearLayout brushSettingsPanel;

  @NonNull
  public final SeekBar brushSizeSlider;

  @NonNull
  public final View btnAirbrush;

  @NonNull
  public final Button btnBrush;

  @NonNull
  public final Button btnBrushSettings;

  @NonNull
  public final Button btnClear;

  @NonNull
  public final View btnColorPicker;

  @NonNull
  public final Button btnEraser;

  @NonNull
  public final View btnHighlighter;

  @NonNull
  public final View btnLayers;

  @NonNull
  public final View btnMarker;

  @NonNull
  public final View btnPencil;

  @NonNull
  public final Button btnRedo;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final View btnShapeCircle;

  @NonNull
  public final View btnShapeRect;

  @NonNull
  public final View btnSticker;

  @NonNull
  public final View btnText;

  @NonNull
  public final Button btnUndo;

  @NonNull
  public final View colorBlack;

  @NonNull
  public final View colorBlue;

  @NonNull
  public final View colorCyan;

  @NonNull
  public final View colorDarkGray;

  @NonNull
  public final View colorGray;

  @NonNull
  public final View colorGreen;

  @NonNull
  public final View colorLightGray;

  @NonNull
  public final View colorMagenta;

  @NonNull
  public final View colorOrange;

  @NonNull
  public final LinearLayout colorPickerPanel;

  @NonNull
  public final View colorPurple;

  @NonNull
  public final View colorRed;

  @NonNull
  public final View colorWhite;

  @NonNull
  public final View colorYellow;

  @NonNull
  public final ModernDrawingView drawingView;

  @NonNull
  public final View layerPanel;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final View selectedColorIndicator;

  @NonNull
  public final Toolbar toolbar;

  private ActivityModernEditorBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout bottomToolbar, @NonNull View brushOpacitySlider,
      @NonNull LinearLayout brushSettingsPanel, @NonNull SeekBar brushSizeSlider,
      @NonNull View btnAirbrush, @NonNull Button btnBrush, @NonNull Button btnBrushSettings,
      @NonNull Button btnClear, @NonNull View btnColorPicker, @NonNull Button btnEraser,
      @NonNull View btnHighlighter, @NonNull View btnLayers, @NonNull View btnMarker,
      @NonNull View btnPencil, @NonNull Button btnRedo, @NonNull Button btnSave,
      @NonNull View btnShapeCircle, @NonNull View btnShapeRect, @NonNull View btnSticker,
      @NonNull View btnText, @NonNull Button btnUndo, @NonNull View colorBlack,
      @NonNull View colorBlue, @NonNull View colorCyan, @NonNull View colorDarkGray,
      @NonNull View colorGray, @NonNull View colorGreen, @NonNull View colorLightGray,
      @NonNull View colorMagenta, @NonNull View colorOrange, @NonNull LinearLayout colorPickerPanel,
      @NonNull View colorPurple, @NonNull View colorRed, @NonNull View colorWhite,
      @NonNull View colorYellow, @NonNull ModernDrawingView drawingView, @NonNull View layerPanel,
      @NonNull ProgressBar progressBar, @NonNull View selectedColorIndicator,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.bottomToolbar = bottomToolbar;
    this.brushOpacitySlider = brushOpacitySlider;
    this.brushSettingsPanel = brushSettingsPanel;
    this.brushSizeSlider = brushSizeSlider;
    this.btnAirbrush = btnAirbrush;
    this.btnBrush = btnBrush;
    this.btnBrushSettings = btnBrushSettings;
    this.btnClear = btnClear;
    this.btnColorPicker = btnColorPicker;
    this.btnEraser = btnEraser;
    this.btnHighlighter = btnHighlighter;
    this.btnLayers = btnLayers;
    this.btnMarker = btnMarker;
    this.btnPencil = btnPencil;
    this.btnRedo = btnRedo;
    this.btnSave = btnSave;
    this.btnShapeCircle = btnShapeCircle;
    this.btnShapeRect = btnShapeRect;
    this.btnSticker = btnSticker;
    this.btnText = btnText;
    this.btnUndo = btnUndo;
    this.colorBlack = colorBlack;
    this.colorBlue = colorBlue;
    this.colorCyan = colorCyan;
    this.colorDarkGray = colorDarkGray;
    this.colorGray = colorGray;
    this.colorGreen = colorGreen;
    this.colorLightGray = colorLightGray;
    this.colorMagenta = colorMagenta;
    this.colorOrange = colorOrange;
    this.colorPickerPanel = colorPickerPanel;
    this.colorPurple = colorPurple;
    this.colorRed = colorRed;
    this.colorWhite = colorWhite;
    this.colorYellow = colorYellow;
    this.drawingView = drawingView;
    this.layerPanel = layerPanel;
    this.progressBar = progressBar;
    this.selectedColorIndicator = selectedColorIndicator;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityModernEditorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityModernEditorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_modern_editor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityModernEditorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomToolbar;
      LinearLayout bottomToolbar = ViewBindings.findChildViewById(rootView, id);
      if (bottomToolbar == null) {
        break missingId;
      }

      id = R.id.brushOpacitySlider;
      View brushOpacitySlider = ViewBindings.findChildViewById(rootView, id);
      if (brushOpacitySlider == null) {
        break missingId;
      }

      id = R.id.brushSettingsPanel;
      LinearLayout brushSettingsPanel = ViewBindings.findChildViewById(rootView, id);
      if (brushSettingsPanel == null) {
        break missingId;
      }

      id = R.id.brushSizeSlider;
      SeekBar brushSizeSlider = ViewBindings.findChildViewById(rootView, id);
      if (brushSizeSlider == null) {
        break missingId;
      }

      id = R.id.btnAirbrush;
      View btnAirbrush = ViewBindings.findChildViewById(rootView, id);
      if (btnAirbrush == null) {
        break missingId;
      }

      id = R.id.btnBrush;
      Button btnBrush = ViewBindings.findChildViewById(rootView, id);
      if (btnBrush == null) {
        break missingId;
      }

      id = R.id.btnBrushSettings;
      Button btnBrushSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnBrushSettings == null) {
        break missingId;
      }

      id = R.id.btnClear;
      Button btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.btnColorPicker;
      View btnColorPicker = ViewBindings.findChildViewById(rootView, id);
      if (btnColorPicker == null) {
        break missingId;
      }

      id = R.id.btnEraser;
      Button btnEraser = ViewBindings.findChildViewById(rootView, id);
      if (btnEraser == null) {
        break missingId;
      }

      id = R.id.btnHighlighter;
      View btnHighlighter = ViewBindings.findChildViewById(rootView, id);
      if (btnHighlighter == null) {
        break missingId;
      }

      id = R.id.btnLayers;
      View btnLayers = ViewBindings.findChildViewById(rootView, id);
      if (btnLayers == null) {
        break missingId;
      }

      id = R.id.btnMarker;
      View btnMarker = ViewBindings.findChildViewById(rootView, id);
      if (btnMarker == null) {
        break missingId;
      }

      id = R.id.btnPencil;
      View btnPencil = ViewBindings.findChildViewById(rootView, id);
      if (btnPencil == null) {
        break missingId;
      }

      id = R.id.btnRedo;
      Button btnRedo = ViewBindings.findChildViewById(rootView, id);
      if (btnRedo == null) {
        break missingId;
      }

      id = R.id.btnSave;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btnShapeCircle;
      View btnShapeCircle = ViewBindings.findChildViewById(rootView, id);
      if (btnShapeCircle == null) {
        break missingId;
      }

      id = R.id.btnShapeRect;
      View btnShapeRect = ViewBindings.findChildViewById(rootView, id);
      if (btnShapeRect == null) {
        break missingId;
      }

      id = R.id.btnSticker;
      View btnSticker = ViewBindings.findChildViewById(rootView, id);
      if (btnSticker == null) {
        break missingId;
      }

      id = R.id.btnText;
      View btnText = ViewBindings.findChildViewById(rootView, id);
      if (btnText == null) {
        break missingId;
      }

      id = R.id.btnUndo;
      Button btnUndo = ViewBindings.findChildViewById(rootView, id);
      if (btnUndo == null) {
        break missingId;
      }

      id = R.id.colorBlack;
      View colorBlack = ViewBindings.findChildViewById(rootView, id);
      if (colorBlack == null) {
        break missingId;
      }

      id = R.id.colorBlue;
      View colorBlue = ViewBindings.findChildViewById(rootView, id);
      if (colorBlue == null) {
        break missingId;
      }

      id = R.id.colorCyan;
      View colorCyan = ViewBindings.findChildViewById(rootView, id);
      if (colorCyan == null) {
        break missingId;
      }

      id = R.id.colorDarkGray;
      View colorDarkGray = ViewBindings.findChildViewById(rootView, id);
      if (colorDarkGray == null) {
        break missingId;
      }

      id = R.id.colorGray;
      View colorGray = ViewBindings.findChildViewById(rootView, id);
      if (colorGray == null) {
        break missingId;
      }

      id = R.id.colorGreen;
      View colorGreen = ViewBindings.findChildViewById(rootView, id);
      if (colorGreen == null) {
        break missingId;
      }

      id = R.id.colorLightGray;
      View colorLightGray = ViewBindings.findChildViewById(rootView, id);
      if (colorLightGray == null) {
        break missingId;
      }

      id = R.id.colorMagenta;
      View colorMagenta = ViewBindings.findChildViewById(rootView, id);
      if (colorMagenta == null) {
        break missingId;
      }

      id = R.id.colorOrange;
      View colorOrange = ViewBindings.findChildViewById(rootView, id);
      if (colorOrange == null) {
        break missingId;
      }

      id = R.id.colorPickerPanel;
      LinearLayout colorPickerPanel = ViewBindings.findChildViewById(rootView, id);
      if (colorPickerPanel == null) {
        break missingId;
      }

      id = R.id.colorPurple;
      View colorPurple = ViewBindings.findChildViewById(rootView, id);
      if (colorPurple == null) {
        break missingId;
      }

      id = R.id.colorRed;
      View colorRed = ViewBindings.findChildViewById(rootView, id);
      if (colorRed == null) {
        break missingId;
      }

      id = R.id.colorWhite;
      View colorWhite = ViewBindings.findChildViewById(rootView, id);
      if (colorWhite == null) {
        break missingId;
      }

      id = R.id.colorYellow;
      View colorYellow = ViewBindings.findChildViewById(rootView, id);
      if (colorYellow == null) {
        break missingId;
      }

      id = R.id.drawingView;
      ModernDrawingView drawingView = ViewBindings.findChildViewById(rootView, id);
      if (drawingView == null) {
        break missingId;
      }

      id = R.id.layerPanel;
      View layerPanel = ViewBindings.findChildViewById(rootView, id);
      if (layerPanel == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.selectedColorIndicator;
      View selectedColorIndicator = ViewBindings.findChildViewById(rootView, id);
      if (selectedColorIndicator == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityModernEditorBinding((ConstraintLayout) rootView, bottomToolbar,
          brushOpacitySlider, brushSettingsPanel, brushSizeSlider, btnAirbrush, btnBrush,
          btnBrushSettings, btnClear, btnColorPicker, btnEraser, btnHighlighter, btnLayers,
          btnMarker, btnPencil, btnRedo, btnSave, btnShapeCircle, btnShapeRect, btnSticker, btnText,
          btnUndo, colorBlack, colorBlue, colorCyan, colorDarkGray, colorGray, colorGreen,
          colorLightGray, colorMagenta, colorOrange, colorPickerPanel, colorPurple, colorRed,
          colorWhite, colorYellow, drawingView, layerPanel, progressBar, selectedColorIndicator,
          toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
