package drawing.jaraappskids.kidsdrawing.tools

import android.graphics.Color
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Comprehensive tests for Brush Tool functionality
 * Tests color selection, size adjustment, smoothness control, and stroke persistence
 */
@RunWith(RobolectricTestRunner::class)
class BrushToolTest : BaseDrawingTest() {

    @Test
    fun `test brush color selection validation`() {
        // Given: A palette of test colors
        val testColors = listOf(
            Color.RED, Color.GREEN, Color.BLUE, Color.YELLOW,
            Color.MAGENTA, Color.CYAN, Color.BLACK, Color.WHITE,
            Color.GRAY, Color.LTGRAY, Color.DKGRAY
        )
        
        testColors.forEach { expectedColor ->
            // When: Setting brush color
            assertNoExceptions {
                drawingView.setPaintColor(expectedColor)
            }

            // Then: Color should be set without exceptions
            // Note: DrawingView doesn't expose getter for color
        }
    }

    @Test
    fun `test brush size adjustment within valid range`() {
        // Given: Valid brush size range (10-30dp)
        val minSize = 10
        val maxSize = 30
        val testSizes = intArrayOf(10, 15, 20, 25, 30)

        testSizes.forEach { size ->
            // When: Setting brush size
            assertNoExceptions {
                drawingView.setPaintStrokeWidth(size)
            }

            // Then: Size should be set without errors
            assertThat(size).isAtLeast(minSize)
            assertThat(size).isAtMost(maxSize)
        }
    }

    @Test
    fun `test brush size boundary conditions`() {
        // Test minimum boundary
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(10)
        }

        // Test maximum boundary
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(30)
        }

        // Test values below minimum (should be handled gracefully)
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(5)
        }

        // Test values above maximum (should be handled gracefully)
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(50)
        }
    }

    @Test
    fun `test drawing view touch event handling`() {
        // Given: Drawing view ready for touch events
        val testPoints = TestUtils.createComplexDrawingPattern()

        // When: Simulating touch events
        assertNoExceptions {
            testPoints.take(5).forEachIndexed { index, (x, y) ->
                val action = when (index) {
                    0 -> android.view.MotionEvent.ACTION_DOWN
                    testPoints.size - 1 -> android.view.MotionEvent.ACTION_UP
                    else -> android.view.MotionEvent.ACTION_MOVE
                }

                val event = TestUtils.createMockMotionEvent(action, x, y)
                drawingView.onTouchEvent(event)
            }
        }

        // Then: Touch events should be handled without errors
    }

    @Test
    fun `test stroke color persistence during drawing`() {
        // Given: Specific brush configuration
        val testColor = Color.BLUE
        val testWidth = 15f

        setupDrawingView(testColor, testWidth, false)

        // When: Performing drawing operations
        simulateDrawing(10f, 10f, 50f, 50f)
        waitForDrawingCompletion()

        // Then: Drawing should complete without errors
        // Note: DrawingView doesn't expose color getter, so we can't verify persistence directly
    }

    @Test
    fun `test background color setting functionality`() {
        // Given: Various background colors
        val testColors = listOf(Color.WHITE, Color.LTGRAY, Color.TRANSPARENT)

        testColors.forEach { color ->
            // When: Setting background color
            assertNoExceptions {
                drawingView.setBackgroundColor(color)
            }

            // Then: Operation should complete without errors
        }
    }

    @Test
    fun `test multiple brush strokes with different settings`() {
        // Given: Multiple brush configurations
        val brushConfigs = listOf(
            Pair(Color.RED, 12),
            Pair(Color.GREEN, 18),
            Pair(Color.BLUE, 25),
            Pair(Color.YELLOW, 8)
        )

        brushConfigs.forEachIndexed { index, (color, width) ->
            // When: Applying brush configuration and drawing
            setupDrawingView(color, width.toFloat(), false)

            val startX = (index * 20f) % 100f
            val startY = (index * 25f) % 100f
            simulateDrawing(startX, startY, startX + 30f, startY + 30f)

            // Then: Drawing should complete without errors
            // Note: DrawingView doesn't expose color getter
        }
    }

    @Test
    fun `test brush performance with rapid strokes`() {
        // Given: Brush configured for performance testing
        setupDrawingView(Color.BLACK, 15f, false)
        
        // When: Performing rapid brush strokes
        val executionTime = measurePerformance {
            repeat(30) { i ->
                val startX = (i * 5f) % 150f
                val startY = (i * 7f) % 150f
                simulateDrawing(startX, startY, startX + 20f, startY + 20f, 5)
            }
        }
        
        // Then: Performance should be acceptable (under 1.5 seconds for 30 strokes)
        assertThat(executionTime).isLessThan(1500L)
    }

    @Test
    fun `test brush memory usage during extended drawing`() {
        // Given: Initial memory measurement
        val initialMemory = TestUtils.measureMemoryUsage()
        
        // When: Performing extended drawing session
        repeat(25) { i ->
            val color = Color.rgb((i * 10) % 255, (i * 15) % 255, (i * 20) % 255)
            val width = 10f + (i % 15)
            
            setupDrawingView(color, width, false)
            simulateDrawing(
                (i * 6f) % 120f,
                (i * 8f) % 120f,
                ((i + 5) * 6f) % 120f,
                ((i + 5) * 8f) % 120f
            )
        }
        
        // Then: Memory usage should remain reasonable
        val finalMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
        val maxAllowedIncrease = 20 * 1024 * 1024 // 20MB
        
        assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
    }

    @Test
    fun `test brush color accuracy with custom colors`() {
        // Given: Custom color values
        val customColors = intArrayOf(
            Color.argb(255, 128, 64, 192),  // Custom purple
            Color.argb(200, 255, 128, 0),   // Semi-transparent orange
            Color.argb(150, 0, 255, 128),   // Semi-transparent green
            Color.argb(100, 255, 0, 255)    // Semi-transparent magenta
        )
        
        customColors.forEach { customColor ->
            // When: Setting custom color
            drawingView.setPaintColor(customColor)

            // Then: Color should be set exactly as specified
            assertThat(DrawingView.mPaintColor).isEqualTo(customColor)
        }
    }

    @Test
    fun `test brush state consistency after undo operations`() {
        // Given: Brush with specific settings
        val originalColor = Color.CYAN
        val originalWidth = 20f

        setupDrawingView(originalColor, originalWidth, false)
        simulateDrawing()

        // When: Performing undo operation
        drawingView.undo()

        // Then: Brush color should remain unchanged
        assertThat(DrawingView.mPaintColor).isEqualTo(originalColor)
    }

    @Test
    fun `test brush redo functionality`() {
        // Given: Drawing view with drawing and undo performed
        setupDrawingView(Color.RED, 15f, false)
        simulateDrawing()
        drawingView.undo()

        // When: Performing redo operation
        assertNoExceptions {
            drawingView.redo()
        }

        // Then: Operation should complete without errors
    }
}
