<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_splash_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="215" endOffset="16"/></Target><Target id="@+id/cardAppIcon" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="85" startOffset="12" endLine="102" endOffset="47"/></Target><Target id="@+id/ivAppIcon" view="ImageView"><Expressions/><location startLine="94" startOffset="16" endLine="100" endOffset="52"/></Target><Target id="@+id/tvSplashTitle1" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="116" endOffset="51"/></Target><Target id="@+id/tvSplashTitle2" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="129" endOffset="51"/></Target><Target id="@+id/tvSplashEmojis" view="TextView"><Expressions/><location startLine="132" startOffset="12" endLine="139" endOffset="42"/></Target><Target id="@+id/tvLoadingText" view="TextView"><Expressions/><location startLine="152" startOffset="12" endLine="160" endOffset="52"/></Target><Target id="@+id/progressBar" view="View"><Expressions/><location startLine="172" startOffset="16" endLine="176" endOffset="69"/></Target><Target id="@+id/tvProgressPercent" view="TextView"><Expressions/><location startLine="181" startOffset="12" endLine="188" endOffset="42"/></Target><Target id="@+id/tvLoadingTips" view="TextView"><Expressions/><location startLine="191" startOffset="12" endLine="201" endOffset="42"/></Target><Target id="@+id/imgSplash" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="208" startOffset="4" endLine="213" endOffset="35"/></Target></Targets></Layout>