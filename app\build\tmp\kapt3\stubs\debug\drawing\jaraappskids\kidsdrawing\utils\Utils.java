package drawing.jaraappskids.kidsdrawing.utils;

/**
 * Created by Jr. Android Developer <PERSON><PERSON><PERSON> on 25-Jul-19.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ)\u0010\u0010\u001a\u00020\u00042\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0002\u0010\u0014J&\u0010\u0010\u001a\u0004\u0018\u00010\u00122\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0012J\u0010\u0010\u0015\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0016\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u000e\u0010\u0017\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ \u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u000e\u001a\u00020\u000fH\u0007J%\u0010\u001e\u001a\u00020\u00192\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0002\u0010\"J)\u0010#\u001a\u00020\u00192\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0002\u0010$J$\u0010#\u001a\u00020\u00192\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0012R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/utils/Utils;", "", "()V", "REQUEST_WRITE_STORAGE_REQUEST_CODE", "", "getREQUEST_WRITE_STORAGE_REQUEST_CODE", "()I", "setREQUEST_WRITE_STORAGE_REQUEST_CODE", "(I)V", "downloadClick", "itemClick", "saveClick", "checkPermission", "", "context", "Landroid/content/Context;", "getPref", "key", "", "value", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/Integer;)I", "hasReadPermissions", "hasWritePermissions", "isNetworkConnected", "loadBannerAd", "", "llAdView", "Landroid/widget/RelativeLayout;", "llAdViewFacebook", "Landroid/widget/LinearLayout;", "openInternetDialog", "callbackListener", "Ldrawing/jaraappskids/kidsdrawing/interfaces/CallbackListener;", "isSplash", "(Landroid/content/Context;Ldrawing/jaraappskids/kidsdrawing/interfaces/CallbackListener;Ljava/lang/Boolean;)V", "setPref", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/Integer;)V", "app_debug"})
public final class Utils {
    private static int REQUEST_WRITE_STORAGE_REQUEST_CODE = 111;
    private static int downloadClick = 0;
    private static int itemClick = 0;
    private static int saveClick = 0;
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.utils.Utils INSTANCE = null;
    
    private Utils() {
        super();
    }
    
    public final int getREQUEST_WRITE_STORAGE_REQUEST_CODE() {
        return 0;
    }
    
    public final void setREQUEST_WRITE_STORAGE_REQUEST_CODE(int p0) {
    }
    
    public final boolean checkPermission(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    private final boolean hasReadPermissions(android.content.Context context) {
        return false;
    }
    
    private final boolean hasWritePermissions(android.content.Context context) {
        return false;
    }
    
    public final void setPref(@org.jetbrains.annotations.Nullable()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPref(@org.jetbrains.annotations.Nullable()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String value) {
        return null;
    }
    
    public final void setPref(@org.jetbrains.annotations.Nullable()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.Integer value) {
    }
    
    public final int getPref(@org.jetbrains.annotations.Nullable()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.Integer value) {
        return 0;
    }
    
    @java.lang.Deprecated()
    public final void loadBannerAd(@org.jetbrains.annotations.NotNull()
    android.widget.RelativeLayout llAdView, @org.jetbrains.annotations.NotNull()
    android.widget.LinearLayout llAdViewFacebook, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final boolean isNetworkConnected(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    public final void openInternetDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.CallbackListener callbackListener, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isSplash) {
    }
}