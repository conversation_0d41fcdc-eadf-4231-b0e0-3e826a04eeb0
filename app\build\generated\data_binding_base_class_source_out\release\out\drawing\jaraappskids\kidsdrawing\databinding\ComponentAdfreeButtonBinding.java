// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ComponentAdfreeButtonBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAdfreeAction;

  @NonNull
  public final ImageView ivAdfreeIcon;

  @NonNull
  public final TextView tvAdfreeText;

  private ComponentAdfreeButtonBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnAdfreeAction, @NonNull ImageView ivAdfreeIcon,
      @NonNull TextView tvAdfreeText) {
    this.rootView = rootView;
    this.btnAdfreeAction = btnAdfreeAction;
    this.ivAdfreeIcon = ivAdfreeIcon;
    this.tvAdfreeText = tvAdfreeText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ComponentAdfreeButtonBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ComponentAdfreeButtonBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.component_adfree_button, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ComponentAdfreeButtonBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_adfree_action;
      Button btnAdfreeAction = ViewBindings.findChildViewById(rootView, id);
      if (btnAdfreeAction == null) {
        break missingId;
      }

      id = R.id.iv_adfree_icon;
      ImageView ivAdfreeIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAdfreeIcon == null) {
        break missingId;
      }

      id = R.id.tv_adfree_text;
      TextView tvAdfreeText = ViewBindings.findChildViewById(rootView, id);
      if (tvAdfreeText == null) {
        break missingId;
      }

      return new ComponentAdfreeButtonBinding((LinearLayout) rootView, btnAdfreeAction,
          ivAdfreeIcon, tvAdfreeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
