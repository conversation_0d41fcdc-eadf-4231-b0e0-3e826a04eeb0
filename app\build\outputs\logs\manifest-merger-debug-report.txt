-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:31:9-39:20
	android:grantUriPermissions
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:35:17-51
	android:authorities
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:33:17-64
	android:exported
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:34:17-41
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:32:17-66
manifest
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-114:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\b1d8742628e464c148a337029068b772\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7d140dd0c9249c4a749acac0a6ceebaa\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\003d3540143f1bd8ca35f691a36398bc\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:2:1-33:12
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\45e642a9cd8be02bb5cd930d88401dc3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\transforms-4\735751a767daaa7b2fdc35c57e2f1ca4\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0f27c9ac9bade141c33d2dd5b8f3b51e\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\80a8ef3b2117ea982238e32352f88523\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4138bbfd937e2ada2e439038e6cf17f\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb5cda3da2f0136ecb83c29cfb693f\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac103ed08faab1039137f104216888d5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d1bca2974b99b005821835f518f981b\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\e84c4b669318951efd6348f72ca36fa7\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4da5f4ffa871951c6204a2e4410160\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\7339612f73c68901ac70b40ad22d70ef\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d465a8673ed4187c6f67df5a33e6ac9a\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5d3b42c2e6090db29115414c27b81596\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9bee98f07083debb8de48b294ef94cb\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7869acd6a9d38a5d7a07ce0ef7f9244e\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec91eeb12da2185cfbd342d156fcbbe0\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a6f1908d75ee5644286b8cc0f9c8480\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\63e98a685ab6b18a757f49d3a19aa9e1\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.skydoves:colorpicker-compose:1.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\9d23e84fbdc5600c463366a6a5dcb169\transformed\jetified-colorpicker-compose-1.0.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f000eb6983c9e9ce3a56ee003917edea\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d985fd3374b6edf2346f386e02994e\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\88314a71b10c769ff42ce5957b26d018\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ee094baaab82e62b0c873b66da0425c\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\50671555c85f1ab9f3428115181fb6fa\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\057805e77f892585ad94e895ef1d0d41\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\269f3d8ffc39f596e6f02c0e0adfbb52\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\18aa5f4b9111f91bdcbe40e3ce47ca2a\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\79c513d3dd3a1d94e741cf2fa58ce02f\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cb66de09a1b452180d7399de0cee919\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\63b9c896b68ec31ade1ff37716f940c9\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\43fde4332a2b947447039e98ce947c98\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\61ab77ac73f341a2d1782fb97c5b4eb5\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\80ef300b80392ecf1f2915ce7a1fab2c\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\afe5bd7f0b53cd14d19aba1a5bd4257b\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c6eadba9bde2baf6eaeab6e68fe9fe54\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\e47fbe8612f947854a24ef739b60182b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\9b6243b20e3a4e41e3e6cbc9352db8f3\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\2507dffaec27660ae19740e0267a928c\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c08cf38c33743c88990fe1ac6c820e56\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b87d4f59fc34d5af52b70740df851f9\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\235640ffdab7baf676c504777a960dea\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbea484b52b67b463c43e7a15c35542a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbd9aba0a54976fd5d240b5813c6ee77\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5994bd70caefbba375c87bd18a9b7d8c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\99575feef4a1621588c34abc5737a569\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\2505c591238dc9b03ece86e2aded4553\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\f57fb5e397d6be3492af34f8b265e73e\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\29698c546c53ac08d5a67593cfe9052d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\82529481705794cccd426817f249ed52\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ada764ca619c3b5a4b784e79376ef696\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e3d7c1e2d3ca030fc0838bc9907c6c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\735b675a39ad36bb5ae97a912fb5b884\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7521680be541af6320a99da116e2513\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad84fa5d3f8494971e9d8679ac083f5b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4867a63d1d69a4de74aca3546ccc2b34\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c8882d088bfc70760b71de95339c343\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d13c7971678ad9c441d2b1a2e9d5da0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd1a40e2da14d8ab76f12e56ed3eedc\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fb8627e8cdb204fd4c449a6324955e\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccf8fe3b05321c8550a0a20761793146\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b856474d375103851e00b7b9115229b2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\929bb82f2a763f002424505f62cec30b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d8fafb799cfb3180e869eb3e9f85d960\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\a3a5238786212d5a0b767c1675035d29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\7dbd04a0b3799f003685737fd41ca68b\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\883d45bd03c4e36d2dbfc9e1206f5d8c\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6841bac5d819affb19206c109bcb90ae\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6f5cfd99c14f0252c3c0740c7b7c31e7\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2f5a7cfb52e5079d66e945aacb738274\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\941542af46e7dd084d6ac5ddc18365a6\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\18b8f8e0a09784d9d578dc13721dc4e7\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cc2950092fd45d1b45581588f3d0cfca\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cd3f538e6033e5b33c6a21020dd7f570\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\99bc75dc8be958e44cb15aeb31de9c60\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\582208939a7f9e008c1affded16328f7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c11738fc64f2f189b4ddbba178f4b23f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f054f207751e509fc21ad7948673043c\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\716b2c5b8a929578df530f9b93f7963d\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\79485e8f72c2dc4e2d906f357e25a672\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\e19cd58831c87baca57961d250216ded\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\426ffc714eb16024bfbf60d250abae5a\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c784cbf9b1154d2ea5775cc1f2aa099e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\54828ec404b296af0b5fbb8245abe8bb\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\ae2d176b9baabf1c8eb3ca11e6b75d01\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0530d8500249b34a433153ec18e8e1b\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a29f52d73d4f741cc688753c01f2a146\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\b832ec4d6277d64d47623e2799361d2c\transformed\documentfile-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c463affc8987aa9af8a3f5a234a982bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\897c0f34cf2556c7bb5441b897fe546d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\458d8656eff18adc92a7389b6d32ed6f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fa9d757cff19d1d5dfd9cff8b7f35c\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\db09acfe7dc58d950b142a12ffaa8459\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77381f507037f48ef60d0f2fb60760eb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18610d132223485bef2c91c4600b36b4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\349229a8dc8e7e20fcceb386149c0515\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce187cdf2bc0cd3c0e76a76ce154b041\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fc971b0942917083ae3506da0f7b860\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a1e3f598fcb0d93d905c819f7733e82\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf577476da8f7afce8b0b5c999e290f8\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:2:1-12:12
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69daf8ad0d55b180b3304970d58cbfd\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\8f24d881b887e09aa8ec18f8fb2e8904\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:3:9-55
	android:versionCode
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:5-78
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:19:5-79
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:19:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:5-66
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:18:5-67
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:18:5-67
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:5-78
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:13:5-79
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:13:5-79
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:5-10:38
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:25:5-80
	android:maxSdkVersion
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:5-12:38
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:26:5-81
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:26:5-81
	android:maxSdkVersion
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:22-78
application
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-112:19
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-112:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\003d3540143f1bd8ca35f691a36398bc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\003d3540143f1bd8ca35f691a36398bc\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:5-58
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:5-58
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:27:5-33:19
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:27:5-33:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\45e642a9cd8be02bb5cd930d88401dc3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\45e642a9cd8be02bb5cd930d88401dc3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\transforms-4\735751a767daaa7b2fdc35c57e2f1ca4\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:11:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\transforms-4\735751a767daaa7b2fdc35c57e2f1ca4\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5d3b42c2e6090db29115414c27b81596\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5d3b42c2e6090db29115414c27b81596\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9bee98f07083debb8de48b294ef94cb\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9bee98f07083debb8de48b294ef94cb\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec91eeb12da2185cfbd342d156fcbbe0\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec91eeb12da2185cfbd342d156fcbbe0\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c463affc8987aa9af8a3f5a234a982bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c463affc8987aa9af8a3f5a234a982bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:19:13-56
	tools:ignore
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:18-55
	android:roundIcon
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:21:13-58
	android:largeHeap
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:25:13-37
	android:icon
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:20:13-47
	android:fullBackupOnly
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:18:13-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:23:13-39
	android:label
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:22:13-45
	android:hardwareAccelerated
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:27:13-47
	android:fullBackupContent
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:17:13-46
	android:allowBackup
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:16:13-40
	android:theme
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:24:13-44
	tools:replace
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:28:13-48
	android:usesCleartextTraffic
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:26:13-48
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:15:13-47
activity#drawing.jaraappskids.kidsdrawing.activities.SplashActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:41:9-51:20
	android:exported
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:42:13-36
	android:theme
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:44:17-55
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:43:17-58
intent-filter#action:name:android.intent.action.MAIN+action:name:android.intent.action.VIEW+category:name:android.intent.category.LAUNCHER
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:45:13-50:29
action#android.intent.action.MAIN
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:17-68
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:25-66
action#android.intent.action.VIEW
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:17-76
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:27-74
activity#drawing.jaraappskids.kidsdrawing.activities.MainActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:53:9-56:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:55:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:54:17-56
activity#drawing.jaraappskids.kidsdrawing.activities.BGImageListActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:58:9-61:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:60:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:59:17-63
activity#drawing.jaraappskids.kidsdrawing.editor.ui.ModernEditorActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:73:9-79:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:75:17-53
	android:hardwareAccelerated
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:77:17-51
	android:windowSoftInputMode
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:76:17-60
	android:theme
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:78:17-76
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:74:17-63
activity#drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:81:9-83:55
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:83:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:82:17-54
activity#drawing.jaraappskids.kidsdrawing.mywork.FullScreenActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:84:9-86:55
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:86:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:85:17-58
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:88:9-90:65
	android:value
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:90:17-62
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:89:17-73
meta-data#com.google.android.gms.version
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:92:9-94:73
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:94:17-70
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:93:17-62
property#android.adservices.AD_SERVICES_CONFIG
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:96:9-99:48
	android:resource
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:98:13-59
	tools:replace
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:99:13-45
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:97:13-65
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:36:13-38:57
	android:resource
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:38:21-55
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:37:21-71
uses-sdk
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\b1d8742628e464c148a337029068b772\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\b1d8742628e464c148a337029068b772\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7d140dd0c9249c4a749acac0a6ceebaa\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7d140dd0c9249c4a749acac0a6ceebaa\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\003d3540143f1bd8ca35f691a36398bc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\003d3540143f1bd8ca35f691a36398bc\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:8:5-10:61
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:8:5-10:61
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\45e642a9cd8be02bb5cd930d88401dc3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\45e642a9cd8be02bb5cd930d88401dc3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\transforms-4\735751a767daaa7b2fdc35c57e2f1ca4\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\transforms-4\735751a767daaa7b2fdc35c57e2f1ca4\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0f27c9ac9bade141c33d2dd5b8f3b51e\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0f27c9ac9bade141c33d2dd5b8f3b51e\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\80a8ef3b2117ea982238e32352f88523\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\80a8ef3b2117ea982238e32352f88523\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4138bbfd937e2ada2e439038e6cf17f\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4138bbfd937e2ada2e439038e6cf17f\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb5cda3da2f0136ecb83c29cfb693f\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb5cda3da2f0136ecb83c29cfb693f\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac103ed08faab1039137f104216888d5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac103ed08faab1039137f104216888d5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d1bca2974b99b005821835f518f981b\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d1bca2974b99b005821835f518f981b\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\e84c4b669318951efd6348f72ca36fa7\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\e84c4b669318951efd6348f72ca36fa7\transformed\jetified-hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4da5f4ffa871951c6204a2e4410160\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4da5f4ffa871951c6204a2e4410160\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\7339612f73c68901ac70b40ad22d70ef\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\7339612f73c68901ac70b40ad22d70ef\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d465a8673ed4187c6f67df5a33e6ac9a\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d465a8673ed4187c6f67df5a33e6ac9a\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\21e77ea1dcfdfaa083b29a7f8ff6c694\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31113d4e2ab0eaa06a3835d2ef12087\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5d3b42c2e6090db29115414c27b81596\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5d3b42c2e6090db29115414c27b81596\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9bee98f07083debb8de48b294ef94cb\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9bee98f07083debb8de48b294ef94cb\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7869acd6a9d38a5d7a07ce0ef7f9244e\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7869acd6a9d38a5d7a07ce0ef7f9244e\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec91eeb12da2185cfbd342d156fcbbe0\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec91eeb12da2185cfbd342d156fcbbe0\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8784e12cfdf22977c2421790741b565f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a6f1908d75ee5644286b8cc0f9c8480\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a6f1908d75ee5644286b8cc0f9c8480\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\63e98a685ab6b18a757f49d3a19aa9e1\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\63e98a685ab6b18a757f49d3a19aa9e1\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.github.skydoves:colorpicker-compose:1.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\9d23e84fbdc5600c463366a6a5dcb169\transformed\jetified-colorpicker-compose-1.0.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.skydoves:colorpicker-compose:1.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\9d23e84fbdc5600c463366a6a5dcb169\transformed\jetified-colorpicker-compose-1.0.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f000eb6983c9e9ce3a56ee003917edea\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f000eb6983c9e9ce3a56ee003917edea\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d985fd3374b6edf2346f386e02994e\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d985fd3374b6edf2346f386e02994e\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\88314a71b10c769ff42ce5957b26d018\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\88314a71b10c769ff42ce5957b26d018\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ee094baaab82e62b0c873b66da0425c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ee094baaab82e62b0c873b66da0425c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\50671555c85f1ab9f3428115181fb6fa\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\50671555c85f1ab9f3428115181fb6fa\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\057805e77f892585ad94e895ef1d0d41\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\057805e77f892585ad94e895ef1d0d41\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\269f3d8ffc39f596e6f02c0e0adfbb52\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\269f3d8ffc39f596e6f02c0e0adfbb52\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\18aa5f4b9111f91bdcbe40e3ce47ca2a\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\18aa5f4b9111f91bdcbe40e3ce47ca2a\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\79c513d3dd3a1d94e741cf2fa58ce02f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\79c513d3dd3a1d94e741cf2fa58ce02f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cb66de09a1b452180d7399de0cee919\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cb66de09a1b452180d7399de0cee919\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\63b9c896b68ec31ade1ff37716f940c9\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\63b9c896b68ec31ade1ff37716f940c9\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\43fde4332a2b947447039e98ce947c98\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\43fde4332a2b947447039e98ce947c98\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\61ab77ac73f341a2d1782fb97c5b4eb5\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\61ab77ac73f341a2d1782fb97c5b4eb5\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\80ef300b80392ecf1f2915ce7a1fab2c\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\80ef300b80392ecf1f2915ce7a1fab2c\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\afe5bd7f0b53cd14d19aba1a5bd4257b\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\afe5bd7f0b53cd14d19aba1a5bd4257b\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c6eadba9bde2baf6eaeab6e68fe9fe54\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c6eadba9bde2baf6eaeab6e68fe9fe54\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\e47fbe8612f947854a24ef739b60182b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\e47fbe8612f947854a24ef739b60182b\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\9b6243b20e3a4e41e3e6cbc9352db8f3\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\9b6243b20e3a4e41e3e6cbc9352db8f3\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\2507dffaec27660ae19740e0267a928c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\2507dffaec27660ae19740e0267a928c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c08cf38c33743c88990fe1ac6c820e56\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c08cf38c33743c88990fe1ac6c820e56\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b87d4f59fc34d5af52b70740df851f9\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b87d4f59fc34d5af52b70740df851f9\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\235640ffdab7baf676c504777a960dea\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\235640ffdab7baf676c504777a960dea\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbea484b52b67b463c43e7a15c35542a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbea484b52b67b463c43e7a15c35542a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbd9aba0a54976fd5d240b5813c6ee77\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbd9aba0a54976fd5d240b5813c6ee77\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5994bd70caefbba375c87bd18a9b7d8c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5994bd70caefbba375c87bd18a9b7d8c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\99575feef4a1621588c34abc5737a569\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\99575feef4a1621588c34abc5737a569\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\2505c591238dc9b03ece86e2aded4553\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\2505c591238dc9b03ece86e2aded4553\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\f57fb5e397d6be3492af34f8b265e73e\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\f57fb5e397d6be3492af34f8b265e73e\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\29698c546c53ac08d5a67593cfe9052d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\29698c546c53ac08d5a67593cfe9052d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\82529481705794cccd426817f249ed52\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\82529481705794cccd426817f249ed52\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ada764ca619c3b5a4b784e79376ef696\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ada764ca619c3b5a4b784e79376ef696\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e3d7c1e2d3ca030fc0838bc9907c6c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e3d7c1e2d3ca030fc0838bc9907c6c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\735b675a39ad36bb5ae97a912fb5b884\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\735b675a39ad36bb5ae97a912fb5b884\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7521680be541af6320a99da116e2513\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\d7521680be541af6320a99da116e2513\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad84fa5d3f8494971e9d8679ac083f5b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad84fa5d3f8494971e9d8679ac083f5b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4867a63d1d69a4de74aca3546ccc2b34\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4867a63d1d69a4de74aca3546ccc2b34\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c8882d088bfc70760b71de95339c343\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c8882d088bfc70760b71de95339c343\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d13c7971678ad9c441d2b1a2e9d5da0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d13c7971678ad9c441d2b1a2e9d5da0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd1a40e2da14d8ab76f12e56ed3eedc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd1a40e2da14d8ab76f12e56ed3eedc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fb8627e8cdb204fd4c449a6324955e\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fb8627e8cdb204fd4c449a6324955e\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccf8fe3b05321c8550a0a20761793146\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ccf8fe3b05321c8550a0a20761793146\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b856474d375103851e00b7b9115229b2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b856474d375103851e00b7b9115229b2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\929bb82f2a763f002424505f62cec30b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\929bb82f2a763f002424505f62cec30b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d8fafb799cfb3180e869eb3e9f85d960\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d8fafb799cfb3180e869eb3e9f85d960\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\a3a5238786212d5a0b767c1675035d29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\a3a5238786212d5a0b767c1675035d29\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\7dbd04a0b3799f003685737fd41ca68b\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\7dbd04a0b3799f003685737fd41ca68b\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\883d45bd03c4e36d2dbfc9e1206f5d8c\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\883d45bd03c4e36d2dbfc9e1206f5d8c\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6841bac5d819affb19206c109bcb90ae\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6841bac5d819affb19206c109bcb90ae\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6f5cfd99c14f0252c3c0740c7b7c31e7\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\6f5cfd99c14f0252c3c0740c7b7c31e7\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2f5a7cfb52e5079d66e945aacb738274\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2f5a7cfb52e5079d66e945aacb738274\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\941542af46e7dd084d6ac5ddc18365a6\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\941542af46e7dd084d6ac5ddc18365a6\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\18b8f8e0a09784d9d578dc13721dc4e7\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\18b8f8e0a09784d9d578dc13721dc4e7\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cc2950092fd45d1b45581588f3d0cfca\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cc2950092fd45d1b45581588f3d0cfca\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cd3f538e6033e5b33c6a21020dd7f570\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\cd3f538e6033e5b33c6a21020dd7f570\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\99bc75dc8be958e44cb15aeb31de9c60\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\99bc75dc8be958e44cb15aeb31de9c60\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\582208939a7f9e008c1affded16328f7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\582208939a7f9e008c1affded16328f7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c11738fc64f2f189b4ddbba178f4b23f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\c11738fc64f2f189b4ddbba178f4b23f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f054f207751e509fc21ad7948673043c\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\f054f207751e509fc21ad7948673043c\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\716b2c5b8a929578df530f9b93f7963d\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\716b2c5b8a929578df530f9b93f7963d\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\79485e8f72c2dc4e2d906f357e25a672\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\79485e8f72c2dc4e2d906f357e25a672\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\e19cd58831c87baca57961d250216ded\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\e19cd58831c87baca57961d250216ded\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\426ffc714eb16024bfbf60d250abae5a\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\426ffc714eb16024bfbf60d250abae5a\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c784cbf9b1154d2ea5775cc1f2aa099e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c784cbf9b1154d2ea5775cc1f2aa099e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\54828ec404b296af0b5fbb8245abe8bb\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\54828ec404b296af0b5fbb8245abe8bb\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\ae2d176b9baabf1c8eb3ca11e6b75d01\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\ae2d176b9baabf1c8eb3ca11e6b75d01\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0530d8500249b34a433153ec18e8e1b\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0530d8500249b34a433153ec18e8e1b\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a29f52d73d4f741cc688753c01f2a146\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a29f52d73d4f741cc688753c01f2a146\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\b832ec4d6277d64d47623e2799361d2c\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\b832ec4d6277d64d47623e2799361d2c\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c463affc8987aa9af8a3f5a234a982bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c463affc8987aa9af8a3f5a234a982bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\897c0f34cf2556c7bb5441b897fe546d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\897c0f34cf2556c7bb5441b897fe546d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\458d8656eff18adc92a7389b6d32ed6f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\458d8656eff18adc92a7389b6d32ed6f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fa9d757cff19d1d5dfd9cff8b7f35c\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fa9d757cff19d1d5dfd9cff8b7f35c\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\db09acfe7dc58d950b142a12ffaa8459\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\db09acfe7dc58d950b142a12ffaa8459\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77381f507037f48ef60d0f2fb60760eb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77381f507037f48ef60d0f2fb60760eb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18610d132223485bef2c91c4600b36b4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18610d132223485bef2c91c4600b36b4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\349229a8dc8e7e20fcceb386149c0515\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\349229a8dc8e7e20fcceb386149c0515\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce187cdf2bc0cd3c0e76a76ce154b041\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce187cdf2bc0cd3c0e76a76ce154b041\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fc971b0942917083ae3506da0f7b860\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fc971b0942917083ae3506da0f7b860\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a1e3f598fcb0d93d905c819f7733e82\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a1e3f598fcb0d93d905c819f7733e82\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf577476da8f7afce8b0b5c999e290f8\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf577476da8f7afce8b0b5c999e290f8\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69daf8ad0d55b180b3304970d58cbfd\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69daf8ad0d55b180b3304970d58cbfd\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\8f24d881b887e09aa8ec18f8fb2e8904\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\8f24d881b887e09aa8ec18f8fb2e8904\transformed\jetified-dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	tools:overrideLibrary
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:10:9-58
	android:targetSdkVersion
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:22-79
uses-permission#android.permission.AD_SERVICES_CONFIG
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:5-77
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:22-74
queries
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:18:5-29:15
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:21:5-25:15
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:21:5-25:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:19:9-23:18
data
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
	android:scheme
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:24:9-28:18
uses-feature#android.software.leanback
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:11:5-13:36
	android:required
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:13:9-33
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:12:9-49
uses-feature#android.hardware.touchscreen
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:16:9-33
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:15:9-52
package#com.google.android.apps.tv.launcherx
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:9-72
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:18-69
package#com.google.android.tvlauncher
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:9-65
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:18-62
package#com.google.android.tvrecommendations
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:9-72
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:18-69
activity#com.google.android.tv.ads.controls.FallbackImageActivity
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:28:9-32:20
	android:exported
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:31:13-75
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:29:13-84
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c19ad927ba24e79f816fa13ebe3071ca\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:22-74
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:28-81
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:27-73
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:9:13-64
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
