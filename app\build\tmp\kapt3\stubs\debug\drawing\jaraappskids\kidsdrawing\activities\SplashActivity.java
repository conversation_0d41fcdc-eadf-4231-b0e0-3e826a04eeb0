package drawing.jaraappskids.kidsdrawing.activities;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\t\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003B\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0016\u001a\u00020\u0017H\u0016J\b\u0010\u0018\u001a\u00020\u0017H\u0016J\b\u0010\u0019\u001a\u00020\u0017H\u0002J\b\u0010\u001a\u001a\u00020\u0017H\u0002J\b\u0010\u001b\u001a\u00020\u0017H\u0002J\b\u0010\u001c\u001a\u00020\u0017H\u0002J\u0006\u0010\u001d\u001a\u00020\u0017J\b\u0010\u001e\u001a\u00020\u0017H\u0002J\b\u0010\u001f\u001a\u00020\u0017H\u0002J\b\u0010 \u001a\u00020\u0017H\u0016J\u0012\u0010!\u001a\u00020\u00172\b\u0010\"\u001a\u0004\u0018\u00010#H\u0014J\b\u0010$\u001a\u00020\u0017H\u0014J\b\u0010%\u001a\u00020\u0017H\u0016J\b\u0010&\u001a\u00020\u0017H\u0016J\b\u0010\'\u001a\u00020\u0017H\u0014J\b\u0010(\u001a\u00020\u0017H\u0016J\u0010\u0010)\u001a\u00020\u00172\u0006\u0010*\u001a\u00020+H\u0002J\b\u0010,\u001a\u00020\u0017H\u0016J\b\u0010-\u001a\u00020\u0017H\u0002J\b\u0010.\u001a\u00020\u0017H\u0002J\b\u0010/\u001a\u00020\u0017H\u0002J\u0010\u00100\u001a\u00020\u00172\u0006\u00101\u001a\u00020\u0012H\u0002J\u0010\u00102\u001a\u00020\u00172\u0006\u00101\u001a\u00020\u0012H\u0002J\u0010\u00103\u001a\u00020\u00172\u0006\u00101\u001a\u00020\u0012H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/activities/SplashActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Ldrawing/jaraappskids/kidsdrawing/interfaces/CallbackListener;", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdsCallback;", "()V", "cardAppIcon", "Landroidx/cardview/widget/CardView;", "handler", "Landroid/os/Handler;", "isLoaded", "", "myRunnable", "Ljava/lang/Runnable;", "progressAnimator", "Landroid/animation/ValueAnimator;", "progressBar", "Landroid/view/View;", "progressValue", "", "tvLoadingText", "Landroid/widget/TextView;", "tvProgressPercent", "adClose", "", "adLoadingFailed", "animateAppIcon", "animateEmojiBreathing", "animateLoadingText", "animateTitleText", "callApi", "checkAd", "initViews", "onCancel", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onLoaded", "onRetry", "onStop", "onSuccess", "startNextActivity", "time", "", "startNextScreen", "startProgressAnimation", "startSplashAnimations", "successCall", "updateLoadingText", "progress", "updateLoadingTips", "updateProgress", "app_debug"})
public final class SplashActivity extends androidx.appcompat.app.AppCompatActivity implements drawing.jaraappskids.kidsdrawing.interfaces.CallbackListener, drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback {
    private boolean isLoaded = false;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler handler = null;
    private int progressValue = 0;
    @org.jetbrains.annotations.Nullable()
    private android.animation.ValueAnimator progressAnimator;
    private android.view.View progressBar;
    private android.widget.TextView tvProgressPercent;
    private android.widget.TextView tvLoadingText;
    private androidx.cardview.widget.CardView cardAppIcon;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Runnable myRunnable = null;
    
    public SplashActivity() {
        super();
    }
    
    private final void startNextActivity(long time) {
    }
    
    public final void callApi() {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void startSplashAnimations() {
    }
    
    private final void animateAppIcon() {
    }
    
    private final void animateTitleText() {
    }
    
    private final void animateLoadingText() {
    }
    
    private final void animateEmojiBreathing() {
    }
    
    private final void startProgressAnimation() {
    }
    
    private final void updateProgress(int progress) {
    }
    
    private final void updateLoadingText(int progress) {
    }
    
    private final void updateLoadingTips(int progress) {
    }
    
    @java.lang.Override()
    public void onSuccess() {
    }
    
    @java.lang.Override()
    public void onCancel() {
    }
    
    @java.lang.Override()
    public void onRetry() {
    }
    
    private final void successCall() {
    }
    
    private final void checkAd() {
    }
    
    @java.lang.Override()
    public void adLoadingFailed() {
    }
    
    @java.lang.Override()
    public void adClose() {
    }
    
    @java.lang.Override()
    public void startNextScreen() {
    }
    
    @java.lang.Override()
    public void onLoaded() {
    }
    
    @java.lang.Override()
    protected void onStop() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    @java.lang.Override()
    public void onAdClicked() {
    }
    
    @java.lang.Override()
    public void onAdFailedToShow(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    @java.lang.Override()
    public void onAdImpression() {
    }
    
    @java.lang.Override()
    public void onAdShown() {
    }
    
    @java.lang.Override()
    public void onRewardEarned(@org.jetbrains.annotations.NotNull()
    java.lang.String type, int amount) {
    }
}