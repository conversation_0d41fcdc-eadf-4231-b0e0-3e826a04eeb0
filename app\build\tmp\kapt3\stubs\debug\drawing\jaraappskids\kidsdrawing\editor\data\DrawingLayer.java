package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0018\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BO\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\bH\u00c6\u0003J\t\u0010!\u001a\u00020\nH\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\t\u0010#\u001a\u00020\u000fH\u00c6\u0003JU\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\t\u0010%\u001a\u00020\u000fH\u00d6\u0001J\u0013\u0010&\u001a\u00020\u00062\b\u0010\'\u001a\u0004\u0018\u00010(H\u00d6\u0003J\t\u0010)\u001a\u00020\u000fH\u00d6\u0001J\t\u0010*\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020.2\u0006\u0010/\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u00060"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "Landroid/os/Parcelable;", "id", "", "name", "isVisible", "", "opacity", "", "blendMode", "Ldrawing/jaraappskids/kidsdrawing/editor/data/BlendMode;", "strokes", "", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "order", "", "(Ljava/lang/String;Ljava/lang/String;ZFLdrawing/jaraappskids/kidsdrawing/editor/data/BlendMode;Ljava/util/List;I)V", "getBlendMode", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/BlendMode;", "getId", "()Ljava/lang/String;", "()Z", "getName", "getOpacity", "()F", "getOrder", "()I", "getStrokes", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class DrawingLayer implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    private final boolean isVisible = false;
    private final float opacity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.BlendMode blendMode = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke> strokes = null;
    private final int order = 0;
    
    public DrawingLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, boolean isVisible, float opacity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BlendMode blendMode, @org.jetbrains.annotations.NotNull()
    java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke> strokes, int order) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final float getOpacity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.BlendMode getBlendMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke> getStrokes() {
        return null;
    }
    
    public final int getOrder() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.BlendMode component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke> component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, boolean isVisible, float opacity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BlendMode blendMode, @org.jetbrains.annotations.NotNull()
    java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke> strokes, int order) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}