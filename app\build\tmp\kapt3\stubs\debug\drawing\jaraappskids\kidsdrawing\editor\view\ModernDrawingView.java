package drawing.jaraappskids.kidsdrawing.editor.view;

/**
 * Modern drawing view with hardware acceleration and smooth performance
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0012\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ \u0010,\u001a\u00020 2\u0006\u0010-\u001a\u00020\u001c2\u0006\u0010.\u001a\u00020\u001c2\u0006\u0010/\u001a\u00020\u001cH\u0002J\u0016\u00100\u001a\u00020\u00122\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u001502H\u0002J\u0018\u00103\u001a\u00020 2\u0006\u00104\u001a\u00020\f2\u0006\u00105\u001a\u000206H\u0002J\u0018\u00107\u001a\u00020 2\u0006\u00104\u001a\u00020\f2\u0006\u00108\u001a\u00020&H\u0002J \u00109\u001a\u00020 2\u0006\u0010-\u001a\u00020\u001c2\u0006\u0010.\u001a\u00020\u001c2\u0006\u0010/\u001a\u00020\u001cH\u0002J\u0010\u0010:\u001a\u00020\u00192\u0006\u0010;\u001a\u00020<H\u0002J\b\u0010=\u001a\u00020 H\u0014J\u0010\u0010>\u001a\u00020 2\u0006\u00104\u001a\u00020\fH\u0014J(\u0010?\u001a\u00020 2\u0006\u0010@\u001a\u00020\u00072\u0006\u0010A\u001a\u00020\u00072\u0006\u0010B\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u0007H\u0014J\u0010\u0010D\u001a\u00020\u00192\u0006\u0010;\u001a\u00020<H\u0016J\u0010\u0010E\u001a\u00020 2\u0006\u00104\u001a\u00020\fH\u0002J\u0006\u0010F\u001a\u00020 J\u000e\u0010G\u001a\u00020 2\u0006\u0010H\u001a\u00020\u000eJ\u000e\u0010I\u001a\u00020 2\u0006\u00104\u001a\u00020\u0017J\u000e\u0010J\u001a\u00020 2\u0006\u0010K\u001a\u00020*J \u0010L\u001a\u00020 2\u0006\u0010-\u001a\u00020\u001c2\u0006\u0010.\u001a\u00020\u001c2\u0006\u0010/\u001a\u00020\u001cH\u0002J\b\u0010M\u001a\u00020 H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R(\u0010\u001e\u001a\u0010\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020 \u0018\u00010\u001fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\"\"\u0004\b#\u0010$R(\u0010%\u001a\u0010\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020 \u0018\u00010\u001fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010\"\"\u0004\b(\u0010$R\u000e\u0010)\u001a\u00020*X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006N"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/view/ModernDrawingView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "backgroundPaint", "Landroid/graphics/Paint;", "bitmapCanvas", "Landroid/graphics/Canvas;", "brushSettings", "Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushSettings;", "canvasBitmap", "Landroid/graphics/Bitmap;", "currentPath", "Landroid/graphics/Path;", "currentStroke", "", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingPoint;", "drawingCanvas", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;", "isDirty", "", "isDrawing", "lastX", "", "lastY", "onDrawingStateChanged", "Lkotlin/Function1;", "", "getOnDrawingStateChanged", "()Lkotlin/jvm/functions/Function1;", "setOnDrawingStateChanged", "(Lkotlin/jvm/functions/Function1;)V", "onStrokeCompleted", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "getOnStrokeCompleted", "setOnStrokeCompleted", "selectedTool", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "strokePaint", "continueDrawing", "x", "y", "pressure", "createSmoothPath", "points", "", "drawLayer", "canvas", "layer", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "drawStroke", "stroke", "finishDrawing", "handleDrawingTouch", "event", "Landroid/view/MotionEvent;", "onDetachedFromWindow", "onDraw", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "redrawCanvas", "refresh", "setBrushSettings", "settings", "setDrawingCanvas", "setSelectedTool", "tool", "startDrawing", "updatePaintFromBrush", "app_debug"})
public final class ModernDrawingView extends android.view.View {
    @org.jetbrains.annotations.Nullable()
    private drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas drawingCanvas;
    @org.jetbrains.annotations.NotNull()
    private drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings brushSettings;
    @org.jetbrains.annotations.NotNull()
    private drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool selectedTool = drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool.BRUSH;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingPoint> currentStroke;
    private boolean isDrawing = false;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint strokePaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint backgroundPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Path currentPath = null;
    private float lastX = 0.0F;
    private float lastY = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap canvasBitmap;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Canvas bitmapCanvas;
    private boolean isDirty = true;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke, kotlin.Unit> onStrokeCompleted;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onDrawingStateChanged;
    
    @kotlin.jvm.JvmOverloads()
    public ModernDrawingView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public ModernDrawingView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public ModernDrawingView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke, kotlin.Unit> getOnStrokeCompleted() {
        return null;
    }
    
    public final void setOnStrokeCompleted(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<java.lang.Boolean, kotlin.Unit> getOnDrawingStateChanged() {
        return null;
    }
    
    public final void setOnDrawingStateChanged(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> p0) {
    }
    
    public final void setDrawingCanvas(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas) {
    }
    
    public final void setBrushSettings(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings settings) {
    }
    
    public final void setSelectedTool(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool tool) {
    }
    
    private final void updatePaintFromBrush() {
    }
    
    @java.lang.Override()
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    private final void redrawCanvas(android.graphics.Canvas canvas) {
    }
    
    private final void drawLayer(android.graphics.Canvas canvas, drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
    }
    
    private final void drawStroke(android.graphics.Canvas canvas, drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke) {
    }
    
    private final android.graphics.Path createSmoothPath(java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingPoint> points) {
        return null;
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    private final boolean handleDrawingTouch(android.view.MotionEvent event) {
        return false;
    }
    
    private final void startDrawing(float x, float y, float pressure) {
    }
    
    private final void continueDrawing(float x, float y, float pressure) {
    }
    
    private final void finishDrawing(float x, float y, float pressure) {
    }
    
    public final void refresh() {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
}