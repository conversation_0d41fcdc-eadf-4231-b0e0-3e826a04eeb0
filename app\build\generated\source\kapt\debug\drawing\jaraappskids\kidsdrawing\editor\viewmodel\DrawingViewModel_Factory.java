package drawing.jaraappskids.kidsdrawing.editor.viewmodel;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DrawingViewModel_Factory implements Factory<DrawingViewModel> {
  private final Provider<DrawingRepository> repositoryProvider;

  public DrawingViewModel_Factory(Provider<DrawingRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public DrawingViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static DrawingViewModel_Factory create(Provider<DrawingRepository> repositoryProvider) {
    return new DrawingViewModel_Factory(repositoryProvider);
  }

  public static DrawingViewModel newInstance(DrawingRepository repository) {
    return new DrawingViewModel(repository);
  }
}
