package drawing.jaraappskids.kidsdrawing.utils

import android.graphics.*
import android.view.MotionEvent
import android.view.View
import drawing.jaraappskids.kidsdrawing.custom.DrawingView
import org.mockito.Mockito
import kotlin.random.Random

/**
 * Comprehensive test utilities for Kids Drawing App testing
 * Provides helper methods for drawing simulation, bitmap comparison, and test data generation
 */
object TestUtils {

    /**
     * Creates a mock MotionEvent for touch simulation
     */
    fun createMockMotionEvent(
        action: Int,
        x: Float,
        y: Float,
        eventTime: Long = System.currentTimeMillis()
    ): MotionEvent {
        return Mockito.mock(MotionEvent::class.java).apply {
            Mockito.`when`(this.action).thenReturn(action)
            Mockito.`when`(this.x).thenReturn(x)
            Mockito.`when`(this.y).thenReturn(y)
            Mockito.`when`(this.eventTime).thenReturn(eventTime)
            Mockito.`when`(this.downTime).thenReturn(eventTime)
        }
    }

    /**
     * Simulates a complete drawing stroke with multiple touch points
     */
    fun simulateDrawingStroke(
        view: View,
        startX: Float,
        startY: Float,
        endX: Float,
        endY: Float,
        steps: Int = 10
    ): List<MotionEvent> {
        val events = mutableListOf<MotionEvent>()
        val baseTime = System.currentTimeMillis()

        // ACTION_DOWN
        events.add(createMockMotionEvent(MotionEvent.ACTION_DOWN, startX, startY, baseTime))

        // ACTION_MOVE events
        for (i in 1 until steps) {
            val progress = i.toFloat() / steps
            val x = startX + (endX - startX) * progress
            val y = startY + (endY - startY) * progress
            events.add(createMockMotionEvent(MotionEvent.ACTION_MOVE, x, y, baseTime + i * 16))
        }

        // ACTION_UP
        events.add(createMockMotionEvent(MotionEvent.ACTION_UP, endX, endY, baseTime + steps * 16))

        return events
    }

    /**
     * Creates a test bitmap with specific dimensions and color
     */
    fun createTestBitmap(
        width: Int = 100,
        height: Int = 100,
        color: Int = Color.WHITE
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        canvas.drawColor(color)
        return bitmap
    }

    /**
     * Compares two bitmaps for equality with tolerance
     */
    fun compareBitmaps(
        bitmap1: Bitmap,
        bitmap2: Bitmap,
        tolerance: Float = 0.95f
    ): Boolean {
        if (bitmap1.width != bitmap2.width || bitmap1.height != bitmap2.height) {
            return false
        }

        var matchingPixels = 0
        val totalPixels = bitmap1.width * bitmap1.height

        for (x in 0 until bitmap1.width) {
            for (y in 0 until bitmap1.height) {
                if (bitmap1.getPixel(x, y) == bitmap2.getPixel(x, y)) {
                    matchingPixels++
                }
            }
        }

        return (matchingPixels.toFloat() / totalPixels) >= tolerance
    }

    /**
     * Creates a test Path with random properties
     */
    fun createTestPath(): Path {
        return Path().apply {
            // Add some random path data
            moveTo(Random.nextFloat() * 100, Random.nextFloat() * 100)
            lineTo(Random.nextFloat() * 100, Random.nextFloat() * 100)
        }
    }

    /**
     * Generates test color palette
     */
    fun getTestColorPalette(): List<Int> {
        return listOf(
            Color.RED,
            Color.GREEN,
            Color.BLUE,
            Color.YELLOW,
            Color.MAGENTA,
            Color.CYAN,
            Color.BLACK,
            Color.WHITE
        )
    }

    /**
     * Creates test brush sizes array
     */
    fun getTestBrushSizes(): FloatArray {
        return floatArrayOf(1f, 5f, 10f, 15f, 20f, 25f, 30f)
    }

    /**
     * Measures memory usage for performance testing
     */
    fun measureMemoryUsage(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        return MemoryInfo(
            totalMemory = runtime.totalMemory(),
            freeMemory = runtime.freeMemory(),
            maxMemory = runtime.maxMemory(),
            usedMemory = runtime.totalMemory() - runtime.freeMemory()
        )
    }

    /**
     * Data class for memory information
     */
    data class MemoryInfo(
        val totalMemory: Long,
        val freeMemory: Long,
        val maxMemory: Long,
        val usedMemory: Long
    ) {
        fun getUsedMemoryMB(): Float = usedMemory / (1024f * 1024f)
        fun getAvailableMemoryMB(): Float = (maxMemory - usedMemory) / (1024f * 1024f)
    }

    /**
     * Waits for UI thread to complete operations
     */
    fun waitForUIThread(timeoutMs: Long = 1000) {
        val startTime = System.currentTimeMillis()
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            Thread.sleep(16) // ~60fps
        }
    }

    /**
     * Creates a complex drawing pattern for stress testing
     */
    fun createComplexDrawingPattern(): List<Pair<Float, Float>> {
        val points = mutableListOf<Pair<Float, Float>>()
        
        // Create a spiral pattern
        for (i in 0..360 step 5) {
            val angle = Math.toRadians(i.toDouble())
            val radius = i / 10f
            val x = 50f + (radius * Math.cos(angle)).toFloat()
            val y = 50f + (radius * Math.sin(angle)).toFloat()
            points.add(Pair(x, y))
        }
        
        return points
    }

    /**
     * Validates drawing view state
     */
    fun validateDrawingViewState(
        strokeColor: Int,
        strokeWidth: Float
    ): DrawingViewState {
        return DrawingViewState(
            strokeColor = strokeColor,
            strokeWidth = strokeWidth,
            timestamp = System.currentTimeMillis()
        )
    }

    /**
     * Data class for drawing view state validation
     */
    data class DrawingViewState(
        val strokeColor: Int,
        val strokeWidth: Float,
        val timestamp: Long
    )
}
