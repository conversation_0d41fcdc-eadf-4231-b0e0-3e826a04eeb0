package drawing.jaraappskids.kidsdrawing.activities;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003B\u0005\u00a2\u0006\u0002\u0010\u0004J\b\u0010\f\u001a\u00020\rH\u0002J\b\u0010\u000e\u001a\u00020\rH\u0002J\b\u0010\u000f\u001a\u00020\rH\u0002J\b\u0010\u0010\u001a\u00020\rH\u0002J\b\u0010\u0011\u001a\u00020\u0012H\u0002J\u0010\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u0012H\u0002J\u0010\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0012H\u0002J\b\u0010\u0017\u001a\u00020\u0012H\u0002J\b\u0010\u0018\u001a\u00020\rH\u0002J\b\u0010\u0019\u001a\u00020\rH\u0002J\b\u0010\u001a\u001a\u00020\rH\u0016J\u0012\u0010\u001b\u001a\u00020\r2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0012H\u0016J\u0012\u0010\u001d\u001a\u00020\r2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001fH\u0014J\b\u0010 \u001a\u00020\rH\u0014J\b\u0010!\u001a\u00020\rH\u0014J\b\u0010\"\u001a\u00020\rH\u0016J\b\u0010#\u001a\u00020\rH\u0016J\u0012\u0010$\u001a\u00020\r2\b\u0010\u0016\u001a\u0004\u0018\u00010\u0012H\u0002J\b\u0010%\u001a\u00020\rH\u0002J\b\u0010&\u001a\u00020\rH\u0002J\u0010\u0010\'\u001a\u00020\r2\b\u0010(\u001a\u0004\u0018\u00010)J\b\u0010*\u001a\u00020\rH\u0002J\b\u0010+\u001a\u00020\rH\u0002J\b\u0010,\u001a\u00020\rH\u0002J\b\u0010-\u001a\u00020\rH\u0002J\u0010\u0010.\u001a\u00020\r2\u0006\u0010/\u001a\u000200H\u0002J\u0018\u00101\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u00102\u001a\u000203H\u0002J\b\u00104\u001a\u00020\rH\u0002J \u00105\u001a\u00020\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u0002072\u0006\u00109\u001a\u00020\u0012H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006:"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/activities/MainActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Landroid/view/View$OnClickListener;", "Ldrawing/jaraappskids/kidsdrawing/interfaces/CallbackListener;", "()V", "binding", "Ldrawing/jaraappskids/kidsdrawing/databinding/ActivityMainBinding;", "clickSoundPlayer", "Landroid/media/MediaPlayer;", "magicalSoundPlayer", "vibrator", "Landroid/os/Vibrator;", "animateButtonEntries", "", "animateFloatingClouds", "animateTitleBounce", "animateTwinklingStars", "createMagicalSparkle", "Landroid/view/View;", "createMagicalSparkles", "centerView", "createSparkleEffect", "view", "createSparkleView", "initSoundAndHaptics", "initViews", "onCancel", "onClick", "v", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onPause", "onRetry", "onSuccess", "playClickFeedback", "playEmojiSurprise", "rateUs", "setAppAdId", "id", "", "shareApp", "showAdFreeDialog", "showEasyHardDialog", "startAnimations", "startBGImageListActivity", "isEasy", "", "startGentleFloatingAnimation", "index", "", "successCall", "updateAdFreeDialogContent", "tvAdFreeStatus", "Landroid/widget/TextView;", "tvTimeRemaining", "btnWatchAd", "app_debug"})
public final class MainActivity extends androidx.appcompat.app.AppCompatActivity implements android.view.View.OnClickListener, drawing.jaraappskids.kidsdrawing.interfaces.CallbackListener {
    private drawing.jaraappskids.kidsdrawing.databinding.ActivityMainBinding binding;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer clickSoundPlayer;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer magicalSoundPlayer;
    private android.os.Vibrator vibrator;
    
    public MainActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void successCall() {
    }
    
    public final void setAppAdId(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
    }
    
    private final void initViews() {
    }
    
    private final void initSoundAndHaptics() {
    }
    
    private final void startAnimations() {
    }
    
    private final void animateFloatingClouds() {
    }
    
    private final void animateTwinklingStars() {
    }
    
    private final void animateTitleBounce() {
    }
    
    private final void animateButtonEntries() {
    }
    
    private final void startGentleFloatingAnimation(android.view.View view, int index) {
    }
    
    @java.lang.Override()
    public void onClick(@org.jetbrains.annotations.Nullable()
    android.view.View v) {
    }
    
    private final void playEmojiSurprise() {
    }
    
    private final void createMagicalSparkles(android.view.View centerView) {
    }
    
    private final android.view.View createMagicalSparkle() {
        return null;
    }
    
    private final void showEasyHardDialog() {
    }
    
    private final void startBGImageListActivity(boolean isEasy) {
    }
    
    private final void showAdFreeDialog() {
    }
    
    private final void updateAdFreeDialogContent(android.widget.TextView tvAdFreeStatus, android.widget.TextView tvTimeRemaining, android.view.View btnWatchAd) {
    }
    
    private final void rateUs() {
    }
    
    private final void shareApp() {
    }
    
    @java.lang.Override()
    public void onSuccess() {
    }
    
    @java.lang.Override()
    public void onCancel() {
    }
    
    @java.lang.Override()
    public void onRetry() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    private final void playClickFeedback(android.view.View view) {
    }
    
    private final void createSparkleEffect(android.view.View view) {
    }
    
    private final android.view.View createSparkleView() {
        return null;
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
}