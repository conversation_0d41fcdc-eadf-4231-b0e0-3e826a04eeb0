@echo off
echo ========================================
echo Kids Drawing App - Testing Framework
echo ========================================
echo.

echo Compiling test sources...
call gradlew compileDebugUnitTestKotlin
if %ERRORLEVEL% neq 0 (
    echo ERROR: Test compilation failed!
    pause
    exit /b 1
)

echo.
echo Test compilation successful!
echo.

echo Running unit tests...
call gradlew testDebugUnitTest --continue
if %ERRORLEVEL% neq 0 (
    echo WARNING: Some unit tests failed. Check reports for details.
) else (
    echo All unit tests passed!
)

echo.
echo ========================================
echo Test execution completed!
echo ========================================
echo.
echo Test reports available at:
echo - Unit Tests: app\build\reports\tests\testDebugUnitTest\index.html
echo - Coverage: app\build\reports\jacoco\testDebugUnitTestCoverage\html\index.html
echo.
pause
