<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.2" type="partial_results">
    <map id="UnusedResources">
        <location id="R.color.isb_selector_tick_texts_color"
            file="${:indicatorseekbar*debug*MAIN*sourceProvider*0*resDir*0}/color/isb_selector_tick_texts_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="432"/>
        <entry
            name="model"
            string="attr[isb_max(D),isb_min(D),isb_progress(D),isb_progress_value_float(D),isb_seek_smoothly(D),isb_r2l(D),isb_ticks_count(D),isb_user_seekable(D),isb_clear_default_padding(D),isb_only_thumb_draggable(D),isb_show_indicator(D),isb_indicator_color(D),isb_indicator_text_color(D),isb_indicator_text_size(D),isb_indicator_content_layout(D),isb_indicator_top_content_layout(D),isb_track_background_size(D),isb_track_background_color(D),isb_track_progress_size(D),isb_track_progress_color(D),isb_track_rounded_corners(D),isb_thumb_text_color(D),isb_show_thumb_text(D),isb_thumb_size(D),isb_thumb_color(D),isb_thumb_drawable(D),isb_thumb_adjust_auto(D),isb_tick_marks_color(D),isb_tick_marks_size(D),isb_tick_marks_drawable(D),isb_tick_marks_ends_hide(D),isb_tick_marks_swept_hide(D),isb_show_tick_marks_type(D),isb_show_tick_texts(D),isb_tick_texts_color(D),isb_tick_texts_size(D),isb_tick_texts_array(D),isb_tick_texts_typeface(D)],color[isb_selector_tick_marks_color(D),isb_selector_tick_texts_color(D)],drawable[isb_indicator_rounded_corners(U),isb_indicator_square_corners(U)],id[indicator_container(U),isb_progress(U),indicator_arrow(U)],layout[isb_indicator(U)],styleable[IndicatorSeekBar(U),IndicatorSeekBar_isb_max(R),IndicatorSeekBar_isb_min(R),IndicatorSeekBar_isb_progress(R),IndicatorSeekBar_isb_progress_value_float(R),IndicatorSeekBar_isb_user_seekable(R),IndicatorSeekBar_isb_clear_default_padding(R),IndicatorSeekBar_isb_only_thumb_draggable(R),IndicatorSeekBar_isb_seek_smoothly(R),IndicatorSeekBar_isb_r2l(R),IndicatorSeekBar_isb_track_background_size(R),IndicatorSeekBar_isb_track_progress_size(R),IndicatorSeekBar_isb_track_background_color(R),IndicatorSeekBar_isb_track_progress_color(R),IndicatorSeekBar_isb_track_rounded_corners(R),IndicatorSeekBar_isb_thumb_size(R),IndicatorSeekBar_isb_thumb_drawable(R),IndicatorSeekBar_isb_thumb_adjust_auto(R),IndicatorSeekBar_isb_thumb_color(R),IndicatorSeekBar_isb_show_thumb_text(R),IndicatorSeekBar_isb_thumb_text_color(R),IndicatorSeekBar_isb_ticks_count(R),IndicatorSeekBar_isb_show_tick_marks_type(R),IndicatorSeekBar_isb_tick_marks_size(R),IndicatorSeekBar_isb_tick_marks_color(R),IndicatorSeekBar_isb_tick_marks_drawable(R),IndicatorSeekBar_isb_tick_marks_swept_hide(R),IndicatorSeekBar_isb_tick_marks_ends_hide(R),IndicatorSeekBar_isb_show_tick_texts(R),IndicatorSeekBar_isb_tick_texts_size(R),IndicatorSeekBar_isb_tick_texts_color(R),IndicatorSeekBar_isb_tick_texts_array(R),IndicatorSeekBar_isb_tick_texts_typeface(R),IndicatorSeekBar_isb_show_indicator(R),IndicatorSeekBar_isb_indicator_color(R),IndicatorSeekBar_isb_indicator_text_size(R),IndicatorSeekBar_isb_indicator_text_color(R),IndicatorSeekBar_isb_indicator_content_layout(R),IndicatorSeekBar_isb_indicator_top_content_layout(R)];;;;"/>
        <location id="R.color.isb_selector_tick_marks_color"
            file="${:indicatorseekbar*debug*MAIN*sourceProvider*0*resDir*0}/color/isb_selector_tick_marks_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="7"
            endColumn="12"
            endOffset="341"/>
    </map>

</incidents>
