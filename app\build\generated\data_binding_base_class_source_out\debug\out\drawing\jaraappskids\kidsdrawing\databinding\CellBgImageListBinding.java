// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import drawing.jaraappskids.kidsdrawing.custom.SquareCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CellBgImageListBinding implements ViewBinding {
  @NonNull
  private final SquareCardView rootView;

  @NonNull
  public final SquareCardView cvBGImage;

  @NonNull
  public final AppCompatImageView ivBGImage;

  private CellBgImageListBinding(@NonNull SquareCardView rootView,
      @NonNull SquareCardView cvBGImage, @NonNull AppCompatImageView ivBGImage) {
    this.rootView = rootView;
    this.cvBGImage = cvBGImage;
    this.ivBGImage = ivBGImage;
  }

  @Override
  @NonNull
  public SquareCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static CellBgImageListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CellBgImageListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.cell_bg_image_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CellBgImageListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      SquareCardView cvBGImage = (SquareCardView) rootView;

      id = R.id.ivBGImage;
      AppCompatImageView ivBGImage = ViewBindings.findChildViewById(rootView, id);
      if (ivBGImage == null) {
        break missingId;
      }

      return new CellBgImageListBinding((SquareCardView) rootView, cvBGImage, ivBGImage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
