<dependencies>
  <compile
      roots="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified@jar,androidx.databinding:viewbinding:8.5.2@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar,com.squareup.leakcanary:leakcanary-android:2.10@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.google.android.material:material:1.12.0@aar,com.github.QuadFlask:colorpicker:0.0.13@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,com.google.ads.interactivemedia.v3:interactivemedia:3.36.0@aar,androidx.preference:preference-ktx:1.2.1@aar,androidx.preference:preference:1.2.1@aar,androidx.preference:preference:1.2.1@aar,com.google.android.tv:tv-ads:1.0.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.github.bumptech.glide:glide:4.14.2@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,com.google.android.gms:play-services-ads:24.3.0@aar,com.google.android.gms:play-services-pal:20.0.1@aar,com.google.android.gms:play-services-ads-identifier:18.2.0@aar,com.google.android.gms:play-services-appset:16.0.2@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-ads-api:24.3.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment-ktx:1.3.6@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.activity:activity:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.media:media:1.7.0@aar,androidx.media:media:1.7.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.customview:customview:1.1.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.core:core-ktx:1.13.0@aar,androidx.loader:loader:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.work:work-runtime-ktx:2.9.1@aar,androidx.work:work-runtime:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar,com.squareup.leakcanary:leakcanary-object-watcher-android:2.10@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.cardview:cardview:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.14.2@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.android.datatransport:transport-api:3.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,:@@:indicatorseekbar::debug,com.github.yukuku:ambilwarna:2.0.1@aar,com.intuit.sdp:sdp-android:1.1.0@aar,org.jetbrains:annotations:23.0.0@jar,com.squareup.leakcanary:leakcanary-android-core:2.10@aar,com.squareup.leakcanary:shark-android:2.10@jar,com.squareup.leakcanary:shark:2.10@jar,com.squareup.leakcanary:shark-graph:2.10@jar,com.squareup.leakcanary:shark-hprof:2.10@jar,com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher:2.10@jar,com.squareup.leakcanary:leakcanary-android-utils:2.10@aar,com.squareup.leakcanary:shark-log:2.10@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.github.bumptech.glide:disklrucache:4.14.2@jar,com.github.bumptech.glide:annotations:4.14.2@jar,androidx.exifinterface:exifinterface:1.3.3@aar,javax.inject:javax.inject:1@jar">
    <dependency
        name="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified@jar"
        simpleName="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar"/>
    <dependency
        name="androidx.databinding:viewbinding:8.5.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.github.QuadFlask:colorpicker:0.0.13@aar"
        simpleName="com.github.QuadFlask:colorpicker"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.google.ads.interactivemedia.v3:interactivemedia:3.36.0@aar"
        simpleName="com.google.ads.interactivemedia.v3:interactivemedia"/>
    <dependency
        name="androidx.preference:preference-ktx:1.2.1@aar"
        simpleName="androidx.preference:preference-ktx"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="com.google.android.tv:tv-ads:1.0.0@aar"
        simpleName="com.google.android.tv:tv-ads"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.14.2@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-ads:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-pal:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-pal"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.2@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.3.6@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.14.2@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.0.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name=":@@:indicatorseekbar::debug"
        simpleName="artifacts::indicatorseekbar"/>
    <dependency
        name="com.github.yukuku:ambilwarna:2.0.1@aar"
        simpleName="com.github.yukuku:ambilwarna"/>
    <dependency
        name="com.intuit.sdp:sdp-android:1.1.0@aar"
        simpleName="com.intuit.sdp:sdp-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-core:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-core"/>
    <dependency
        name="com.squareup.leakcanary:shark-android:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-android"/>
    <dependency
        name="com.squareup.leakcanary:shark:2.10@jar"
        simpleName="com.squareup.leakcanary:shark"/>
    <dependency
        name="com.squareup.leakcanary:shark-graph:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-graph"/>
    <dependency
        name="com.squareup.leakcanary:shark-hprof:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-hprof"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-core"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher:2.10@jar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-utils:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-utils"/>
    <dependency
        name="com.squareup.leakcanary:shark-log:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-log"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.14.2@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.14.2@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.3@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </compile>
  <package
      roots="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified@jar,androidx.databinding:viewbinding:8.5.2@aar,com.squareup.leakcanary:leakcanary-android:2.10@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar,com.google.android.material:material:1.12.0@aar,com.google.ads.interactivemedia.v3:interactivemedia:3.36.0@aar,com.google.android.tv:tv-ads:1.0.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.github.QuadFlask:colorpicker:0.0.13@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.preference:preference-ktx:1.2.1@aar,androidx.preference:preference:1.2.1@aar,androidx.preference:preference:1.2.1@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.github.bumptech.glide:glide:4.14.2@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,com.google.android.gms:play-services-ads:24.3.0@aar,com.google.android.gms:play-services-pal:20.0.1@aar,com.google.android.gms:play-services-ads-identifier:18.2.0@aar,com.google.android.gms:play-services-appset:16.0.2@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-ads-api:24.3.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment-ktx:1.3.6@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.activity:activity:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.media:media:1.7.0@aar,androidx.media:media:1.7.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.core:core-ktx:1.13.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.work:work-runtime-ktx:2.9.1@aar,androidx.work:work-runtime:2.9.1@aar,androidx.loader:loader:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.window:window:1.0.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-service:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,com.squareup.leakcanary:leakcanary-android-core:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher-android:2.10@aar,com.squareup.leakcanary:plumber-android:2.10@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar,androidx.room:room-ktx:2.5.0@aar,androidx.room:room-runtime:2.5.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,com.squareup.leakcanary:shark-android:2.10@jar,com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10@aar,com.squareup.leakcanary:plumber-android-core:2.10@aar,androidx.sqlite:sqlite-framework:2.3.0@aar,androidx.collection:collection-ktx:1.1.0@jar,com.squareup.leakcanary:shark:2.10@jar,com.squareup.leakcanary:leakcanary-object-watcher:2.10@jar,com.squareup.leakcanary:leakcanary-android-utils:2.10@aar,com.squareup.curtains:curtains:1.2.4@aar,com.squareup.leakcanary:shark-graph:2.10@jar,com.squareup.leakcanary:shark-hprof:2.10@jar,com.squareup.leakcanary:shark-log:2.10@jar,androidx.sqlite:sqlite:2.3.0@aar,com.squareup.okio:okio:2.2.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.room:room-common:2.5.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.github.bumptech.glide:gifdecoder:4.14.2@aar,androidx.exifinterface:exifinterface:1.3.3@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.android.datatransport:transport-api:3.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0@jar,:@@:indicatorseekbar::debug,com.github.yukuku:ambilwarna:2.0.1@aar,com.intuit.sdp:sdp-android:1.1.0@aar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.google.guava:guava:31.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,com.github.bumptech.glide:disklrucache:4.14.2@jar,com.github.bumptech.glide:annotations:4.14.2@jar,org.jetbrains:annotations:23.0.0@jar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified@jar"
        simpleName="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar"/>
    <dependency
        name="androidx.databinding:viewbinding:8.5.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.google.ads.interactivemedia.v3:interactivemedia:3.36.0@aar"
        simpleName="com.google.ads.interactivemedia.v3:interactivemedia"/>
    <dependency
        name="com.google.android.tv:tv-ads:1.0.0@aar"
        simpleName="com.google.android.tv:tv-ads"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.github.QuadFlask:colorpicker:0.0.13@aar"
        simpleName="com.github.QuadFlask:colorpicker"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.preference:preference-ktx:1.2.1@aar"
        simpleName="androidx.preference:preference-ktx"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.14.2@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-ads:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-pal:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-pal"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.2@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.3.6@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-core:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-core"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android"/>
    <dependency
        name="com.squareup.leakcanary:plumber-android:2.10@aar"
        simpleName="com.squareup.leakcanary:plumber-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.5.0@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.squareup.leakcanary:shark-android:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-android"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-core"/>
    <dependency
        name="com.squareup.leakcanary:plumber-android-core:2.10@aar"
        simpleName="com.squareup.leakcanary:plumber-android-core"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="com.squareup.leakcanary:shark:2.10@jar"
        simpleName="com.squareup.leakcanary:shark"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher:2.10@jar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-utils:2.10@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-utils"/>
    <dependency
        name="com.squareup.curtains:curtains:1.2.4@aar"
        simpleName="com.squareup.curtains:curtains"/>
    <dependency
        name="com.squareup.leakcanary:shark-graph:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-graph"/>
    <dependency
        name="com.squareup.leakcanary:shark-hprof:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-hprof"/>
    <dependency
        name="com.squareup.leakcanary:shark-log:2.10@jar"
        simpleName="com.squareup.leakcanary:shark-log"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="com.squareup.okio:okio:2.2.2@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.14.2@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.3@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.0.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name=":@@:indicatorseekbar::debug"
        simpleName="artifacts::indicatorseekbar"/>
    <dependency
        name="com.github.yukuku:ambilwarna:2.0.1@aar"
        simpleName="com.github.yukuku:ambilwarna"/>
    <dependency
        name="com.intuit.sdp:sdp-android:1.1.0@aar"
        simpleName="com.intuit.sdp:sdp-android"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.14.2@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.14.2@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </package>
</dependencies>
