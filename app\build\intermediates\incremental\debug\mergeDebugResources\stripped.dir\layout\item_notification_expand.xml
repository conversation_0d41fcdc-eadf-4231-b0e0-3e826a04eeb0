<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="2dp">

    <FrameLayout
        android:id="@+id/flImageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/llImageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true">

            <ImageView
                android:id="@+id/notification_img"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/notification_img2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                android:visibility="gone" />

        </LinearLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/gardient_top_bottom"/>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/icon_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="5dp">

        <ImageView
            android:id="@+id/big_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:paddingLeft="4dp"
            android:paddingRight="6dp"
            android:paddingBottom="1dp"
            android:src="@mipmap/ic_launcher" />

        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:ellipsize="end"
            android:maxLines="2"

            android:textColor="@color/colorWhite"
            tools:text="Expand me to see a detailed message!" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/notification_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/flImageView"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="10dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingHorizontal="6dp">

        <TextView
            android:id="@+id/notification_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="3"

            android:textColor="@color/colorWhite" />

    </LinearLayout>

    <TextView
        android:id="@+id/timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:paddingTop="8dp"
        android:paddingRight="3.5dp"

        android:visibility="gone" />

</RelativeLayout>

