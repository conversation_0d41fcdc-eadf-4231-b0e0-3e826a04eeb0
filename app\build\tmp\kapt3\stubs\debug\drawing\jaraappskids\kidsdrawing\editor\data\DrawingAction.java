package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\n\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\fB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\n\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u00a8\u0006\u0017"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "", "()V", "AddLayer", "AddShape", "AddSticker", "AddStroke", "AddText", "Clear", "RemoveElement", "RemoveLayer", "SetActiveLayer", "UpdateLayer", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddShape;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddSticker;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddStroke;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddText;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$Clear;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$RemoveElement;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$RemoveLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$SetActiveLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$UpdateLayer;", "app_debug"})
public abstract class DrawingAction {
    
    private DrawingAction() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "layer", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;)V", "getLayer", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AddLayer extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer = null;
        
        public AddLayer(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer getLayer() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.AddLayer copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddShape;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "shape", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingShape;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingShape;)V", "getShape", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingShape;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AddShape extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape shape = null;
        
        public AddShape(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape shape) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape getShape() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.AddShape copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingShape shape) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddSticker;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "sticker", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingSticker;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingSticker;)V", "getSticker", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingSticker;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AddSticker extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker sticker = null;
        
        public AddSticker(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker sticker) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker getSticker() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.AddSticker copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingSticker sticker) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddStroke;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "stroke", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;)V", "getStroke", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingStroke;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AddStroke extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke = null;
        
        public AddStroke(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke getStroke() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.AddStroke copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingStroke stroke) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$AddText;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "text", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingText;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingText;)V", "getText", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingText;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AddText extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingText text = null;
        
        public AddText(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingText text) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingText getText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingText component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.AddText copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingText text) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$Clear;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "()V", "app_debug"})
    public static final class Clear extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        public static final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.Clear INSTANCE = null;
        
        private Clear() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$RemoveElement;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "elementId", "", "(Ljava/lang/String;)V", "getElementId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class RemoveElement extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String elementId = null;
        
        public RemoveElement(@org.jetbrains.annotations.NotNull()
        java.lang.String elementId) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getElementId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.RemoveElement copy(@org.jetbrains.annotations.NotNull()
        java.lang.String elementId) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$RemoveLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "layerId", "", "(Ljava/lang/String;)V", "getLayerId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class RemoveLayer extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String layerId = null;
        
        public RemoveLayer(@org.jetbrains.annotations.NotNull()
        java.lang.String layerId) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLayerId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.RemoveLayer copy(@org.jetbrains.annotations.NotNull()
        java.lang.String layerId) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$SetActiveLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "layerId", "", "(Ljava/lang/String;)V", "getLayerId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class SetActiveLayer extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String layerId = null;
        
        public SetActiveLayer(@org.jetbrains.annotations.NotNull()
        java.lang.String layerId) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLayerId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.SetActiveLayer copy(@org.jetbrains.annotations.NotNull()
        java.lang.String layerId) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction$UpdateLayer;", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "layer", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;)V", "getLayer", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class UpdateLayer extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction {
        @org.jetbrains.annotations.NotNull()
        private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer = null;
        
        public UpdateLayer(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer getLayer() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction.UpdateLayer copy(@org.jetbrains.annotations.NotNull()
        drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}