// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddTextBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatEditText etText;

  @NonNull
  public final AppCompatTextView tvCancel;

  @NonNull
  public final AppCompatTextView tvOK;

  private DialogAddTextBinding(@NonNull LinearLayout rootView, @NonNull AppCompatEditText etText,
      @NonNull AppCompatTextView tvCancel, @NonNull AppCompatTextView tvOK) {
    this.rootView = rootView;
    this.etText = etText;
    this.tvCancel = tvCancel;
    this.tvOK = tvOK;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddTextBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddTextBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_text, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddTextBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etText;
      AppCompatEditText etText = ViewBindings.findChildViewById(rootView, id);
      if (etText == null) {
        break missingId;
      }

      id = R.id.tvCancel;
      AppCompatTextView tvCancel = ViewBindings.findChildViewById(rootView, id);
      if (tvCancel == null) {
        break missingId;
      }

      id = R.id.tvOK;
      AppCompatTextView tvOK = ViewBindings.findChildViewById(rootView, id);
      if (tvOK == null) {
        break missingId;
      }

      return new DialogAddTextBinding((LinearLayout) rootView, etText, tvCancel, tvOK);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
