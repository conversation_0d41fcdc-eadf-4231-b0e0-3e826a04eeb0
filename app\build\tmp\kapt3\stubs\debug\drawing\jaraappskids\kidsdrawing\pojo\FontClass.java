package drawing.jaraappskids.kidsdrawing.pojo;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002R\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\t\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\u000e\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R\u001a\u0010\u0014\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019\u00a8\u0006\u001a"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/pojo/FontClass;", "", "()V", "fontTypeFace", "Landroid/graphics/Typeface;", "getFontTypeFace", "()Landroid/graphics/Typeface;", "setFontTypeFace", "(Landroid/graphics/Typeface;)V", "isSelected", "", "()Z", "setSelected", "(Z)V", "name", "", "getName", "()Ljava/lang/String;", "setName", "(Ljava/lang/String;)V", "pos", "", "getPos", "()I", "setPos", "(I)V", "app_debug"})
public final class FontClass {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String name = "";
    private int pos = 0;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Typeface fontTypeFace;
    private boolean isSelected = false;
    
    public FontClass() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    public final void setName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getPos() {
        return 0;
    }
    
    public final void setPos(int p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Typeface getFontTypeFace() {
        return null;
    }
    
    public final void setFontTypeFace(@org.jetbrains.annotations.Nullable()
    android.graphics.Typeface p0) {
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
}