package drawing.jaraappskids.kidsdrawing.magicbrush;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 C2\u00020\u0001:\u0001CB\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0018\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\"2\u0006\u0010\'\u001a\u00020\"H\u0002J\u0018\u0010(\u001a\u00020%2\u0006\u0010&\u001a\u00020\"2\u0006\u0010\'\u001a\u00020\"H\u0002J\b\u0010)\u001a\u00020%H\u0002J\u0006\u0010*\u001a\u00020%J\u0006\u0010+\u001a\u00020%J\u0006\u0010,\u001a\u00020%J\u0006\u0010-\u001a\u00020%J\u0016\u0010.\u001a\u00020\t2\u0006\u0010/\u001a\u00020\t2\u0006\u00100\u001a\u000201J(\u00102\u001a\u00020%2\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u00020\u00122\u0006\u00106\u001a\u00020\"2\u0006\u00107\u001a\u00020\"H\u0002J\b\u00108\u001a\u00020%H\u0014J\u0010\u00109\u001a\u00020:2\u0006\u0010;\u001a\u00020<H\u0017J\u0006\u0010=\u001a\u00020%J\u0012\u0010>\u001a\u00020%2\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0002J\u0012\u0010?\u001a\u00020%2\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0002J\u0016\u0010@\u001a\u00020%2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0002\u001a\u00020\u0003J\u0006\u0010A\u001a\u00020%J\u0006\u0010B\u001a\u00020%R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0002\u001a\u00020\u0003X\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00150\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0016\u001a\u00020\u0017X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u0019\"\u0004\b\u001a\u0010\u001bR\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00150\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/OverlayBrushView;", "Landroidx/appcompat/widget/AppCompatImageView;", "context", "Landroid/content/Context;", "attributeSet", "Landroid/util/AttributeSet;", "(Landroid/content/Context;Landroid/util/AttributeSet;)V", "arrOfBitmap", "Ljava/util/ArrayList;", "Landroid/graphics/Bitmap;", "brushEffectLoad", "Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad;", "brushEffectLoadList", "getContext$app_debug", "()Landroid/content/Context;", "setContext$app_debug", "(Landroid/content/Context;)V", "getRandomEffects", "Ldrawing/jaraappskids/kidsdrawing/magicbrush/GetRandomEffect;", "lists", "Ljava/util/Stack;", "", "pattern", "", "getPattern", "()Ljava/lang/String;", "setPattern", "(Ljava/lang/String;)V", "redoBrushEffectLoadList", "redoLists", "screenBitmap", "touchCount", "", "xlast", "", "ylast", "actionDownTouch", "", "x", "y", "actionMoveTouch", "addBrushBitmap", "cleanup", "clearMagicBrush", "clearRedoMagicBrush", "closeTouch", "makeTransparent", "bit", "transparentColor", "Landroid/graphics/Color;", "onCanvas", "canvas", "Landroid/graphics/Canvas;", "getRandomEffect", "width", "height", "onDetachedFromWindow", "onTouchEvent", "", "motionEvent", "Landroid/view/MotionEvent;", "redoMagicBrush", "setBrushRes", "setBrushResFoundMap", "setBrushStyle", "undoBrush", "undoMagicBrush", "Companion", "app_debug"})
public final class OverlayBrushView extends androidx.appcompat.widget.AppCompatImageView {
    @org.jetbrains.annotations.NotNull()
    private android.content.Context context;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap screenBitmap;
    @org.jetbrains.annotations.Nullable()
    private drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad brushEffectLoad;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad> brushEffectLoadList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.magicbrush.GetRandomEffect> getRandomEffects = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<android.graphics.Bitmap> arrOfBitmap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Stack<java.util.List<drawing.jaraappskids.kidsdrawing.magicbrush.GetRandomEffect>> lists = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Stack<java.util.List<drawing.jaraappskids.kidsdrawing.magicbrush.GetRandomEffect>> redoLists = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Stack<drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad> redoBrushEffectLoadList = null;
    public java.lang.String pattern;
    private float xlast = 0.0F;
    private float ylast = 0.0F;
    private int touchCount = 0;
    private static boolean onTouchOverlayWhenBrushView = false;
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.magicbrush.OverlayBrushView.Companion Companion = null;
    
    public OverlayBrushView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.util.AttributeSet attributeSet) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext$app_debug() {
        return null;
    }
    
    public final void setContext$app_debug(@org.jetbrains.annotations.NotNull()
    android.content.Context p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPattern() {
        return null;
    }
    
    public final void setPattern(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    private final void setBrushResFoundMap(drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad brushEffectLoad) {
    }
    
    private final void setBrushRes(drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad brushEffectLoad) {
    }
    
    @java.lang.Override()
    @android.annotation.SuppressLint(value = {"ClickableViewAccessibility"})
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent motionEvent) {
        return false;
    }
    
    private final void actionDownTouch(float x, float y) {
    }
    
    private final void actionMoveTouch(float x, float y) {
    }
    
    private final void addBrushBitmap() {
    }
    
    public final void undoMagicBrush() {
    }
    
    public final void redoMagicBrush() {
    }
    
    public final void clearRedoMagicBrush() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Bitmap makeTransparent(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bit, @org.jetbrains.annotations.NotNull()
    android.graphics.Color transparentColor) {
        return null;
    }
    
    private final void onCanvas(android.graphics.Canvas canvas, drawing.jaraappskids.kidsdrawing.magicbrush.GetRandomEffect getRandomEffect, float width, float height) {
    }
    
    public final void closeTouch() {
    }
    
    public final void setBrushStyle(@org.jetbrains.annotations.NotNull()
    java.lang.String pattern, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final void clearMagicBrush() {
    }
    
    public final void undoBrush() {
    }
    
    /**
     * Clean up all resources to prevent memory leaks
     */
    public final void cleanup() {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\b\u00a8\u0006\t"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/OverlayBrushView$Companion;", "", "()V", "onTouchOverlayWhenBrushView", "", "getOnTouchOverlayWhenBrushView", "()Z", "setOnTouchOverlayWhenBrushView", "(Z)V", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final boolean getOnTouchOverlayWhenBrushView() {
            return false;
        }
        
        public final void setOnTouchOverlayWhenBrushView(boolean p0) {
        }
    }
}