package drawing.jaraappskids.kidsdrawing.ads;

/**
 * Modern Ads Manager - Fresh and Clean Implementation
 * Features:
 * - Simple and efficient ad management
 * - All major ad types (Banner, Interstitial, Rewarded, App Open)
 * - Smart frequency management
 * - Modern lifecycle handling
 * - Memory leak prevention
 * - Analytics integration
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0016\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u00107\u001a\u00020\u0018J\b\u00108\u001a\u000209H\u0002J\u0006\u0010:\u001a\u000209J\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020=0<J\u0010\u0010>\u001a\u00020\u000e2\u0006\u0010?\u001a\u00020\u000eH\u0002J\b\u0010@\u001a\u0004\u0018\u00010$J\u0010\u0010A\u001a\u00020\u00042\u0006\u0010?\u001a\u00020\u000eH\u0002J\u0010\u0010B\u001a\u0002092\u0006\u0010?\u001a\u00020\u000eH\u0002J \u0010C\u001a\u0002092\u0006\u0010D\u001a\u00020E2\u0010\b\u0002\u0010F\u001a\n\u0012\u0004\u0012\u000209\u0018\u00010GJ\u0010\u0010H\u001a\u00020\u00182\u0006\u0010D\u001a\u00020EH\u0002J\u0006\u0010I\u001a\u00020\u0018J\u0006\u0010)\u001a\u00020\u0018J\u0006\u0010J\u001a\u00020\u0018J\u0006\u0010K\u001a\u00020\u0018J\b\u0010L\u001a\u00020\u0018H\u0002J\u0010\u0010M\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\u0018\u0010N\u001a\u0002092\b\u0010O\u001a\u0004\u0018\u00010P2\u0006\u0010D\u001a\u00020EJ\u0010\u0010Q\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\u0010\u0010R\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\u0010\u0010S\u001a\u0002092\u0006\u0010T\u001a\u00020\u000eH\u0002J\u0010\u0010U\u001a\u0002092\u0006\u0010V\u001a\u00020WH\u0016J\u0010\u0010X\u001a\u0002092\u0006\u0010V\u001a\u00020WH\u0016J\u0010\u0010Y\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\u0006\u0010Z\u001a\u000209J\u000e\u0010[\u001a\u0002092\u0006\u0010D\u001a\u00020EJ\u0006\u0010\\\u001a\u000209J\u0010\u0010]\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\b\u0010^\u001a\u000209H\u0002J\u0010\u0010_\u001a\u0002092\b\u0010`\u001a\u0004\u0018\u00010$J\u0010\u0010a\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\u001a\u0010b\u001a\u0002092\u0006\u0010`\u001a\u00020$2\n\b\u0002\u0010F\u001a\u0004\u0018\u00010\u001cJ\u0010\u0010c\u001a\u0002092\u0006\u0010`\u001a\u00020$H\u0002J\u0018\u0010d\u001a\u0002092\b\u0010O\u001a\u0004\u0018\u00010P2\u0006\u0010D\u001a\u00020EJ\u0016\u0010e\u001a\u0002092\u0006\u0010`\u001a\u00020$2\u0006\u0010f\u001a\u00020\u001cJ\u0016\u0010g\u001a\u0002092\u0006\u0010`\u001a\u00020$2\u0006\u0010f\u001a\u00020\u001cJ\u0010\u0010h\u001a\u0002092\u0006\u0010?\u001a\u00020\u000eH\u0002J\u0018\u0010i\u001a\u0002092\u0006\u0010?\u001a\u00020\u000e2\u0006\u0010j\u001a\u00020\u000eH\u0002J\u0010\u0010k\u001a\u0002092\u0006\u0010?\u001a\u00020\u000eH\u0002J\u0010\u0010l\u001a\u0002092\u0006\u0010?\u001a\u00020\u000eH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\"\u001a\n\u0012\u0004\u0012\u00020$\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\'\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00101\u001a\u0004\u0018\u000102X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00103\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006m"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/ads/ModernAdsManager;", "Landroidx/lifecycle/DefaultLifecycleObserver;", "()V", "MAX_APP_OPEN_PER_DAY", "", "MAX_INTERSTITIALS_PER_DAY", "MAX_REWARDED_PER_DAY", "MIN_AD_INTERVAL", "", "MIN_APP_OPEN_INTERVAL", "MIN_BACKGROUND_TIME", "MIN_INTERSTITIAL_INTERVAL", "MIN_REWARDED_INTERVAL", "PREF_AD_FREE_SESSION_START", "", "PREF_LAST_SESSION_END_TIME", "PREF_WAS_AD_FREE_ON_EXIT", "TAG", "TEST_APP_OPEN_ID", "TEST_BANNER_ID", "TEST_INTERSTITIAL_ID", "TEST_REWARDED_ID", "adFreeSessionStartTime", "appInBackground", "", "appOpenAd", "Lcom/google/android/gms/ads/appopen/AppOpenAd;", "appOpenCallback", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdsCallback;", "backgroundTime", "bannerAd", "Lcom/google/android/gms/ads/AdView;", "cooldownTimer", "Ljava/util/Timer;", "currentActivityRef", "Ljava/lang/ref/WeakReference;", "Landroid/app/Activity;", "interstitialAd", "Lcom/google/android/gms/ads/interstitial/InterstitialAd;", "interstitialCallback", "isAppOpenLoading", "isInitialized", "isInterstitialLoading", "isRewardedLoading", "isShowingAd", "lastAdShownTime", "lastAppOpenTime", "lastInterstitialTime", "lastRewardedTime", "rewardedAd", "Lcom/google/android/gms/ads/rewarded/RewardedAd;", "rewardedCallback", "sessionAdsShown", "sessionStartTime", "wasAdFreeWhenExited", "areAdsEnabled", "checkAdFreeExitReset", "", "cleanup", "getAdRevenue", "", "", "getAdUnitId", "adType", "getCurrentActivity", "getTodayAdCount", "incrementTodayAdCount", "initialize", "context", "Landroid/content/Context;", "callback", "Lkotlin/Function0;", "isAdsEnabled", "isAppOpenReady", "isInterstitialReady", "isRewardedReady", "isTestMode", "loadAppOpenAd", "loadBannerAd", "container", "Landroid/view/View;", "loadInterstitialAd", "loadRewardedAd", "logMessage", "message", "onStart", "owner", "Landroidx/lifecycle/LifecycleOwner;", "onStop", "preloadAds", "preloadInterstitialAd", "refreshAds", "refreshAllAds", "resetAdFreeProgress", "saveAdFreeExitState", "setCurrentActivity", "activity", "showAdFreeResetNotification", "showAppOpenAd", "showAppOpenAdAuto", "showBannerAd", "showInterstitialAd", "adsCallback", "showRewardedAd", "trackAdClicked", "trackAdFailed", "error", "trackAdLoaded", "trackAdShown", "app_debug"})
public final class ModernAdsManager implements androidx.lifecycle.DefaultLifecycleObserver {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ModernAdsManager";
    @org.jetbrains.annotations.Nullable()
    private static com.google.android.gms.ads.interstitial.InterstitialAd interstitialAd;
    @org.jetbrains.annotations.Nullable()
    private static com.google.android.gms.ads.rewarded.RewardedAd rewardedAd;
    @org.jetbrains.annotations.Nullable()
    private static com.google.android.gms.ads.appopen.AppOpenAd appOpenAd;
    @org.jetbrains.annotations.Nullable()
    private static com.google.android.gms.ads.AdView bannerAd;
    private static boolean isInterstitialLoading = false;
    private static boolean isRewardedLoading = false;
    private static boolean isAppOpenLoading = false;
    private static boolean isShowingAd = false;
    private static long lastAdShownTime = 0L;
    private static long lastInterstitialTime = 0L;
    private static long lastRewardedTime = 0L;
    private static long lastAppOpenTime = 0L;
    private static final long MIN_AD_INTERVAL = 5000L;
    private static final long MIN_INTERSTITIAL_INTERVAL = 30000L;
    private static final long MIN_REWARDED_INTERVAL = 3000L;
    private static final long MIN_APP_OPEN_INTERVAL = 60000L;
    private static final int MAX_INTERSTITIALS_PER_DAY = 25;
    private static final int MAX_REWARDED_PER_DAY = 15;
    private static final int MAX_APP_OPEN_PER_DAY = 10;
    private static boolean appInBackground = false;
    private static long backgroundTime = 0L;
    private static final long MIN_BACKGROUND_TIME = 4000L;
    private static long sessionStartTime;
    private static int sessionAdsShown = 0;
    private static boolean wasAdFreeWhenExited = false;
    private static long adFreeSessionStartTime = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_WAS_AD_FREE_ON_EXIT = "was_ad_free_on_exit";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_AD_FREE_SESSION_START = "ad_free_session_start";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_LAST_SESSION_END_TIME = "last_session_end_time";
    @org.jetbrains.annotations.Nullable()
    private static java.util.Timer cooldownTimer;
    @org.jetbrains.annotations.Nullable()
    private static drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback interstitialCallback;
    @org.jetbrains.annotations.Nullable()
    private static drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback rewardedCallback;
    @org.jetbrains.annotations.Nullable()
    private static drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback appOpenCallback;
    @org.jetbrains.annotations.Nullable()
    private static java.lang.ref.WeakReference<android.app.Activity> currentActivityRef;
    private static boolean isInitialized = false;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TEST_BANNER_ID = "ca-app-pub-3940256099942544/9214589741";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TEST_INTERSTITIAL_ID = "ca-app-pub-3940256099942544/1033173712";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TEST_REWARDED_ID = "ca-app-pub-3940256099942544/5224354917";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TEST_APP_OPEN_ID = "ca-app-pub-3940256099942544/9257395921";
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.ads.ModernAdsManager INSTANCE = null;
    
    private ModernAdsManager() {
        super();
    }
    
    /**
     * Initialize the ads system
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * Preload all ad types for better performance
     */
    private final void preloadAds(android.content.Context context) {
    }
    
    /**
     * Load interstitial ad
     */
    private final void loadInterstitialAd(android.content.Context context) {
    }
    
    /**
     * Show interstitial ad with smart frequency management
     */
    public final void showInterstitialAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    /**
     * Load rewarded ad
     */
    private final void loadRewardedAd(android.content.Context context) {
    }
    
    /**
     * Show rewarded ad
     */
    public final void showRewardedAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    /**
     * Load app open ad
     */
    private final void loadAppOpenAd(android.content.Context context) {
    }
    
    /**
     * Show app open ad automatically when app comes to foreground
     */
    private final void showAppOpenAdAuto(android.app.Activity activity) {
    }
    
    /**
     * Load banner ad into provided container
     */
    public final void loadBannerAd(@org.jetbrains.annotations.Nullable()
    android.view.View container, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @java.lang.Override()
    public void onStart(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.LifecycleOwner owner) {
    }
    
    @java.lang.Override()
    public void onStop(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.LifecycleOwner owner) {
    }
    
    /**
     * Save ad-free session state when app goes to background
     */
    private final void saveAdFreeExitState() {
    }
    
    /**
     * Check if user exited during ad-free earning and reset progress if needed
     */
    private final void checkAdFreeExitReset() {
    }
    
    /**
     * Reset ad-free progress as punishment for exiting
     */
    private final void resetAdFreeProgress(android.content.Context context) {
    }
    
    /**
     * Show notification to user about progress reset
     */
    private final void showAdFreeResetNotification(android.content.Context context) {
    }
    
    /**
     * Get appropriate ad unit ID (test or production)
     */
    private final java.lang.String getAdUnitId(java.lang.String adType) {
        return null;
    }
    
    /**
     * Check if we're in test mode
     */
    private final boolean isTestMode() {
        return false;
    }
    
    /**
     * Check if ads are enabled
     */
    private final boolean isAdsEnabled(android.content.Context context) {
        return false;
    }
    
    /**
     * Get today's ad count for specific type
     */
    private final int getTodayAdCount(java.lang.String adType) {
        return 0;
    }
    
    /**
     * Increment today's ad count for specific type
     */
    private final void incrementTodayAdCount(java.lang.String adType) {
    }
    
    private final void trackAdLoaded(java.lang.String adType) {
    }
    
    private final void trackAdShown(java.lang.String adType) {
    }
    
    private final void trackAdClicked(java.lang.String adType) {
    }
    
    private final void trackAdFailed(java.lang.String adType, java.lang.String error) {
    }
    
    /**
     * Check if ads are ready
     */
    public final boolean isInterstitialReady() {
        return false;
    }
    
    public final boolean isRewardedReady() {
        return false;
    }
    
    public final boolean isAppOpenReady() {
        return false;
    }
    
    /**
     * Preload interstitial ad for better performance
     */
    public final void preloadInterstitialAd() {
    }
    
    /**
     * Show banner ad in container
     */
    public final void showBannerAd(@org.jetbrains.annotations.Nullable()
    android.view.View container, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Show app open ad manually
     */
    public final void showAppOpenAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.Nullable()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback callback) {
    }
    
    /**
     * Set current activity for context
     */
    public final void setCurrentActivity(@org.jetbrains.annotations.Nullable()
    android.app.Activity activity) {
    }
    
    /**
     * Get current activity
     */
    @org.jetbrains.annotations.Nullable()
    public final android.app.Activity getCurrentActivity() {
        return null;
    }
    
    /**
     * Log message helper
     */
    private final void logMessage(java.lang.String message) {
    }
    
    /**
     * Force refresh ads
     */
    public final void refreshAds(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Clean up all ad resources when the app is being destroyed
     */
    public final void cleanup() {
    }
    
    /**
     * Get ad revenue tracking information
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getAdRevenue() {
        return null;
    }
    
    /**
     * Check if ads are enabled (for premium users or specific conditions)
     */
    public final boolean areAdsEnabled() {
        return false;
    }
    
    /**
     * Force refresh all ad units
     */
    public final void refreshAllAds() {
    }
    
    /**
     * Check if ModernAdsManager is fully initialized
     */
    public final boolean isInitialized() {
        return false;
    }
}