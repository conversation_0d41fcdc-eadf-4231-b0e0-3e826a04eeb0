// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBgImageListBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView cloudLeft;

  @NonNull
  public final TextView cloudRight;

  @NonNull
  public final AppCompatImageView ivBack;

  @NonNull
  public final CardView llAdView;

  @NonNull
  public final LinearLayout llAdViewFacebook;

  @NonNull
  public final RecyclerView rvBGImageList;

  @NonNull
  public final TextView sparkle1;

  @NonNull
  public final TextView sparkle2;

  @NonNull
  public final AppCompatTextView tvTitle;

  private ActivityBgImageListBinding(@NonNull LinearLayout rootView, @NonNull TextView cloudLeft,
      @NonNull TextView cloudRight, @NonNull AppCompatImageView ivBack, @NonNull CardView llAdView,
      @NonNull LinearLayout llAdViewFacebook, @NonNull RecyclerView rvBGImageList,
      @NonNull TextView sparkle1, @NonNull TextView sparkle2, @NonNull AppCompatTextView tvTitle) {
    this.rootView = rootView;
    this.cloudLeft = cloudLeft;
    this.cloudRight = cloudRight;
    this.ivBack = ivBack;
    this.llAdView = llAdView;
    this.llAdViewFacebook = llAdViewFacebook;
    this.rvBGImageList = rvBGImageList;
    this.sparkle1 = sparkle1;
    this.sparkle2 = sparkle2;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBgImageListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBgImageListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_bg_image_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBgImageListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cloudLeft;
      TextView cloudLeft = ViewBindings.findChildViewById(rootView, id);
      if (cloudLeft == null) {
        break missingId;
      }

      id = R.id.cloudRight;
      TextView cloudRight = ViewBindings.findChildViewById(rootView, id);
      if (cloudRight == null) {
        break missingId;
      }

      id = R.id.ivBack;
      AppCompatImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.llAdView;
      CardView llAdView = ViewBindings.findChildViewById(rootView, id);
      if (llAdView == null) {
        break missingId;
      }

      id = R.id.llAdViewFacebook;
      LinearLayout llAdViewFacebook = ViewBindings.findChildViewById(rootView, id);
      if (llAdViewFacebook == null) {
        break missingId;
      }

      id = R.id.rvBGImageList;
      RecyclerView rvBGImageList = ViewBindings.findChildViewById(rootView, id);
      if (rvBGImageList == null) {
        break missingId;
      }

      id = R.id.sparkle1;
      TextView sparkle1 = ViewBindings.findChildViewById(rootView, id);
      if (sparkle1 == null) {
        break missingId;
      }

      id = R.id.sparkle2;
      TextView sparkle2 = ViewBindings.findChildViewById(rootView, id);
      if (sparkle2 == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      AppCompatTextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityBgImageListBinding((LinearLayout) rootView, cloudLeft, cloudRight, ivBack,
          llAdView, llAdViewFacebook, rvBGImageList, sparkle1, sparkle2, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
