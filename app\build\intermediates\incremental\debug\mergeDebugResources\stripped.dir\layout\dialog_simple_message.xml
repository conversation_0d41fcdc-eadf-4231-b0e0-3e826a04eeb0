<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@android:color/white">>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Message"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/colorTheme"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="This is a simple message dialog."
        android:textSize="14sp"
        android:textColor="@color/colorGrey"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <Button
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"        android:text="OK"
        android:textColor="@color/colorWhite"
        android:background="@android:drawable/btn_default"
        android:padding="12dp" />

</LinearLayout>