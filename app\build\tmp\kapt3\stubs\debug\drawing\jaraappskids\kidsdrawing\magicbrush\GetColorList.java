package drawing.jaraappskids.kidsdrawing.magicbrush;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0007\u001a\u00020\bR\u0016\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0006\u00a8\u0006\t"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/GetColorList;", "", "()V", "colorlist", "", "", "[Ljava/lang/String;", "getRandomColorList", "", "app_debug"})
public final class GetColorList {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] colorlist = {"#F44336", "#F44336", "#9C27B0", "#2196F3", "#3F51B5", "#673AB7", "#03A9F4", "#00BCD4", "#009688", "#4CAF50", "#CDDC39", "#8BC34A", "#FFEB3B", "#FFC107", "#FF9800", "#FF5722"};
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.magicbrush.GetColorList INSTANCE = null;
    
    private GetColorList() {
        super();
    }
    
    public final int getRandomColorList() {
        return 0;
    }
}