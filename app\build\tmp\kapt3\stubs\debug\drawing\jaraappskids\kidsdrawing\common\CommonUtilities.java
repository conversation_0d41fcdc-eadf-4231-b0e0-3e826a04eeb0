package drawing.jaraappskids.kidsdrawing.common;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\t0\bj\b\u0012\u0004\u0012\u00020\t`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u000b\u001a\u0012\u0012\u0004\u0012\u00020\f0\bj\b\u0012\u0004\u0012\u00020\f`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\r\u001a\u0012\u0012\u0004\u0012\u00020\f0\bj\b\u0012\u0004\u0012\u00020\f`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u000e\u001a\u0012\u0012\u0004\u0012\u00020\u000f0\bj\b\u0012\u0004\u0012\u00020\u000f`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0010\u001a\u0012\u0012\u0004\u0012\u00020\u00110\bj\b\u0012\u0004\u0012\u00020\u0011`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0012\u001a\u0012\u0012\u0004\u0012\u00020\u00130\bj\b\u0012\u0004\u0012\u00020\u0013`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0014\u001a\u0012\u0012\u0004\u0012\u00020\f0\bj\b\u0012\u0004\u0012\u00020\f`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0015\u001a\u0012\u0012\u0004\u0012\u00020\f0\bj\b\u0012\u0004\u0012\u00020\f`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u001e\u0010\u0016\u001a\u0012\u0012\u0004\u0012\u00020\u00170\bj\b\u0012\u0004\u0012\u00020\u0017`\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010\u001d\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u0004J\u000e\u0010\u001f\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010 \u001a\u00020\u001a2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010!\u001a\u00020\"\u00a8\u0006#"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/common/CommonUtilities;", "", "()V", "checkRequiredPermission", "", "context", "Landroid/content/Context;", "getFontStyleList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/FontClass;", "Lkotlin/collections/ArrayList;", "getLargeEasyBGImageList", "Ldrawing/jaraappskids/kidsdrawing/pojo/BGImageClass;", "getLargeHardBGImageList", "getMagicBrushButtonImageList", "Ldrawing/jaraappskids/kidsdrawing/pojo/MagicBrushClass;", "getPaintBrushImageList", "Ldrawing/jaraappskids/kidsdrawing/pojo/PaintBrushClass;", "getPencilImageList", "Ldrawing/jaraappskids/kidsdrawing/pojo/PencilClass;", "getSmallEasyBGImageList", "getSmallHardBGImageList", "getStickerImageList", "Ldrawing/jaraappskids/kidsdrawing/pojo/StickerClass;", "isOnline", "requestRequiredPermission", "", "appCompatActivity", "Landroidx/appcompat/app/AppCompatActivity;", "showAlertFinishOnClick", "isDataUpdated", "showPermissionConfirmDialog", "showToast", "msg", "", "app_debug"})
public final class CommonUtilities {
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.common.CommonUtilities INSTANCE = null;
    
    private CommonUtilities() {
        super();
    }
    
    public final boolean isOnline(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    public final void showToast(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String msg) {
    }
    
    public final boolean checkRequiredPermission(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    public final void requestRequiredPermission(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity appCompatActivity) {
    }
    
    public final void showPermissionConfirmDialog(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity appCompatActivity) {
    }
    
    public final void showAlertFinishOnClick(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity appCompatActivity, boolean isDataUpdated) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> getSmallEasyBGImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> getSmallHardBGImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> getLargeEasyBGImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> getLargeHardBGImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.FontClass> getFontStyleList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.PencilClass> getPencilImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.PaintBrushClass> getPaintBrushImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.MagicBrushClass> getMagicBrushButtonImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.StickerClass> getStickerImageList(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
}