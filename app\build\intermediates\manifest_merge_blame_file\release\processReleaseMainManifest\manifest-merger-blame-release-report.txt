1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="drawing.jaraappskids.kidsdrawing"
4    android:versionCode="1"
5    android:versionName="1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:5-78
11-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:5-66
12-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
13-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:5-78
13-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission
14-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:5-10:38
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:22-77
16        android:maxSdkVersion="32" />
16-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:10:9-35
17    <uses-permission
17-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="32" />
19-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
20-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:5-88
20-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:22-85
21    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
21-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:5-82
21-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:22-79
22    <uses-permission android:name="android.permission.AD_SERVICES_CONFIG" />
22-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:5-77
22-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:22-74
23
24    <queries>
24-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:18:5-29:15
25        <intent>
25-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:19:9-23:18
26            <action android:name="android.intent.action.VIEW" />
26-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
26-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
27
28            <data android:scheme="https" />
28-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
28-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
29        </intent>
30        <intent>
30-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:24:9-28:18
31            <action android:name="android.intent.action.VIEW" />
31-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
31-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
32
33            <data android:scheme="http" />
33-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
33-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
34        </intent>
35
36        <package android:name="com.google.android.apps.tv.launcherx" />
36-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:9-72
36-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:18-69
37        <package android:name="com.google.android.tvlauncher" />
37-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:9-65
37-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:18-62
38        <package android:name="com.google.android.tvrecommendations" /> <!-- For browser content -->
38-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:9-72
38-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:18-69
39        <intent>
39-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
40            <action android:name="android.intent.action.VIEW" />
40-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
40-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
43
44            <data android:scheme="https" />
44-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
44-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
45        </intent> <!-- End of browser content -->
46        <!-- For CustomTabsService -->
47        <intent>
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
48            <action android:name="android.support.customtabs.action.CustomTabsService" />
48-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
48-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
49        </intent> <!-- End of CustomTabsService -->
50        <!-- For MRAID capabilities -->
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
52            <action android:name="android.intent.action.INSERT" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
53
54            <data android:mimeType="vnd.android.cursor.dir/event" />
54-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
57            <action android:name="android.intent.action.VIEW" />
57-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
57-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
58
59            <data android:scheme="sms" />
59-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
59-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
60        </intent>
61        <intent>
61-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
62            <action android:name="android.intent.action.DIAL" />
62-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
62-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
63
64            <data android:path="tel:" />
64-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
65        </intent>
66    </queries>
67
68    <uses-feature
68-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:11:5-13:36
69        android:name="android.software.leanback"
69-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:12:9-49
70        android:required="false" />
70-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:13:9-33
71    <uses-feature
71-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:14:5-16:36
72        android:name="android.hardware.touchscreen"
72-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:15:9-52
73        android:required="false" />
73-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:16:9-33
74
75    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
75-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
75-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
76    <uses-permission android:name="android.permission.WAKE_LOCK" />
76-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
76-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
77    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
77-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
77-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
78
79    <permission
79-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
80        android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
84
85    <application
85-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-100:19
86        android:name="drawing.jaraappskids.kidsdrawing.common.AppController"
86-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:15:13-49
87        android:allowBackup="false"
87-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:16:13-40
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
89        android:extractNativeLibs="false"
90        android:fullBackupContent="false"
90-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:17:13-46
91        android:fullBackupOnly="false"
91-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:18:13-43
92        android:hardwareAccelerated="true"
92-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:27:13-47
93        android:icon="@mipmap/ic_launcher"
93-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:20:13-47
94        android:label="@string/app_name"
94-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:22:13-45
95        android:largeHeap="true"
95-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:25:13-37
96        android:requestLegacyExternalStorage="true"
96-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:19:13-56
97        android:roundIcon="@mipmap/ic_launcher_round"
97-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:21:13-58
98        android:supportsRtl="true"
98-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:23:13-39
99        android:theme="@style/AppTheme"
99-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:24:13-44
100        android:usesCleartextTraffic="true" >
100-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:26:13-48
101        <provider
102            android:name="androidx.core.content.FileProvider"
102-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:32:17-66
103            android:authorities="drawing.jaraappskids.kidsdrawing.provider"
103-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:33:17-64
104            android:exported="false"
104-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:34:17-41
105            android:grantUriPermissions="true" >
105-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:35:17-51
106            <meta-data
106-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:36:13-38:57
107                android:name="android.support.FILE_PROVIDER_PATHS"
107-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:37:21-71
108                android:resource="@xml/file_paths" />
108-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:38:21-55
109        </provider>
110
111        <activity
111-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:41:9-51:20
112            android:name="drawing.jaraappskids.kidsdrawing.activities.SplashActivity"
112-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:43:17-58
113            android:exported="true"
113-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:42:13-36
114            android:theme="@style/AppTheme_Splash" >
114-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:44:17-55
115            <intent-filter>
115-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:45:13-50:29
116                <action android:name="android.intent.action.MAIN" />
116-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:17-68
116-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:25-66
117                <action android:name="android.intent.action.VIEW" />
117-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
117-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
118
119                <category android:name="android.intent.category.LAUNCHER" />
119-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:17-76
119-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:27-74
120            </intent-filter>
121        </activity>
122        <activity
122-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:53:9-56:19
123            android:name="drawing.jaraappskids.kidsdrawing.activities.MainActivity"
123-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:54:17-56
124            android:screenOrientation="portrait" />
124-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:55:17-53
125        <activity
125-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:58:9-61:19
126            android:name="drawing.jaraappskids.kidsdrawing.activities.BGImageListActivity"
126-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:59:17-63
127            android:screenOrientation="portrait" />
127-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:60:17-53
128        <activity
128-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:63:9-67:19
129            android:name="drawing.jaraappskids.kidsdrawing.activities.EditorActivity"
129-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:64:17-58
130            android:screenOrientation="portrait"
130-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:65:17-53
131            android:windowSoftInputMode="adjustNothing" />
131-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:66:17-60
132        <activity
132-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:69:9-71:55
133            android:name="drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity"
133-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:70:17-54
134            android:screenOrientation="portrait" />
134-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:71:17-53
135        <activity
135-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:72:9-74:55
136            android:name="drawing.jaraappskids.kidsdrawing.mywork.FullScreenActivity"
136-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:73:17-58
137            android:screenOrientation="portrait" />
137-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:74:17-53
138
139        <meta-data
139-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:76:9-78:65
140            android:name="com.google.android.gms.ads.APPLICATION_ID"
140-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:77:17-73
141            android:value="@string/AD_MOB_APPLICATION_ID" />
141-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:78:17-62
142        <meta-data
142-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:80:9-82:73
143            android:name="com.google.android.gms.version"
143-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:81:17-62
144            android:value="@integer/google_play_services_version" />
144-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:82:17-70
145
146        <property
146-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:84:9-87:48
147            android:name="android.adservices.AD_SERVICES_CONFIG"
147-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:85:13-65
148            android:resource="@xml/gma_ad_services_config" />
148-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:86:13-59
149
150        <!-- Firebase messaging service removed - was causing crashes without proper configuration -->
151        <!--
152        <service
153                android:name=".firebase.MyFirebaseMessagingService"
154                android:exported="false">
155            <intent-filter>
156                <action android:name="com.google.firebase.MESSAGING_EVENT" />
157                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
158            </intent-filter>
159        </service>
160        -->
161        <activity
161-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:28:9-32:20
162            android:name="com.google.android.tv.ads.controls.FallbackImageActivity"
162-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:29:13-84
163            android:exported="false"
163-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:30:13-37
164            android:theme="@style/Theme.AppCompat.NoActionBar.Translucent" >
164-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:31:13-75
165        </activity>
166
167        <provider
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
168            android:name="androidx.startup.InitializationProvider"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
169            android:authorities="drawing.jaraappskids.kidsdrawing.androidx-startup"
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
170            android:exported="false" >
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
171            <meta-data
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.emoji2.text.EmojiCompatInitializer"
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
173                android:value="androidx.startup" />
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
175                android:name="androidx.work.WorkManagerInitializer"
175-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
176                android:value="androidx.startup" />
176-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
177            <meta-data
177-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
178                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
178-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
179                android:value="androidx.startup" />
179-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
180            <meta-data
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
181                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
182                android:value="androidx.startup" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
183        </provider>
184
185        <activity
185-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
186            android:name="com.google.android.gms.common.api.GoogleApiActivity"
186-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
187            android:exported="false"
187-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
188            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
188-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
189        <activity
189-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
190            android:name="com.google.android.gms.ads.AdActivity"
190-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
191            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
192            android:exported="false"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
193            android:theme="@android:style/Theme.Translucent" />
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
194
195        <provider
195-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
196            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
196-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
197            android:authorities="drawing.jaraappskids.kidsdrawing.mobileadsinitprovider"
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
198            android:exported="false"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
199            android:initOrder="100" />
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
200
201        <service
201-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
202            android:name="com.google.android.gms.ads.AdService"
202-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
203            android:enabled="true"
203-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
204            android:exported="false" />
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
205
206        <activity
206-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
207            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
207-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
208            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
209            android:exported="false" />
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
210        <activity
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
211            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
212            android:excludeFromRecents="true"
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
213            android:exported="false"
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
214            android:launchMode="singleTask"
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
215            android:taskAffinity=""
215-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
217
218        <meta-data
218-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
219            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
219-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
220            android:value="true" />
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
221        <meta-data
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
222            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
222-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
223            android:value="true" />
223-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
224
225        <uses-library
225-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
226            android:name="android.ext.adservices"
226-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
227            android:required="false" />
227-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
228
229        <service
229-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
230            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
230-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
232            android:enabled="@bool/enable_system_alarm_service_default"
232-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
233            android:exported="false" />
233-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
234        <service
234-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
235            android:name="androidx.work.impl.background.systemjob.SystemJobService"
235-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
237            android:enabled="@bool/enable_system_job_service_default"
237-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
238            android:exported="true"
238-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
239            android:permission="android.permission.BIND_JOB_SERVICE" />
239-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
240        <service
240-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
241            android:name="androidx.work.impl.foreground.SystemForegroundService"
241-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
242            android:directBootAware="false"
242-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
243            android:enabled="@bool/enable_system_foreground_service_default"
243-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
244            android:exported="false" />
244-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
245
246        <receiver
246-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
247            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
247-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
249            android:enabled="true"
249-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
250            android:exported="false" />
250-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
251        <receiver
251-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
252            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
252-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
253            android:directBootAware="false"
253-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
254            android:enabled="false"
254-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
255            android:exported="false" >
255-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
256            <intent-filter>
256-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
257                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
257-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
257-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
258                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
258-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
258-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
259            </intent-filter>
260        </receiver>
261        <receiver
261-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
262            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
262-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
263            android:directBootAware="false"
263-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
264            android:enabled="false"
264-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
265            android:exported="false" >
265-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
266            <intent-filter>
266-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
267                <action android:name="android.intent.action.BATTERY_OKAY" />
267-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
267-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
268                <action android:name="android.intent.action.BATTERY_LOW" />
268-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
268-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
269            </intent-filter>
270        </receiver>
271        <receiver
271-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
272            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
272-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
274            android:enabled="false"
274-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
275            android:exported="false" >
275-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
276            <intent-filter>
276-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
277                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
278                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
279            </intent-filter>
280        </receiver>
281        <receiver
281-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
282            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
282-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
283            android:directBootAware="false"
283-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
284            android:enabled="false"
284-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
285            android:exported="false" >
285-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
286            <intent-filter>
286-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
287                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
288            </intent-filter>
289        </receiver>
290        <receiver
290-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
291            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
291-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
293            android:enabled="false"
293-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
294            android:exported="false" >
294-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
295            <intent-filter>
295-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
296                <action android:name="android.intent.action.BOOT_COMPLETED" />
296-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
296-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
297                <action android:name="android.intent.action.TIME_SET" />
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
298                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
299            </intent-filter>
300        </receiver>
301        <receiver
301-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
302            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
302-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
303            android:directBootAware="false"
303-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
304            android:enabled="@bool/enable_system_alarm_service_default"
304-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
305            android:exported="false" >
305-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
306            <intent-filter>
306-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
307                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
308            </intent-filter>
309        </receiver>
310        <receiver
310-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
311            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
311-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
313            android:enabled="true"
313-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
314            android:exported="true"
314-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
315            android:permission="android.permission.DUMP" >
315-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
316            <intent-filter>
316-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
317                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
318            </intent-filter>
319        </receiver>
320
321        <uses-library
321-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
322            android:name="androidx.window.extensions"
322-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
323            android:required="false" />
323-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
324        <uses-library
324-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
325            android:name="androidx.window.sidecar"
325-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
326            android:required="false" />
326-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
327
328        <service
328-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
329            android:name="androidx.room.MultiInstanceInvalidationService"
329-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
330            android:directBootAware="true"
330-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
331            android:exported="false" />
331-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
332
333        <receiver
333-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
334            android:name="androidx.profileinstaller.ProfileInstallReceiver"
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
335            android:directBootAware="false"
335-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
336            android:enabled="true"
336-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
337            android:exported="true"
337-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
338            android:permission="android.permission.DUMP" >
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
339            <intent-filter>
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
340                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
341            </intent-filter>
342            <intent-filter>
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
343                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
344            </intent-filter>
345            <intent-filter>
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
346                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
347            </intent-filter>
348            <intent-filter>
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
349                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
350            </intent-filter>
351        </receiver>
352
353        <service
353-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
354            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
354-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
355            android:exported="false" >
355-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
356            <meta-data
356-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
357                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
358                android:value="cct" />
358-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
359        </service>
360        <service
360-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
361            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
361-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
362            android:exported="false"
362-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
363            android:permission="android.permission.BIND_JOB_SERVICE" >
363-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
364        </service>
365
366        <receiver
366-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
367            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
367-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
368            android:exported="false" />
368-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
369    </application>
370
371</manifest>
