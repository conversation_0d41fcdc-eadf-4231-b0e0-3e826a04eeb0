package drawing.jaraappskids.kidsdrawing.pojo;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\b\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0003\u0010\u0005\"\u0004\b\u0006\u0010\u0007R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\u000e\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R\u001a\u0010\u0014\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0011\"\u0004\b\u0016\u0010\u0013\u00a8\u0006\u0017"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/pojo/PencilClass;", "", "()V", "isSelected", "", "()Z", "setSelected", "(Z)V", "pencilColor", "", "getPencilColor", "()I", "setPencilColor", "(I)V", "pencilImageName", "", "getPencilImageName", "()Ljava/lang/String;", "setPencilImageName", "(Ljava/lang/String;)V", "pencilImagePath", "getPencilImagePath", "setPencilImagePath", "app_debug"})
public final class PencilClass {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String pencilImageName = "";
    private int pencilColor = 0;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String pencilImagePath = "";
    private boolean isSelected = false;
    
    public PencilClass() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPencilImageName() {
        return null;
    }
    
    public final void setPencilImageName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getPencilColor() {
        return 0;
    }
    
    public final void setPencilColor(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPencilImagePath() {
        return null;
    }
    
    public final void setPencilImagePath(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
}