// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.viewbinding.ViewBinding;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class RowMagicBrushBinding implements ViewBinding {
  @NonNull
  private final AppCompatImageView rootView;

  @NonNull
  public final AppCompatImageView ivMBButton;

  private RowMagicBrushBinding(@NonNull AppCompatImageView rootView,
      @NonNull AppCompatImageView ivMBButton) {
    this.rootView = rootView;
    this.ivMBButton = ivMBButton;
  }

  @Override
  @NonNull
  public AppCompatImageView getRoot() {
    return rootView;
  }

  @NonNull
  public static RowMagicBrushBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RowMagicBrushBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.row_magic_brush, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RowMagicBrushBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    AppCompatImageView ivMBButton = (AppCompatImageView) rootView;

    return new RowMagicBrushBinding((AppCompatImageView) rootView, ivMBButton);
  }
}
