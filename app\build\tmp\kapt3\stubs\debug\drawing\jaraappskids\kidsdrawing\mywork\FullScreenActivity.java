package drawing.jaraappskids.kidsdrawing.mywork;

/**
 * Created by Jr. Android Developer <PERSON><PERSON><PERSON> on 26-Jul-19.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0015\u001a\u00020\u0012H\u0002J\u0012\u0010\u0016\u001a\u00020\u00122\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018H\u0014R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u00020\u0006X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u001a\u0010\u000b\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0019"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/mywork/FullScreenActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Ldrawing/jaraappskids/kidsdrawing/databinding/ActivityFullScreenBinding;", "context", "Landroid/content/Context;", "getContext", "()Landroid/content/Context;", "setContext", "(Landroid/content/Context;)V", "localImage", "", "getLocalImage", "()Ljava/lang/String;", "setLocalImage", "(Ljava/lang/String;)V", "addImageGallery", "", "file", "Ljava/io/File;", "initDefine", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "app_debug"})
public final class FullScreenActivity extends androidx.appcompat.app.AppCompatActivity {
    public android.content.Context context;
    private drawing.jaraappskids.kidsdrawing.databinding.ActivityFullScreenBinding binding;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String localImage = "";
    
    public FullScreenActivity() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext() {
        return null;
    }
    
    public final void setContext(@org.jetbrains.annotations.NotNull()
    android.content.Context p0) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLocalImage() {
        return null;
    }
    
    public final void setLocalImage(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    private final void initDefine() {
    }
    
    private final void addImageGallery(java.io.File file) {
    }
}