{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeReleaseResources-60:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4886f4714567b788bedb48de3c472517\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,14040", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,14117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6a58776e9dcdced13d931fd31f07cadf\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,295,348,412,479,571,634,756,862,981,1033,1090,1200,1276,1316,1387,1421,1455,1503,1568,1606", "endColumns": "45,49,52,63,66,91,62,121,105,118,51,56,109,75,39,70,33,33,47,64,37,55", "endOffsets": "244,294,347,411,478,570,633,755,861,980,1032,1089,1199,1275,1315,1386,1420,1454,1502,1567,1605,1661"}, "to": {"startLines": "139,140,141,143,144,145,146,147,148,149,150,151,152,153,156,157,158,159,160,161,162,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12253,12303,12357,12494,12562,12633,12729,12796,12922,13032,13155,13211,13272,13386,13682,13726,13801,13839,13877,13929,13998,14782", "endColumns": "49,53,56,67,70,95,66,125,109,122,55,60,113,79,43,74,37,37,51,68,41,59", "endOffsets": "12298,12352,12409,12557,12628,12724,12791,12917,13027,13150,13206,13267,13381,13461,13721,13796,13834,13872,13924,13993,14035,14837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\acab93b1b19a0a7fc9f8e2a900142b63\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5389", "endColumns": "128", "endOffsets": "5513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f8992bbb99f27b442a756634e186b35a\\transformed\\jetified-tv-ads-1.0.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,9", "startColumns": "0,0,0,0", "startOffsets": "197,242,355,580", "endColumns": "44,112,57,77", "endOffsets": "241,354,412,657"}, "to": {"startLines": "33,71,72,75", "startColumns": "4,4,4,4", "startOffsets": "2964,6854,6971,7209", "endColumns": "48,116,61,81", "endOffsets": "3008,6966,7028,7286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8d70832d7f1deb228721870d2bb07788\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4419,4525,4672,4795,4902,5038,5162,5281,5518,5662,5767,5914,6036,6176,6327,6391,6459", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4520,4667,4790,4897,5033,5157,5276,5384,5657,5762,5909,6031,6171,6322,6386,6454,6538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ac185097d874e47ec4cafd044e6cc8ed\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "68,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "6613,7291,7390,7501", "endColumns": "102,98,110,97", "endOffsets": "6711,7385,7496,7594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56b21b1e2df5e7a9e2bbd796d8a365f1\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,74,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,154,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3086,3158,3241,3326,4127,4226,4339,6716,6784,7119,7599,7669,7729,7816,7882,7947,8008,8072,8133,8187,8288,8349,8409,8463,8533,8644,8731,8808,8895,8977,9058,9201,9280,9362,9494,9586,9664,9718,9771,9837,9907,9985,10056,10136,10208,10286,10355,10424,10522,10604,10692,10785,10879,10953,11022,11117,11169,11252,11320,11405,11493,11555,11619,11682,11752,11852,11948,12045,12138,12196,13466,14122,14204,14279", "endLines": "5,34,35,36,37,38,46,47,48,69,70,74,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,154,164,165,166", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3081,3153,3236,3321,3407,4221,4334,4414,6779,6849,7204,7664,7724,7811,7877,7942,8003,8067,8128,8182,8283,8344,8404,8458,8528,8639,8726,8803,8890,8972,9053,9196,9275,9357,9489,9581,9659,9713,9766,9832,9902,9980,10051,10131,10203,10281,10350,10419,10517,10599,10687,10780,10874,10948,11017,11112,11164,11247,11315,11400,11488,11550,11614,11677,11747,11847,11943,12040,12133,12191,12248,13538,14199,14274,14350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c3a5dd56922f0d9d5fe7ff90c31db109\\transformed\\core-1.13.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "39,40,41,42,43,44,45,167", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3412,3508,3611,3709,3807,3910,4015,14355", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3503,3606,3704,3802,3905,4010,4122,14451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4e3970f58cff9116df172ae1e89ec5d\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "67,73,142,155,168,169,170", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6543,7033,12414,13543,14456,14624,14704", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6608,7114,12489,13677,14619,14699,14777"}}]}]}