package drawing.jaraappskids.kidsdrawing.pojo;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\b\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u001a\u0010\t\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u0006\"\u0004\b\u000b\u0010\b\u00a8\u0006\f"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/pojo/BGImageClass;", "", "()V", "bgImageName", "", "getBgImageName", "()Ljava/lang/String;", "setBgImageName", "(Ljava/lang/String;)V", "bgImagePath", "getBgImagePath", "setBgImagePath", "app_debug"})
public final class BGImageClass {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String bgImageName = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String bgImagePath = "";
    
    public BGImageClass() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBgImageName() {
        return null;
    }
    
    public final void setBgImageName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBgImagePath() {
        return null;
    }
    
    public final void setBgImagePath(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
}