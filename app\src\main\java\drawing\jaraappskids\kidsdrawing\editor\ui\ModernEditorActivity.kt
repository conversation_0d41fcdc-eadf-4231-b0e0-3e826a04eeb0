package drawing.jaraappskids.kidsdrawing.editor.ui

import android.graphics.Color
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.AndroidEntryPoint
import drawing.jaraappskids.kidsdrawing.R
import drawing.jaraappskids.kidsdrawing.databinding.ActivityModernEditorBinding
import drawing.jaraappskids.kidsdrawing.editor.data.*
import drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEvent
import drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingViewModel
import kotlinx.coroutines.launch

/**
 * Modern drawing editor activity with Material Design 3 and clean architecture
 */
@AndroidEntryPoint
class ModernEditorActivity : AppCompatActivity() {

    private lateinit var binding: ActivityModernEditorBinding
    private val viewModel: DrawingViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityModernEditorBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupDrawingView()
        setupToolbar()
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupDrawingView() {
        binding.drawingView.apply {
            onStrokeCompleted = { stroke ->
                viewModel.addStroke(stroke)
            }
            onDrawingStateChanged = { isDrawing ->
                viewModel.setDrawingState(isDrawing)
            }
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "Drawing Editor"
        }
    }
    
    private fun setupObservers() {
        // Observe drawing state
        lifecycleScope.launch {
            viewModel.drawingState.collect { state ->
                binding.drawingView.setDrawingCanvas(state.canvas)
                binding.drawingView.setBrushSettings(state.brushSettings)
                binding.drawingView.setSelectedTool(state.selectedTool)
                
                // Update UI based on state
                updateToolButtons(state.selectedTool)
                updateUndoRedoButtons(state)
            }
        }
        
        // Observe UI state
        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                updateUIState(uiState)
            }
        }
        
        // Observe events
        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }
    
    private fun setupClickListeners() {
        // Tool selection
        binding.btnBrush.setOnClickListener {
            viewModel.setSelectedTool(DrawingTool.BRUSH)
        }

        binding.btnEraser.setOnClickListener {
            viewModel.setSelectedTool(DrawingTool.ERASER)
        }

        // Actions
        binding.btnUndo.setOnClickListener {
            viewModel.undo()
        }

        binding.btnRedo.setOnClickListener {
            viewModel.redo()
        }

        binding.btnClear.setOnClickListener {
            showClearConfirmation()
        }

        binding.btnSave.setOnClickListener {
            saveDrawing()
        }

        // Settings
        binding.btnBrushSettings.setOnClickListener {
            toggleBrushSettings()
        }

        // Color presets
        setupColorPresets()

        // Brush size slider
        binding.brushSizeSlider.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    viewModel.setBrushSize(progress.toFloat())
                }
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })
    }

    private fun toggleBrushSettings() {
        val panel = binding.brushSettingsPanel
        panel.visibility = if (panel.visibility == android.view.View.VISIBLE) {
            android.view.View.GONE
        } else {
            android.view.View.VISIBLE
        }
    }
    
    private fun setupColorPresets() {
        val colors = listOf(
            Color.BLACK, Color.WHITE, Color.RED, Color.GREEN,
            Color.BLUE, Color.YELLOW, Color.MAGENTA, Color.CYAN,
            Color.GRAY, Color.DKGRAY, Color.LTGRAY, Color.parseColor("#FF9800")
        )
        
        val colorButtons = listOf(
            binding.colorBlack, binding.colorWhite, binding.colorRed, binding.colorGreen,
            binding.colorBlue, binding.colorYellow, binding.colorMagenta, binding.colorCyan,
            binding.colorGray, binding.colorDarkGray, binding.colorLightGray, binding.colorOrange
        )
        
        colors.forEachIndexed { index, color ->
            if (index < colorButtons.size) {
                colorButtons[index].apply {
                    setBackgroundColor(color)
                    setOnClickListener {
                        viewModel.setBrushColor(color)
                        updateSelectedColor(color)
                    }
                }
            }
        }
    }
    
    private fun updateToolButtons(selectedTool: DrawingTool) {
        // Reset all tool buttons
        listOf(
            binding.btnBrush, binding.btnEraser, binding.btnShapeRect,
            binding.btnShapeCircle, binding.btnText, binding.btnSticker
        ).forEach { button ->
            button.isSelected = false
        }
        
        // Highlight selected tool
        when (selectedTool) {
            DrawingTool.BRUSH -> binding.btnBrush.isSelected = true
            DrawingTool.ERASER -> binding.btnEraser.isSelected = true
            DrawingTool.SHAPE_RECTANGLE -> binding.btnShapeRect.isSelected = true
            DrawingTool.SHAPE_CIRCLE -> binding.btnShapeCircle.isSelected = true
            DrawingTool.TEXT -> binding.btnText.isSelected = true
            DrawingTool.STICKER -> binding.btnSticker.isSelected = true
            else -> binding.btnBrush.isSelected = true
        }
    }
    
    private fun updateUndoRedoButtons(state: DrawingState) {
        binding.btnUndo.isEnabled = viewModel.canUndo()
        binding.btnRedo.isEnabled = viewModel.canRedo()
    }
    
    private fun updateUIState(uiState: drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingUiState) {
        // Update loading states
        binding.progressBar.visibility = if (uiState.isSaving || uiState.isLoading) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
        
        // Update panel visibility
        binding.colorPickerPanel.visibility = if (uiState.showColorPicker) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
        
        binding.brushSettingsPanel.visibility = if (uiState.showBrushSettings) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
        
        binding.layerPanel.visibility = if (uiState.showLayerPanel) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
    }
    
    private fun handleEvent(event: DrawingEvent) {
        when (event) {
            is DrawingEvent.ShowMessage -> {
                Toast.makeText(this, event.message, Toast.LENGTH_SHORT).show()
            }
            is DrawingEvent.Error -> {
                Toast.makeText(this, "Error: ${event.message}", Toast.LENGTH_LONG).show()
            }
            is DrawingEvent.ActionCompleted -> {
                // Optional: Show subtle feedback for actions
            }
            is DrawingEvent.DrawingSaved -> {
                Toast.makeText(this, "Drawing saved to ${event.path}", Toast.LENGTH_LONG).show()
            }
            is DrawingEvent.LayerAdded -> {
                // Update layer UI
            }
            is DrawingEvent.LayerRemoved -> {
                // Update layer UI
            }
            is DrawingEvent.ActiveLayerChanged -> {
                // Update active layer indicator
            }
        }
    }
    
    private fun updateSelectedColor(color: Int) {
        binding.selectedColorIndicator.setBackgroundColor(color)
    }
    
    private fun showClearConfirmation() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Clear Canvas")
            .setMessage("Are you sure you want to clear the entire canvas? This action cannot be undone.")
            .setPositiveButton("Clear") { _, _ ->
                viewModel.clearCanvas()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun saveDrawing() {
        val filename = "drawing_${System.currentTimeMillis()}"
        viewModel.saveDrawing(filename, ExportFormat.PNG)
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
    
    override fun onBackPressed() {
        // Show save confirmation if there are unsaved changes
        if (viewModel.canUndo()) {
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Unsaved Changes")
                .setMessage("You have unsaved changes. Do you want to save before leaving?")
                .setPositiveButton("Save") { _, _ ->
                    saveDrawing()
                    super.onBackPressed()
                }
                .setNegativeButton("Discard") { _, _ ->
                    super.onBackPressed()
                }
                .setNeutralButton("Cancel", null)
                .show()
        } else {
            super.onBackPressed()
        }
    }
}
