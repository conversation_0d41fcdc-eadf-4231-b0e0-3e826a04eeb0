<libraries>
  <library
      name="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified@jar"
      jars="D:\Android\KidsDrawingApp\app\libs\main.jar"
      resolved="__local_aars__:D:\Android\KidsDrawingApp\app\libs\main.jar:unspecified"/>
  <library
      name="androidx.databinding:viewbinding:8.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5f7a0f8bc0449c7f4cfbab25eb4ecf71\transformed\jetified-viewbinding-8.5.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.5.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5f7a0f8bc0449c7f4cfbab25eb4ecf71\transformed\jetified-viewbinding-8.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.0.0\64302a285bd7779196c44a9ceae188ebd62ea67\kotlin-parcelize-runtime-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0"/>
  <library
      name="com.squareup.leakcanary:leakcanary-android:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4da4f441a9fb63d0ab4ee08634b7cf49\transformed\jetified-leakcanary-android-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-android:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4da4f441a9fb63d0ab4ee08634b7cf49\transformed\jetified-leakcanary-android-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d21d60fa257ceb9183d742ea34b7b496\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d21d60fa257ceb9183d742ea34b7b496\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.QuadFlask:colorpicker:0.0.13@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\jars\classes.jar"
      resolved="com.github.QuadFlask:colorpicker:0.0.13"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1052397ee5b8e5745735d4b082350b38\transformed\jetified-appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1052397ee5b8e5745735d4b082350b38\transformed\jetified-appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.ads.interactivemedia.v3:interactivemedia:3.36.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\jars\classes.jar"
      resolved="com.google.ads.interactivemedia.v3:interactivemedia:3.36.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f64af4fcec0833398cf7f56d3f0e55a4\transformed\jetified-preference-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f64af4fcec0833398cf7f56d3f0e55a4\transformed\jetified-preference-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c4e3970f58cff9116df172ae1e89ec5d\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c4e3970f58cff9116df172ae1e89ec5d\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.tv:tv-ads:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\jars\classes.jar"
      resolved="com.google.android.tv:tv-ads:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4886f4714567b788bedb48de3c472517\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4886f4714567b788bedb48de3c472517\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.14.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.14.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\859b5441f9fbe333b8f29647fda22bbf\transformed\jetified-viewpager2-1.1.0-beta02\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0-beta02"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\859b5441f9fbe333b8f29647fda22bbf\transformed\jetified-viewpager2-1.1.0-beta02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:24.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6a58776e9dcdced13d931fd31f07cadf\transformed\jetified-play-services-ads-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:24.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6a58776e9dcdced13d931fd31f07cadf\transformed\jetified-play-services-ads-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-pal:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-pal:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-api:24.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\45e28ea886728f6c9521180bfe880361\transformed\jetified-user-messaging-platform-3.2.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\45e28ea886728f6c9521180bfe880361\transformed\jetified-user-messaging-platform-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:20.1.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b84e674a21560e946203cea650c7d3ac\transformed\jetified-fragment-ktx-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b84e674a21560e946203cea650c7d3ac\transformed\jetified-fragment-ktx-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5f6a47ec2bdb8f8ffa1652b4c9a65bf6\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5f6a47ec2bdb8f8ffa1652b4c9a65bf6\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d8178f82324e7d512be64a7e4f221deb\transformed\jetified-activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d8178f82324e7d512be64a7e4f221deb\transformed\jetified-activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\59bfd416416883deea0bb727308834ce\transformed\jetified-activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\59bfd416416883deea0bb727308834ce\transformed\jetified-activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ac185097d874e47ec4cafd044e6cc8ed\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ac185097d874e47ec4cafd044e6cc8ed\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\11557ed537c408f8869650a01b4f980f\transformed\media-1.7.0\jars\classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\11557ed537c408f8869650a01b4f980f\transformed\media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\097237b8b9cdd7b3c25214a2e00eebf6\transformed\recyclerview-1.3.2\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\097237b8b9cdd7b3c25214a2e00eebf6\transformed\recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6867eb619e543b1744faf8ce1403a87c\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6867eb619e543b1744faf8ce1403a87c\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2b2c6ad2b7afb142c2f8cb4d0363526e\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2b2c6ad2b7afb142c2f8cb4d0363526e\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9d251b9d8ce7bc6804eabd762524a58b\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9d251b9d8ce7bc6804eabd762524a58b\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b8c8b9328e3c6d7235dc1272a27e9b0d\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b8c8b9328e3c6d7235dc1272a27e9b0d\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7802f02953c2e3e2eff05f589482e894\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7802f02953c2e3e2eff05f589482e894\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\36304a4266596246e7b74ddac37af8fc\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\36304a4266596246e7b74ddac37af8fc\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a982044848a2565039a080a184b32318\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a982044848a2565039a080a184b32318\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6024827ae34f7aff50bae9e3e0186c8f\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6024827ae34f7aff50bae9e3e0186c8f\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.11.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e9e2b5177130b9efdbb326a781983289\transformed\webkit-1.11.0-alpha02\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.11.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e9e2b5177130b9efdbb326a781983289\transformed\webkit-1.11.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8b4eb601851c1ba54121a6e8ebe11709\transformed\jetified-core-ktx-1.13.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8b4eb601851c1ba54121a6e8ebe11709\transformed\jetified-core-ktx-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4ade8fa3d445b09822b893727187cf1a\transformed\work-runtime-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4ade8fa3d445b09822b893727187cf1a\transformed\work-runtime-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\407ce014fdfcad4f3d05c3e44277f809\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\407ce014fdfcad4f3d05c3e44277f809\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.7\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2b16a6eb20122dc2eeafe98251b8dc54\transformed\lifecycle-viewmodel-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2b16a6eb20122dc2eeafe98251b8dc54\transformed\lifecycle-viewmodel-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\346ef79e87f08ad9fc9965b50336a7de\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\346ef79e87f08ad9fc9965b50336a7de\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d7c2bfbb5af45d887fc66aa48a8a5cea\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d7c2bfbb5af45d887fc66aa48a8a5cea\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\951de76ebfa8d163b826735331969649\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\951de76ebfa8d163b826735331969649\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\17483682e8bf057c908a5c006a649d06\transformed\lifecycle-livedata-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\17483682e8bf057c908a5c006a649d06\transformed\lifecycle-livedata-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5e1906c59cfb2b087b275ac0bd19cff8\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5e1906c59cfb2b087b275ac0bd19cff8\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2e1f7bb515f0604d4833bd7753ae485e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2e1f7bb515f0604d4833bd7753ae485e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5c0c6f72ed21f06b5bf9de1c3611e04f\transformed\lifecycle-livedata-core-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5c0c6f72ed21f06b5bf9de1c3611e04f\transformed\lifecycle-livedata-core-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.0.0\7d968ca8316eee591ae4dd46b9337b1b7793ec57\kotlin-android-extensions-runtime-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0"/>
  <library
      name="com.squareup.leakcanary:leakcanary-object-watcher-android:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-object-watcher-android:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f1f10f1f7c3efa76f67976899402a483\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f1f10f1f7c3efa76f67976899402a483\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b877b5607c2ac5211a62838db5a6ea86\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b877b5607c2ac5211a62838db5a6ea86\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\cd5f7a6a37fcef9b566cc0298fdb29d5\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\cd5f7a6a37fcef9b566cc0298fdb29d5\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\41ddcd0b7a28586eb05341e8f362bcb9\transformed\jetified-ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\41ddcd0b7a28586eb05341e8f362bcb9\transformed\jetified-ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5c886296f8a815b83add004e4ea2b670\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5c886296f8a815b83add004e4ea2b670\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.14.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.14.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4969a8d10287214ceabd1fb5ee380b8e\transformed\jetified-transport-api-3.0.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4969a8d10287214ceabd1fb5ee380b8e\transformed\jetified-transport-api-3.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1bc7564f966159972dc6d67fd09a85b9\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1bc7564f966159972dc6d67fd09a85b9\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\af472de109f8654b50e8e056af3f9442\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\af472de109f8654b50e8e056af3f9442\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.0\b48df2c4aede9586cc931ead433bc02d6fd7879e\kotlin-stdlib-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.0\e0bc273ee1d963954bfc3e2f1b20ffcf0f4be44f\kotlin-stdlib-jdk7-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:indicatorseekbar::debug"
      project=":indicatorseekbar"/>
  <library
      name="com.github.yukuku:ambilwarna:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5ff9bd2009e5ad31b6e19abe7b702bae\transformed\jetified-ambilwarna-2.0.1\jars\classes.jar"
      resolved="com.github.yukuku:ambilwarna:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5ff9bd2009e5ad31b6e19abe7b702bae\transformed\jetified-ambilwarna-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.intuit.sdp:sdp-android:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\cd5880d74125cf1efbbb9940d7720254\transformed\jetified-sdp-android-1.1.0\jars\classes.jar"
      resolved="com.intuit.sdp:sdp-android:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\cd5880d74125cf1efbbb9940d7720254\transformed\jetified-sdp-android-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.squareup.leakcanary:leakcanary-android-core:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-android-core:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:shark-android:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\shark-android\2.10\919cd08c15dbaff469a1837f1efa22a5b16232cc\shark-android-2.10.jar"
      resolved="com.squareup.leakcanary:shark-android:2.10"/>
  <library
      name="com.squareup.leakcanary:shark:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\shark\2.10\e92cb86c514562f1f591622c47c51f3f2a7d2489\shark-2.10.jar"
      resolved="com.squareup.leakcanary:shark:2.10"/>
  <library
      name="com.squareup.leakcanary:shark-graph:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\shark-graph\2.10\e9e78df8ce773a85adc90b35582aa62163f2d6a8\shark-graph-2.10.jar"
      resolved="com.squareup.leakcanary:shark-graph:2.10"/>
  <library
      name="com.squareup.leakcanary:shark-hprof:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\shark-hprof\2.10\d0c58306d9dc3c8caf44256ddee29918c524bd1a\shark-hprof-2.10.jar"
      resolved="com.squareup.leakcanary:shark-hprof:2.10"/>
  <library
      name="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b0f2f2b1ea683d07cca6c88eea7d04d4\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b0f2f2b1ea683d07cca6c88eea7d04d4\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b89b225644856518cebb3f492eb263ac\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b89b225644856518cebb3f492eb263ac\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\bc29dcba26332e73155eb74df0ec32ab\transformed\jetified-leakcanary-object-watcher-android-core-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\bc29dcba26332e73155eb74df0ec32ab\transformed\jetified-leakcanary-object-watcher-android-core-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:leakcanary-object-watcher:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\leakcanary-object-watcher\2.10\5d91af6785d9d15f51cd08f7c5e52874b2ddea3f\leakcanary-object-watcher-2.10.jar"
      resolved="com.squareup.leakcanary:leakcanary-object-watcher:2.10"/>
  <library
      name="com.squareup.leakcanary:leakcanary-android-utils:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0d78391c2760c4537eda9077bb4f87b7\transformed\jetified-leakcanary-android-utils-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:leakcanary-android-utils:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0d78391c2760c4537eda9077bb4f87b7\transformed\jetified-leakcanary-android-utils-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:shark-log:2.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.leakcanary\shark-log\2.10\91b34759b85e0bb6922607f306dfa0cd50fac5a1\shark-log-2.10.jar"
      resolved="com.squareup.leakcanary:shark-log:2.10"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e8217cf4d1379041f0d649c39ee3f1c9\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e8217cf4d1379041f0d649c39ee3f1c9\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.14.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.14.2\543524781bc197fb64e1c2df1756a8ec17ff05ba\disklrucache-4.14.2.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.14.2"/>
  <library
      name="com.github.bumptech.glide:annotations:4.14.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.14.2\57e84ef841035701766b7d74236d04c65cfa4248\annotations-4.14.2.jar"
      resolved="com.github.bumptech.glide:annotations:4.14.2"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9dacbf323299d6786d635444d15b2ae6\transformed\exifinterface-1.3.3\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9dacbf323299d6786d635444d15b2ae6\transformed\exifinterface-1.3.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\fd1840ee370e9f46a307d715ea24ff82\transformed\jetified-emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\fd1840ee370e9f46a307d715ea24ff82\transformed\jetified-emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\72279248dc3cf56767593da2dba462d5\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\72279248dc3cf56767593da2dba462d5\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\54893f0ffc2693bcfde8b4ab83dcd91b\transformed\jetified-lifecycle-service-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\54893f0ffc2693bcfde8b4ab83dcd91b\transformed\jetified-lifecycle-service-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:plumber-android:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:plumber-android:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d6b4ccd6fdde0e6b89402dbb066bb633\transformed\jetified-room-ktx-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d6b4ccd6fdde0e6b89402dbb066bb633\transformed\jetified-room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.leakcanary:plumber-android-core:2.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b22f2d36704a10d6d0c9b55e100d666f\transformed\jetified-plumber-android-core-2.10\jars\classes.jar"
      resolved="com.squareup.leakcanary:plumber-android-core:2.10"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b22f2d36704a10d6d0c9b55e100d666f\transformed\jetified-plumber-android-core-2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e7ca046154a3cdfdc427cab1ab602fbc\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e7ca046154a3cdfdc427cab1ab602fbc\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.curtains:curtains:1.2.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\00f99faa29e9b9558ed7a4323dc949b4\transformed\jetified-curtains-1.2.4\jars\classes.jar"
      resolved="com.squareup.curtains:curtains:1.2.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\00f99faa29e9b9558ed7a4323dc949b4\transformed\jetified-curtains-1.2.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1b67ab735444fe9da40fb99582ba89ec\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1b67ab735444fe9da40fb99582ba89ec\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio:2.2.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.2.2\36f483536153f15339a8b48d508e22be7c9c531a\okio-2.2.2.jar"
      resolved="com.squareup.okio:okio:2.2.2"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
</libraries>
