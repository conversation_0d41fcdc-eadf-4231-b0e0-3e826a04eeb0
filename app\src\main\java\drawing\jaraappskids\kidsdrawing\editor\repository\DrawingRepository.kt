package drawing.jaraappskids.kidsdrawing.editor.repository

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import drawing.jaraappskids.kidsdrawing.editor.data.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing drawing data and operations
 */
@Singleton
class DrawingRepository @Inject constructor(
    private val context: Context
) {
    
    private val _drawingState = MutableStateFlow(
        DrawingState(
            canvas = DrawingCanvas(1080, 1920),
            brushSettings = BrushSettings(),
            canvasSettings = CanvasSettings(1080, 1920)
        )
    )
    val drawingState: StateFlow<DrawingState> = _drawingState
    
    private val undoStack = mutableListOf<DrawingAction>()
    private val redoStack = mutableListOf<DrawingAction>()
    
    fun updateBrushSettings(settings: BrushSettings) {
        _drawingState.value = _drawingState.value.copy(brushSettings = settings)
    }
    
    fun updateCanvasSettings(settings: CanvasSettings) {
        _drawingState.value = _drawingState.value.copy(canvasSettings = settings)
    }
    
    fun setSelectedTool(tool: DrawingTool) {
        _drawingState.value = _drawingState.value.copy(selectedTool = tool)
    }
    
    fun setDrawingState(isDrawing: Boolean) {
        _drawingState.value = _drawingState.value.copy(isDrawing = isDrawing)
    }
    
    fun executeAction(action: DrawingAction) {
        // Add to undo stack
        undoStack.add(action)
        if (undoStack.size > _drawingState.value.canvasSettings.maxUndoSteps) {
            undoStack.removeAt(0)
        }
        
        // Clear redo stack when new action is performed
        redoStack.clear()
        
        // Apply action to canvas
        val updatedCanvas = applyActionToCanvas(_drawingState.value.canvas, action)
        _drawingState.value = _drawingState.value.copy(
            canvas = updatedCanvas,
            undoStack = undoStack.toList(),
            redoStack = redoStack.toList()
        )
    }
    
    fun undo(): Boolean {
        if (undoStack.isEmpty()) return false
        
        val lastAction = undoStack.removeAt(undoStack.size - 1)
        redoStack.add(lastAction)
        
        // Rebuild canvas from remaining actions
        val baseCanvas = DrawingCanvas(_drawingState.value.canvas.width, _drawingState.value.canvas.height)
        val rebuiltCanvas = undoStack.fold(baseCanvas) { canvas, action ->
            applyActionToCanvas(canvas, action)
        }
        
        _drawingState.value = _drawingState.value.copy(
            canvas = rebuiltCanvas,
            undoStack = undoStack.toList(),
            redoStack = redoStack.toList()
        )
        
        return true
    }
    
    fun redo(): Boolean {
        if (redoStack.isEmpty()) return false
        
        val actionToRedo = redoStack.removeAt(redoStack.size - 1)
        undoStack.add(actionToRedo)
        
        val updatedCanvas = applyActionToCanvas(_drawingState.value.canvas, actionToRedo)
        _drawingState.value = _drawingState.value.copy(
            canvas = updatedCanvas,
            undoStack = undoStack.toList(),
            redoStack = redoStack.toList()
        )
        
        return true
    }
    
    fun canUndo(): Boolean = undoStack.isNotEmpty()
    fun canRedo(): Boolean = redoStack.isNotEmpty()
    
    private fun applyActionToCanvas(canvas: DrawingCanvas, action: DrawingAction): DrawingCanvas {
        return when (action) {
            is DrawingAction.AddStroke -> {
                val layer = canvas.getActiveLayer() ?: return canvas
                layer.strokes.add(action.stroke)
                canvas
            }
            is DrawingAction.AddShape -> {
                // Handle shape addition
                canvas
            }
            is DrawingAction.AddText -> {
                // Handle text addition
                canvas
            }
            is DrawingAction.AddSticker -> {
                // Handle sticker addition
                canvas
            }
            is DrawingAction.RemoveElement -> {
                // Handle element removal
                canvas
            }
            is DrawingAction.UpdateLayer -> {
                val index = canvas.layers.indexOfFirst { it.id == action.layer.id }
                if (index >= 0) {
                    canvas.layers[index] = action.layer
                }
                canvas
            }
            is DrawingAction.AddLayer -> {
                canvas.addLayer(action.layer)
                canvas
            }
            is DrawingAction.RemoveLayer -> {
                canvas.removeLayer(action.layerId)
                canvas
            }
            is DrawingAction.SetActiveLayer -> {
                canvas.copy(activeLayerId = action.layerId)
            }
            is DrawingAction.Clear -> {
                canvas.layers.clear()
                canvas
            }
        }
    }
    
    suspend fun saveDrawing(filename: String, format: ExportFormat = ExportFormat.PNG): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = renderCanvasToBitmap(_drawingState.value.canvas)
                val file = File(context.filesDir, "$filename.${format.name.lowercase()}")
                
                FileOutputStream(file).use { out ->
                    when (format) {
                        ExportFormat.PNG -> bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                        ExportFormat.JPEG -> bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
                        else -> bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                    }
                }
                
                Result.success(file.absolutePath)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    private fun renderCanvasToBitmap(canvas: DrawingCanvas): Bitmap {
        val bitmap = Bitmap.createBitmap(canvas.width, canvas.height, Bitmap.Config.ARGB_8888)
        val androidCanvas = Canvas(bitmap)
        
        // Draw background
        androidCanvas.drawColor(canvas.backgroundColor)
        
        // Draw layers in order
        canvas.layers.sortedBy { it.order }.forEach { layer ->
            if (layer.isVisible) {
                drawLayer(androidCanvas, layer)
            }
        }
        
        return bitmap
    }
    
    private fun drawLayer(canvas: Canvas, layer: DrawingLayer) {
        layer.strokes.forEach { stroke ->
            drawStroke(canvas, stroke)
        }
    }
    
    private fun drawStroke(canvas: Canvas, stroke: DrawingStroke) {
        if (stroke.points.size < 2) return
        
        val paint = Paint().apply {
            color = stroke.color
            strokeWidth = stroke.strokeWidth
            alpha = (stroke.opacity * 255).toInt()
            isAntiAlias = true
            style = Paint.Style.STROKE
            strokeCap = Paint.Cap.ROUND
            strokeJoin = Paint.Join.ROUND
        }
        
        val path = Path()
        val firstPoint = stroke.points.first()
        path.moveTo(firstPoint.x, firstPoint.y)
        
        for (i in 1 until stroke.points.size) {
            val point = stroke.points[i]
            path.lineTo(point.x, point.y)
        }
        
        canvas.drawPath(path, paint)
    }
    
    fun clearCanvas() {
        executeAction(DrawingAction.Clear)
    }
    
    fun addLayer(name: String): String {
        val layer = DrawingLayer(name = name, order = _drawingState.value.canvas.layers.size)
        executeAction(DrawingAction.AddLayer(layer))
        executeAction(DrawingAction.SetActiveLayer(layer.id))
        return layer.id
    }
    
    fun removeLayer(layerId: String) {
        executeAction(DrawingAction.RemoveLayer(layerId))
    }
    
    fun setActiveLayer(layerId: String) {
        executeAction(DrawingAction.SetActiveLayer(layerId))
    }
}
