<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/cvChild"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        app:cardPreventCornerOverlap="true"
        app:cardElevation="@dimen/zero_dp"
        app:cardBackgroundColor="@color/colorWhite"
        app:cardUseCompatPadding="true"
        app:cardCornerRadius="@dimen/fifteen_dp">

    <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvFontStyle"
            android:layout_width="@dimen/one_twenty_dp"
            android:layout_height="@dimen/forty_dp"
            android:gravity="center"
            android:layout_gravity="center"
            android:text="@string/label_text_style"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:layout_centerInParent="true"/>

</androidx.cardview.widget.CardView>