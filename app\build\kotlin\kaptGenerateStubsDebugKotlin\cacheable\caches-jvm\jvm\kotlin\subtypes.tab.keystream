android.app.Application(androidx.appcompat.app.AppCompatActivity?drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback!android.view.View.OnClickListener7drawing.jaraappskids.kidsdrawing.interfaces.AdsCallbackandroid.os.AsyncTask<drawing.jaraappskids.kidsdrawing.interfaces.CallbackListener1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder+androidx.lifecycle.DefaultLifecycleObserverandroid.view.View!androidx.cardview.widget.CardView,androidx.appcompat.widget.AppCompatImageViewandroid.os.Parcelablekotlin.Enum:drawing.jaraappskids.kidsdrawing.editor.data.DrawingActionandroidx.lifecycle.ViewModel>drawing.jaraappskids.kidsdrawing.editor.viewmodel.DrawingEventandroidx.fragment.app.Fragment.android.widget.AdapterView.OnItemClickListenerandroid.widget.BaseAdapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             