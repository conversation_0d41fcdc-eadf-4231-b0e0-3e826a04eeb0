package drawing.jaraappskids.kidsdrawing.editor.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DrawingRepository_Factory implements Factory<DrawingRepository> {
  private final Provider<Context> contextProvider;

  public DrawingRepository_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DrawingRepository get() {
    return newInstance(contextProvider.get());
  }

  public static DrawingRepository_Factory create(Provider<Context> contextProvider) {
    return new DrawingRepository_Factory(contextProvider);
  }

  public static DrawingRepository newInstance(Context context) {
    return new DrawingRepository(context);
  }
}
