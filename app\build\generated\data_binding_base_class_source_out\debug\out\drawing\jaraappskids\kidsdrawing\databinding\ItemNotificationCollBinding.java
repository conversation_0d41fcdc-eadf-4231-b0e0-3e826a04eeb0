// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationCollBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView bigIcon;

  @NonNull
  public final TextView contentText;

  @NonNull
  public final RelativeLayout iconContainer;

  @NonNull
  public final LinearLayout notificationMain;

  @NonNull
  public final TextView timestamp;

  @NonNull
  public final TextView titleText;

  private ItemNotificationCollBinding(@NonNull RelativeLayout rootView, @NonNull ImageView bigIcon,
      @NonNull TextView contentText, @NonNull RelativeLayout iconContainer,
      @NonNull LinearLayout notificationMain, @NonNull TextView timestamp,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.bigIcon = bigIcon;
    this.contentText = contentText;
    this.iconContainer = iconContainer;
    this.notificationMain = notificationMain;
    this.timestamp = timestamp;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationCollBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationCollBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification_coll, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationCollBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.big_icon;
      ImageView bigIcon = ViewBindings.findChildViewById(rootView, id);
      if (bigIcon == null) {
        break missingId;
      }

      id = R.id.content_text;
      TextView contentText = ViewBindings.findChildViewById(rootView, id);
      if (contentText == null) {
        break missingId;
      }

      id = R.id.icon_container;
      RelativeLayout iconContainer = ViewBindings.findChildViewById(rootView, id);
      if (iconContainer == null) {
        break missingId;
      }

      id = R.id.notification_main;
      LinearLayout notificationMain = ViewBindings.findChildViewById(rootView, id);
      if (notificationMain == null) {
        break missingId;
      }

      id = R.id.timestamp;
      TextView timestamp = ViewBindings.findChildViewById(rootView, id);
      if (timestamp == null) {
        break missingId;
      }

      id = R.id.title_text;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemNotificationCollBinding((RelativeLayout) rootView, bigIcon, contentText,
          iconContainer, notificationMain, timestamp, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
