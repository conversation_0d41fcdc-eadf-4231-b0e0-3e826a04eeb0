<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="drawing.jaraappskids.kidsdrawing"
    android:versionCode="1"
    android:versionName="1.4" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.AD_SERVICES_CONFIG" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="http" />
        </intent>

        <package android:name="com.google.android.apps.tv.launcherx" />
        <package android:name="com.google.android.tvlauncher" />
        <package android:name="com.google.android.tvrecommendations" /> <!-- For browser content -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent> <!-- End of browser content -->
        <!-- For CustomTabsService -->
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent> <!-- End of CustomTabsService -->
        <!-- For MRAID capabilities -->
        <intent>
            <action android:name="android.intent.action.INSERT" />

            <data android:mimeType="vnd.android.cursor.dir/event" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="sms" />
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />

            <data android:path="tel:" />
        </intent>
    </queries>

    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <permission
        android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- To allow posting notifications on Android 13 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name="drawing.jaraappskids.kidsdrawing.DrawingApplication"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="false"
        android:fullBackupOnly="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true" >
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="drawing.jaraappskids.kidsdrawing.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name="drawing.jaraappskids.kidsdrawing.activities.SplashActivity"
            android:exported="true"
            android:theme="@style/AppTheme_Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="drawing.jaraappskids.kidsdrawing.activities.MainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="drawing.jaraappskids.kidsdrawing.activities.BGImageListActivity"
            android:screenOrientation="portrait" />

        <!-- Old editor activity - replaced with modern version -->
        <!--
        <activity
                android:name=".activities.EditorActivity"
                android:screenOrientation="portrait"
                android:windowSoftInputMode="adjustNothing"
                />
        -->


        <!-- Modern Drawing Editor -->
        <activity
            android:name="drawing.jaraappskids.kidsdrawing.editor.ui.ModernEditorActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="drawing.jaraappskids.kidsdrawing.mywork.FullScreenActivity"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/AD_MOB_APPLICATION_ID" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config" />

        <!-- Firebase messaging service removed - was causing crashes without proper configuration -->
        <!--
        <service
                android:name=".firebase.MyFirebaseMessagingService"
                android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </service>
        -->
        <activity
            android:name="com.google.android.tv.ads.controls.FallbackImageActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.NoActionBar.Translucent" >
        </activity>
        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
            android:authorities="drawing.jaraappskids.kidsdrawing.mobileadsinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="com.google.android.gms.ads.AdService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false" />
        <activity
            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true" />

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="drawing.jaraappskids.kidsdrawing.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <provider
            android:name="leakcanary.internal.LeakCanaryFileProvider"
            android:authorities="com.squareup.leakcanary.fileprovider.drawing.jaraappskids.kidsdrawing"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/leak_canary_file_paths" />
        </provider>

        <activity
            android:name="leakcanary.internal.activity.LeakActivity"
            android:exported="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_display_activity_label"
            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
            android:theme="@style/leak_canary_LeakCanary.Base" >
            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:mimeType="*/*" />
                <data android:host="*" />
                <data android:pathPattern=".*\\.hprof" />
                <data android:pathPattern=".*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
                <!--
            Since hprof isn't a standard MIME type, we have to declare such patterns.
            Most file providers will generate URIs including their own package name,
            which contains `.` characters that must be explicitly escaped in pathPattern.
            @see https://stackoverflow.com/a/31028507/703646
                -->
            </intent-filter>
        </activity>

        <activity-alias
            android:name="leakcanary.internal.activity.LeakLauncherActivity"
            android:banner="@drawable/leak_canary_tv_icon"
            android:enabled="@bool/leak_canary_add_launcher_icon"
            android:exported="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_display_activity_label"
            android:targetActivity="leakcanary.internal.activity.LeakActivity"
            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
            android:theme="@style/leak_canary_LeakCanary.Base" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!-- Android TV launcher intent -->
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="leakcanary.internal.RequestPermissionActivity"
            android:excludeFromRecents="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_storage_permission_activity_label"
            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
            android:theme="@style/leak_canary_Theme.Transparent" />

        <receiver android:name="leakcanary.internal.NotificationReceiver" />

        <provider
            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
            android:authorities="drawing.jaraappskids.kidsdrawing.leakcanary-installer"
            android:enabled="@bool/leak_canary_watcher_auto_install"
            android:exported="false" />
        <provider
            android:name="leakcanary.internal.PlumberInstaller"
            android:authorities="drawing.jaraappskids.kidsdrawing.plumber-installer"
            android:enabled="@bool/leak_canary_plumber_auto_install"
            android:exported="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>