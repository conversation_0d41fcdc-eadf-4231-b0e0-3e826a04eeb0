package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BQ\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\tH\u00c6\u0003J\t\u0010!\u001a\u00020\u000bH\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000e0\rH\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0\rH\u00c6\u0003J[\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rH\u00c6\u0001J\u0013\u0010%\u001a\u00020\u000b2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020*H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0017R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0019\u00a8\u0006+"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingState;", "", "canvas", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;", "brushSettings", "Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushSettings;", "canvasSettings", "Ldrawing/jaraappskids/kidsdrawing/editor/data/CanvasSettings;", "selectedTool", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "isDrawing", "", "undoStack", "", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingAction;", "redoStack", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushSettings;Ldrawing/jaraappskids/kidsdrawing/editor/data/CanvasSettings;Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;ZLjava/util/List;Ljava/util/List;)V", "getBrushSettings", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/BrushSettings;", "getCanvas", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;", "getCanvasSettings", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/CanvasSettings;", "()Z", "getRedoStack", "()Ljava/util/List;", "getSelectedTool", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingTool;", "getUndoStack", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class DrawingState {
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas = null;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings brushSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings canvasSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool selectedTool = null;
    private final boolean isDrawing = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> undoStack = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> redoStack = null;
    
    public DrawingState(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings brushSettings, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings canvasSettings, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool selectedTool, boolean isDrawing, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> undoStack, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> redoStack) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas getCanvas() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings getBrushSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings getCanvasSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool getSelectedTool() {
        return null;
    }
    
    public final boolean isDrawing() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> getUndoStack() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> getRedoStack() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingState copy(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas canvas, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.BrushSettings brushSettings, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.CanvasSettings canvasSettings, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingTool selectedTool, boolean isDrawing, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> undoStack, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends drawing.jaraappskids.kidsdrawing.editor.data.DrawingAction> redoStack) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}