package drawing.jaraappskids.kidsdrawing.common;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u001a\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\bJ\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000209X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010A\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010C\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010D\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010E\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010F\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u001a\u0010G\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bH\u0010I\"\u0004\bJ\u0010KR\u001a\u0010L\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010I\"\u0004\bN\u0010KR\u001a\u0010O\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bP\u0010I\"\u0004\bQ\u0010KR\u001a\u0010R\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bS\u0010I\"\u0004\bT\u0010KR\u001a\u0010U\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bV\u0010I\"\u0004\bW\u0010KR\u001a\u0010X\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bY\u0010I\"\u0004\bZ\u0010KR\u001a\u0010[\u001a\u000209X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\\\u0010I\"\u0004\b]\u0010KR\u000e\u0010^\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010_\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010`\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010a\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010c\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010d\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010g\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010h\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010i\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010j\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010k\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010l\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010m\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010o\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010p\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u001a\u0010q\u001a\u00020\"X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\br\u0010s\"\u0004\bt\u0010uR\u000e\u0010v\u001a\u00020\"X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010w\u001a\u00020\"X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010x\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010y\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u001a\u0010z\u001a\u00020\"X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b{\u0010s\"\u0004\b|\u0010uR\u000e\u0010}\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010~\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u007f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000f\u0010\u0080\u0001\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000f\u0010\u0081\u0001\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000f\u0010\u0082\u0001\u001a\u000209X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0083\u0001"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/common/CommonConstants;", "", "()V", "AD_GOOGLE", "", "AD_TYPE_FACEBOOK_GOOGLE", "AD_TYPE_FB_GOOGLE", "APP_OPEN_AD_TIMEOUT", "ARRAY_OF_IMAGE", "AssetsFolderPath", "BANNER_AD_REFRESH_TIME", "CLICK_IMAGE_COUNT", "CapAdd", "CapCancel", "CapClean", "CapConfirm", "CapContinue", "CapDelete", "CapDuplicate", "CapExit", "CapPermission", "CapPleaseWait", "CapRemove", "ClickTypeFont", "ClickTypeMagic", "ClickTypeMagicBrush", "ClickTypePaint", "ClickTypePaintBrush", "ClickTypePencil", "ClickTypeSticker", "DEFAULT_APP_OPEN_TIMEOUT", "", "DEFAULT_BANNER_REFRESH_TIME", "DEFAULT_INTERSTITIAL_FREQUENCY", "", "DISABLE", "DefaultAssetsFont", "DirBGImageEasyLarge", "DirBGImageEasySmall", "DirBGImageHardLarge", "DirBGImageHardSmall", "DirFont", "DirMagicBrushButton", "DirMagicBrushContent", "DirPaintBrush", "DirPencil", "DirSavedCard", "DirSticker", "ENABLE", "ENABLE_DISABLE", "EditorItemFont", "EditorItemMagic", "EditorItemPaint", "EditorItemPencil", "EditorItemSticker", "EditorItemText", "FALLBACK_TO_TEST_ADS", "", "GOOGLE_ADMOB_APP_ID", "GOOGLE_APP_OPEN", "GOOGLE_APP_OPEN_ID", "GOOGLE_BANNER", "GOOGLE_BANNER_ID", "GOOGLE_INTERSTITIAL", "GOOGLE_INTERSTITIAL_ID", "GOOGLE_NATIVE", "GOOGLE_NATIVE_ID", "GOOGLE_REWARDED", "GOOGLE_REWARDED_ID", "IMAGE_DATE", "INTERSTITIAL_AD_FREQUENCY", "IS_CLEAR", "getIS_CLEAR", "()Z", "setIS_CLEAR", "(Z)V", "IS_CLEAR_BRUSH", "getIS_CLEAR_BRUSH", "setIS_CLEAR_BRUSH", "IS_CLEAR_MAGIC", "getIS_CLEAR_MAGIC", "setIS_CLEAR_MAGIC", "IS_CLEAR_PENCIL", "getIS_CLEAR_PENCIL", "setIS_CLEAR_PENCIL", "IS_CLEAR_STICKER", "getIS_CLEAR_STICKER", "setIS_CLEAR_STICKER", "IS_CLEAR_TEXT", "getIS_CLEAR_TEXT", "setIS_CLEAR_TEXT", "IS_DRAWING_MODE", "getIS_DRAWING_MODE", "setIS_DRAWING_MODE", "KeyBGImageType", "KeyIsDataUpdated", "KeyIsFirstTime", "KeyIsFrom", "KeyIsFromEditing", "KeyItemPos", "KeySavedDirName", "LOCAL_IMAGE", "MsgAllowPermission", "MsgDoYouWantToAdd", "MsgDoYouWantToClean", "MsgDoYouWantToDuplicate", "MsgDoYouWantToExit", "MsgDoYouWantToRemove", "MsgDoubleBackToExit", "MsgNoItemsInEditor", "MsgPleaseEnterText", "MsgPleaseSelectItem", "MsgSomethingWrong", "POSITION", "getPOSITION", "()I", "setPOSITION", "(I)V", "ReqCodeDataUpdated", "RequestCodePermission", "SPLASH_SCREEN_COUNT", "STATUS_ENABLE_DISABLE", "STICKER_COUNT", "getSTICKER_COUNT", "setSTICKER_COUNT", "TEST_APP_OPEN_ID", "TEST_BANNER_ID", "TEST_INTERSTITIAL_ID", "TEST_NATIVE_ID", "TEST_REWARDED_ID", "USE_HYBRID_ADS", "app_debug"})
public final class CommonConstants {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgAllowPermission = "Please allow all required permission to continue...";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgSomethingWrong = "Something went wrong. Please try again!";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoubleBackToExit = "Click back again to Exit...";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoYouWantToExit = "Are you sure do you want to exit?";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoYouWantToAdd = "Are you sure do you want to add this item?";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoYouWantToRemove = "Are you sure do you want to remove this item?";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoYouWantToDuplicate = "Are you sure do you want to duplicate this item?";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgDoYouWantToClean = "Are you sure do you want to clean this screen?";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgPleaseSelectItem = "Please select item first.";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgNoItemsInEditor = "There are no items in editor.";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MsgPleaseEnterText = "Please enter some text.";
    public static final int ReqCodeDataUpdated = 111;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyIsDataUpdated = "IsDataUpdated";
    public static final int RequestCodePermission = 222;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyIsFirstTime = "IsFirstTime";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyIsFrom = "IsFrom";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyIsFromEditing = "IsFromEditing";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyItemPos = "ItemPos";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeyBGImageType = "BGImageType";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KeySavedDirName = "SavedDirName";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapPleaseWait = "Please wait...";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapPermission = "Permission";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapContinue = "Continue";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapConfirm = "Confirm";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapCancel = "Cancel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapExit = "Exit";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapDuplicate = "Duplicate";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapAdd = "Add";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapRemove = "Remove";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapClean = "Clean";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CapDelete = "Delete";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AssetsFolderPath = "file:///android_asset/";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirBGImageEasySmall = "BGImageEasySmall";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirBGImageHardSmall = "BGImageHardSmall";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirBGImageEasyLarge = "BGImageEasyLarge";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirBGImageHardLarge = "BGImageHardLarge";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirFont = "Font";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirMagicBrushButton = "MagicBrushButton";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirMagicBrushContent = "MagicBrushContent";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirPaintBrush = "PaintBrush";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirPencil = "Pencil";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirSticker = "Sticker";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DirSavedCard = "SavedCard";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DefaultAssetsFont = "Font_01.ttf";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemText = "Text";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemFont = "Font";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemMagic = "Magic";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemPencil = "Pencil";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemPaint = "Paint";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EditorItemSticker = "Sticker";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypeFont = "Font";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypePencil = "Pencil";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypePaint = "Paint";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypeMagic = "Magic";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypeSticker = "Sticker";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypePaintBrush = "PaintBrush";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ClickTypeMagicBrush = "MagicBrush";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String LOCAL_IMAGE = "IMAGE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String IMAGE_DATE = "DATE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ARRAY_OF_IMAGE = "ARRAY_OF_IMAGE";
    private static int POSITION = -1;
    private static boolean IS_CLEAR = true;
    private static boolean IS_CLEAR_BRUSH = true;
    private static boolean IS_CLEAR_MAGIC = true;
    private static boolean IS_CLEAR_PENCIL = true;
    private static boolean IS_CLEAR_STICKER = true;
    private static boolean IS_CLEAR_TEXT = true;
    private static int STICKER_COUNT = 0;
    private static boolean IS_DRAWING_MODE = false;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_ADMOB_APP_ID = "ca-app-pub-9280326363135397~1540206282";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_BANNER_ID = "ca-app-pub-9280326363135397/7478928675";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_INTERSTITIAL_ID = "ca-app-pub-9280326363135397/4820975748";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_REWARDED_ID = "ca-app-pub-9280326363135397/8971814877";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_APP_OPEN_ID = "ca-app-pub-9280326363135397/5559342344";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_NATIVE_ID = "ca-app-pub-3940256099942544/2247696110";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEST_BANNER_ID = "ca-app-pub-9280326363135397/7478928675";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEST_INTERSTITIAL_ID = "ca-app-pub-3940256099942544/1033173712";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEST_REWARDED_ID = "ca-app-pub-3940256099942544/5224354917";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEST_APP_OPEN_ID = "ca-app-pub-3940256099942544/3419835294";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEST_NATIVE_ID = "ca-app-pub-3940256099942544/2247696110";
    public static final boolean USE_HYBRID_ADS = true;
    public static final boolean FALLBACK_TO_TEST_ADS = true;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AD_GOOGLE = "google";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AD_TYPE_FACEBOOK_GOOGLE = "google";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ENABLE = "Enable";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DISABLE = "Disable";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ENABLE_DISABLE = "Enable";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AD_TYPE_FB_GOOGLE = "AD_TYPE_FB_GOOGLE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_BANNER = "GOOGLE_BANNER";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_INTERSTITIAL = "GOOGLE_INTERSTITIAL";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_REWARDED = "GOOGLE_REWARDED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_APP_OPEN = "GOOGLE_APP_OPEN";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GOOGLE_NATIVE = "GOOGLE_NATIVE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SPLASH_SCREEN_COUNT = "splash_screen_count";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CLICK_IMAGE_COUNT = "click_image_count";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String STATUS_ENABLE_DISABLE = "STATUS_ENABLE_DISABLE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INTERSTITIAL_AD_FREQUENCY = "interstitial_ad_frequency";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BANNER_AD_REFRESH_TIME = "banner_ad_refresh_time";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String APP_OPEN_AD_TIMEOUT = "app_open_ad_timeout";
    public static final int DEFAULT_INTERSTITIAL_FREQUENCY = 1;
    public static final long DEFAULT_BANNER_REFRESH_TIME = 60000L;
    public static final long DEFAULT_APP_OPEN_TIMEOUT = 4000L;
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.common.CommonConstants INSTANCE = null;
    
    private CommonConstants() {
        super();
    }
    
    public final int getPOSITION() {
        return 0;
    }
    
    public final void setPOSITION(int p0) {
    }
    
    public final boolean getIS_CLEAR() {
        return false;
    }
    
    public final void setIS_CLEAR(boolean p0) {
    }
    
    public final boolean getIS_CLEAR_BRUSH() {
        return false;
    }
    
    public final void setIS_CLEAR_BRUSH(boolean p0) {
    }
    
    public final boolean getIS_CLEAR_MAGIC() {
        return false;
    }
    
    public final void setIS_CLEAR_MAGIC(boolean p0) {
    }
    
    public final boolean getIS_CLEAR_PENCIL() {
        return false;
    }
    
    public final void setIS_CLEAR_PENCIL(boolean p0) {
    }
    
    public final boolean getIS_CLEAR_STICKER() {
        return false;
    }
    
    public final void setIS_CLEAR_STICKER(boolean p0) {
    }
    
    public final boolean getIS_CLEAR_TEXT() {
        return false;
    }
    
    public final void setIS_CLEAR_TEXT(boolean p0) {
    }
    
    public final int getSTICKER_COUNT() {
        return 0;
    }
    
    public final void setSTICKER_COUNT(int p0) {
    }
    
    public final boolean getIS_DRAWING_MODE() {
        return false;
    }
    
    public final void setIS_DRAWING_MODE(boolean p0) {
    }
}