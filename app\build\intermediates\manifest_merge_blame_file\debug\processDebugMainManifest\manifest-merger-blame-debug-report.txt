1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="drawing.jaraappskids.kidsdrawing"
4    android:versionCode="1"
5    android:versionName="1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:5-78
11-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:5-66
12-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
13-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:5-78
13-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission
14-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:5-10:38
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:22-77
16        android:maxSdkVersion="32" />
16-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:10:9-35
17    <uses-permission
17-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="32" />
19-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
20-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:5-88
20-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:22-85
21    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
21-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:5-82
21-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:22-79
22    <uses-permission android:name="android.permission.AD_SERVICES_CONFIG" />
22-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:5-77
22-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:22-74
23
24    <queries>
24-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:18:5-29:15
25        <intent>
25-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:19:9-23:18
26            <action android:name="android.intent.action.VIEW" />
26-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
26-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
27
28            <data android:scheme="https" />
28-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
28-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
29        </intent>
30        <intent>
30-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:24:9-28:18
31            <action android:name="android.intent.action.VIEW" />
31-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
31-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
32
33            <data android:scheme="http" />
33-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
33-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
34        </intent>
35
36        <package android:name="com.google.android.apps.tv.launcherx" />
36-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:9-72
36-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:18-69
37        <package android:name="com.google.android.tvlauncher" />
37-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:9-65
37-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:18-62
38        <package android:name="com.google.android.tvrecommendations" /> <!-- For browser content -->
38-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:9-72
38-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:18-69
39        <intent>
39-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
40            <action android:name="android.intent.action.VIEW" />
40-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
40-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
43
44            <data android:scheme="https" />
44-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
44-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
45        </intent> <!-- End of browser content -->
46        <!-- For CustomTabsService -->
47        <intent>
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
48            <action android:name="android.support.customtabs.action.CustomTabsService" />
48-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
48-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
49        </intent> <!-- End of CustomTabsService -->
50        <!-- For MRAID capabilities -->
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
52            <action android:name="android.intent.action.INSERT" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
53
54            <data android:mimeType="vnd.android.cursor.dir/event" />
54-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
57            <action android:name="android.intent.action.VIEW" />
57-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
57-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
58
59            <data android:scheme="sms" />
59-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
59-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
60        </intent>
61        <intent>
61-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
62            <action android:name="android.intent.action.DIAL" />
62-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
62-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
63
64            <data android:path="tel:" />
64-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
65        </intent>
66    </queries>
67
68    <uses-feature
68-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:11:5-13:36
69        android:name="android.software.leanback"
69-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:12:9-49
70        android:required="false" />
70-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:13:9-33
71    <uses-feature
71-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:14:5-16:36
72        android:name="android.hardware.touchscreen"
72-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:15:9-52
73        android:required="false" />
73-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:16:9-33
74
75    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
75-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
75-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
76    <uses-permission android:name="android.permission.WAKE_LOCK" />
76-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
76-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\e5624315552f2cb1f8e52c8957b1c6a4\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
77    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
77-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
77-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
78
79    <permission
79-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
80        android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- To allow posting notifications on Android 13 -->
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
84    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
84-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:5-77
84-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:22-74
85
86    <application
86-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-112:19
87        android:name="drawing.jaraappskids.kidsdrawing.DrawingApplication"
87-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:15:13-47
88        android:allowBackup="false"
88-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:16:13-40
89        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
89-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\af892568e4c6055353b79bd60cd12ea7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
90        android:debuggable="true"
91        android:extractNativeLibs="false"
92        android:fullBackupContent="false"
92-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:17:13-46
93        android:fullBackupOnly="false"
93-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:18:13-43
94        android:hardwareAccelerated="true"
94-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:27:13-47
95        android:icon="@mipmap/ic_launcher"
95-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:20:13-47
96        android:label="@string/app_name"
96-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:22:13-45
97        android:largeHeap="true"
97-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:25:13-37
98        android:requestLegacyExternalStorage="true"
98-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:19:13-56
99        android:roundIcon="@mipmap/ic_launcher_round"
99-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:21:13-58
100        android:supportsRtl="true"
100-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:23:13-39
101        android:testOnly="true"
102        android:theme="@style/AppTheme"
102-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:24:13-44
103        android:usesCleartextTraffic="true" >
103-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:26:13-48
104        <provider
105            android:name="androidx.core.content.FileProvider"
105-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:32:17-66
106            android:authorities="drawing.jaraappskids.kidsdrawing.provider"
106-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:33:17-64
107            android:exported="false"
107-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:34:17-41
108            android:grantUriPermissions="true" >
108-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:35:17-51
109            <meta-data
109-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:36:13-38:57
110                android:name="android.support.FILE_PROVIDER_PATHS"
110-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:37:21-71
111                android:resource="@xml/file_paths" />
111-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:38:21-55
112        </provider>
113
114        <activity
114-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:41:9-51:20
115            android:name="drawing.jaraappskids.kidsdrawing.activities.SplashActivity"
115-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:43:17-58
116            android:exported="true"
116-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:42:13-36
117            android:theme="@style/AppTheme_Splash" >
117-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:44:17-55
118            <intent-filter>
118-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:45:13-50:29
119                <action android:name="android.intent.action.MAIN" />
119-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:17-68
119-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:25-66
120                <action android:name="android.intent.action.VIEW" />
120-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
120-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
121
122                <category android:name="android.intent.category.LAUNCHER" />
122-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:17-76
122-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:27-74
123            </intent-filter>
124        </activity>
125        <activity
125-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:53:9-56:19
126            android:name="drawing.jaraappskids.kidsdrawing.activities.MainActivity"
126-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:54:17-56
127            android:screenOrientation="portrait" />
127-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:55:17-53
128        <activity
128-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:58:9-61:19
129            android:name="drawing.jaraappskids.kidsdrawing.activities.BGImageListActivity"
129-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:59:17-63
130            android:screenOrientation="portrait" />
130-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:60:17-53
131
132        <!-- Old editor activity - replaced with modern version -->
133        <!--
134        <activity
135                android:name=".activities.EditorActivity"
136                android:screenOrientation="portrait"
137                android:windowSoftInputMode="adjustNothing"
138                />
139        -->
140
141
142        <!-- Modern Drawing Editor -->
143        <activity
143-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:73:9-79:19
144            android:name="drawing.jaraappskids.kidsdrawing.editor.ui.ModernEditorActivity"
144-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:74:17-63
145            android:hardwareAccelerated="true"
145-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:77:17-51
146            android:screenOrientation="portrait"
146-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:75:17-53
147            android:theme="@style/Theme.Material3.DayNight.NoActionBar"
147-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:78:17-76
148            android:windowSoftInputMode="adjustNothing" />
148-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:76:17-60
149        <activity
149-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:81:9-83:55
150            android:name="drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity"
150-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:82:17-54
151            android:screenOrientation="portrait" />
151-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:83:17-53
152        <activity
152-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:84:9-86:55
153            android:name="drawing.jaraappskids.kidsdrawing.mywork.FullScreenActivity"
153-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:85:17-58
154            android:screenOrientation="portrait" />
154-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:86:17-53
155
156        <meta-data
156-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:88:9-90:65
157            android:name="com.google.android.gms.ads.APPLICATION_ID"
157-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:89:17-73
158            android:value="@string/AD_MOB_APPLICATION_ID" />
158-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:90:17-62
159        <meta-data
159-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:92:9-94:73
160            android:name="com.google.android.gms.version"
160-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:93:17-62
161            android:value="@integer/google_play_services_version" />
161-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:94:17-70
162
163        <property
163-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:96:9-99:48
164            android:name="android.adservices.AD_SERVICES_CONFIG"
164-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:97:13-65
165            android:resource="@xml/gma_ad_services_config" />
165-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:98:13-59
166
167        <!-- Firebase messaging service removed - was causing crashes without proper configuration -->
168        <!--
169        <service
170                android:name=".firebase.MyFirebaseMessagingService"
171                android:exported="false">
172            <intent-filter>
173                <action android:name="com.google.firebase.MESSAGING_EVENT" />
174                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
175            </intent-filter>
176        </service>
177        -->
178        <activity
178-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:28:9-32:20
179            android:name="com.google.android.tv.ads.controls.FallbackImageActivity"
179-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:29:13-84
180            android:exported="false"
180-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:30:13-37
181            android:theme="@style/Theme.AppCompat.NoActionBar.Translucent" >
181-->[com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b608133b9f6151e8ab6ea0b248ed861e\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:31:13-75
182        </activity>
183        <activity
183-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
184            android:name="com.google.android.gms.common.api.GoogleApiActivity"
184-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
185            android:exported="false"
185-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
186            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
186-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6128e6b97d809c1b0cadcc023b8fe28\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
187        <activity
187-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
188            android:name="com.google.android.gms.ads.AdActivity"
188-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
189            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
189-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
190            android:exported="false"
190-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
191            android:theme="@android:style/Theme.Translucent" />
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
192
193        <provider
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
194            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
194-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
195            android:authorities="drawing.jaraappskids.kidsdrawing.mobileadsinitprovider"
195-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
196            android:exported="false"
196-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
197            android:initOrder="100" />
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
198
199        <service
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
200            android:name="com.google.android.gms.ads.AdService"
200-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
201            android:enabled="true"
201-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
202            android:exported="false" />
202-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
203
204        <activity
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
205            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
205-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
206            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
206-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
207            android:exported="false" />
207-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
208        <activity
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
209            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
210            android:excludeFromRecents="true"
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
211            android:exported="false"
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
212            android:launchMode="singleTask"
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
213            android:taskAffinity=""
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
214            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
215
216        <meta-data
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
217            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
217-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
218            android:value="true" />
218-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
219        <meta-data
219-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
220            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
221            android:value="true" />
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
222
223        <activity
223-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
224            android:name="androidx.compose.ui.tooling.PreviewActivity"
224-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
225            android:exported="true" />
225-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\55e7c9cbc1d6dd133da7f3b2c1483b2b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
226
227        <uses-library
227-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
228            android:name="android.ext.adservices"
228-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
229            android:required="false" />
229-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\91057cc8bbc32afe5bd828ec4f317497\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
230
231        <provider
231-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
232            android:name="androidx.startup.InitializationProvider"
232-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
233            android:authorities="drawing.jaraappskids.kidsdrawing.androidx-startup"
233-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
234            android:exported="false" >
234-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
235            <meta-data
235-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
236                android:name="androidx.work.WorkManagerInitializer"
236-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
237                android:value="androidx.startup" />
237-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
238            <meta-data
238-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
239                android:name="androidx.emoji2.text.EmojiCompatInitializer"
239-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
240                android:value="androidx.startup" />
240-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f8836a65faca206b59415ea25454c12\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
241            <meta-data
241-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
242                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
242-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
243                android:value="androidx.startup" />
243-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f812ce9c4a77f1a5a7c3d1418b2a50\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
244            <meta-data
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
245                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
246                android:value="androidx.startup" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
247        </provider>
248
249        <service
249-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
250            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
250-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
252            android:enabled="@bool/enable_system_alarm_service_default"
252-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
253            android:exported="false" />
253-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
254        <service
254-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
255            android:name="androidx.work.impl.background.systemjob.SystemJobService"
255-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
256            android:directBootAware="false"
256-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
257            android:enabled="@bool/enable_system_job_service_default"
257-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
258            android:exported="true"
258-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
259            android:permission="android.permission.BIND_JOB_SERVICE" />
259-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
260        <service
260-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
261            android:name="androidx.work.impl.foreground.SystemForegroundService"
261-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
263            android:enabled="@bool/enable_system_foreground_service_default"
263-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
264            android:exported="false" />
264-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
265
266        <receiver
266-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
267            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
267-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
269            android:enabled="true"
269-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
270            android:exported="false" />
270-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
271        <receiver
271-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
272            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
272-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
274            android:enabled="false"
274-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
275            android:exported="false" >
275-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
276            <intent-filter>
276-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
277                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
278                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
279            </intent-filter>
280        </receiver>
281        <receiver
281-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
282            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
282-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
283            android:directBootAware="false"
283-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
284            android:enabled="false"
284-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
285            android:exported="false" >
285-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
286            <intent-filter>
286-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
287                <action android:name="android.intent.action.BATTERY_OKAY" />
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
288                <action android:name="android.intent.action.BATTERY_LOW" />
288-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
288-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
289            </intent-filter>
290        </receiver>
291        <receiver
291-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
292            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
292-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
293            android:directBootAware="false"
293-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
294            android:enabled="false"
294-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
295            android:exported="false" >
295-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
296            <intent-filter>
296-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
297                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
298                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
299            </intent-filter>
300        </receiver>
301        <receiver
301-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
302            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
302-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
303            android:directBootAware="false"
303-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
304            android:enabled="false"
304-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
305            android:exported="false" >
305-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
306            <intent-filter>
306-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
307                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
308            </intent-filter>
309        </receiver>
310        <receiver
310-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
311            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
311-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
313            android:enabled="false"
313-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
314            android:exported="false" >
314-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
315            <intent-filter>
315-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
316                <action android:name="android.intent.action.BOOT_COMPLETED" />
316-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
316-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
317                <action android:name="android.intent.action.TIME_SET" />
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
318                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
318-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
318-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
319            </intent-filter>
320        </receiver>
321        <receiver
321-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
322            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
322-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
324            android:enabled="@bool/enable_system_alarm_service_default"
324-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
325            android:exported="false" >
325-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
326            <intent-filter>
326-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
327                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
327-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
327-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
331            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
331-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
333            android:enabled="true"
333-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
334            android:exported="true"
334-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
335            android:permission="android.permission.DUMP" >
335-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
336            <intent-filter>
336-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
337                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
337-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
337-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd99526a1072fab824d3f7f988d627ea\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
338            </intent-filter>
339        </receiver>
340
341        <uses-library
341-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
342            android:name="androidx.window.extensions"
342-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
343            android:required="false" />
343-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
344        <uses-library
344-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
345            android:name="androidx.window.sidecar"
345-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
346            android:required="false" />
346-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f8c303c8ad769dea6b983356ba03f93\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
347
348        <provider
348-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:32:9-40:20
349            android:name="leakcanary.internal.LeakCanaryFileProvider"
349-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:33:13-70
350            android:authorities="com.squareup.leakcanary.fileprovider.drawing.jaraappskids.kidsdrawing"
350-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:34:13-88
351            android:exported="false"
351-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:35:13-37
352            android:grantUriPermissions="true" >
352-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:36:13-47
353            <meta-data
353-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:36:13-38:57
354                android:name="android.support.FILE_PROVIDER_PATHS"
354-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:37:21-71
355                android:resource="@xml/leak_canary_file_paths" />
355-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:38:21-55
356        </provider>
357
358        <activity
358-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:42:9-73:20
359            android:name="leakcanary.internal.activity.LeakActivity"
359-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:43:13-69
360            android:exported="true"
360-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:44:13-36
361            android:icon="@mipmap/leak_canary_icon"
361-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:45:13-52
362            android:label="@string/leak_canary_display_activity_label"
362-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:46:13-71
363            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
363-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:47:13-76
364            android:theme="@style/leak_canary_LeakCanary.Base" >
364-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:48:13-63
365            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
365-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:13-72:29
365-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:28-81
366                <action android:name="android.intent.action.VIEW" />
366-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
366-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
367
368                <category android:name="android.intent.category.DEFAULT" />
368-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:17-76
368-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:27-73
369                <category android:name="android.intent.category.BROWSABLE" />
369-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
369-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4db35ed2a666db050b55fa8d7284114a\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
370
371                <data android:scheme="file" />
371-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
371-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
372                <data android:scheme="content" />
372-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
372-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
373                <data android:mimeType="*/*" />
373-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
374                <data android:host="*" />
374-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
375                <data android:pathPattern=".*\\.hprof" />
375-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
376                <data android:pathPattern=".*\\..*\\.hprof" />
376-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
377                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
377-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
378                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
378-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
379                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
379-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
380                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
380-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
381                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
381-->[com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c2a699134d8a4fe6b5f846c5d280ca9\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
382                <!--
383            Since hprof isn't a standard MIME type, we have to declare such patterns.
384            Most file providers will generate URIs including their own package name,
385            which contains `.` characters that must be explicitly escaped in pathPattern.
386            @see https://stackoverflow.com/a/31028507/703646
387                -->
388            </intent-filter>
389        </activity>
390
391        <activity-alias
391-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:75:9-92:26
392            android:name="leakcanary.internal.activity.LeakLauncherActivity"
392-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:76:13-77
393            android:banner="@drawable/leak_canary_tv_icon"
393-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:77:13-59
394            android:enabled="@bool/leak_canary_add_launcher_icon"
394-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:78:13-66
395            android:exported="true"
395-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:79:13-36
396            android:icon="@mipmap/leak_canary_icon"
396-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:80:13-52
397            android:label="@string/leak_canary_display_activity_label"
397-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:81:13-71
398            android:targetActivity="leakcanary.internal.activity.LeakActivity"
398-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:82:13-79
399            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
399-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:83:13-76
400            android:theme="@style/leak_canary_LeakCanary.Base" >
400-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:84:13-63
401            <intent-filter>
401-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:85:13-91:29
402                <action android:name="android.intent.action.MAIN" />
402-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:17-68
402-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:25-66
403
404                <category android:name="android.intent.category.LAUNCHER" />
404-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:17-76
404-->D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:27-74
405                <!-- Android TV launcher intent -->
406                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
406-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:17-86
406-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:27-83
407            </intent-filter>
408        </activity-alias>
409
410        <activity
410-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:94:9-100:68
411            android:name="leakcanary.internal.RequestPermissionActivity"
411-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:95:13-73
412            android:excludeFromRecents="true"
412-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:96:13-46
413            android:icon="@mipmap/leak_canary_icon"
413-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:97:13-52
414            android:label="@string/leak_canary_storage_permission_activity_label"
414-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:98:13-82
415            android:taskAffinity="com.squareup.leakcanary.drawing.jaraappskids.kidsdrawing"
415-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:99:13-76
416            android:theme="@style/leak_canary_Theme.Transparent" />
416-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:100:13-65
417
418        <receiver android:name="leakcanary.internal.NotificationReceiver" />
418-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:9-77
418-->[com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\16d152fbe7f28d80b4cbac082314f7d8\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:19-74
419
420        <provider
420-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:8:9-12:40
421            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
421-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:9:13-78
422            android:authorities="drawing.jaraappskids.kidsdrawing.leakcanary-installer"
422-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:10:13-72
423            android:enabled="@bool/leak_canary_watcher_auto_install"
423-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:11:13-69
424            android:exported="false" />
424-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\2dbc804d4da0329e52ec14c6bb9b8dde\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:12:13-37
425        <provider
425-->[com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:8:9-12:40
426            android:name="leakcanary.internal.PlumberInstaller"
426-->[com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:9:13-64
427            android:authorities="drawing.jaraappskids.kidsdrawing.plumber-installer"
427-->[com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:10:13-69
428            android:enabled="@bool/leak_canary_plumber_auto_install"
428-->[com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:11:13-69
429            android:exported="false" />
429-->[com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\7e228ae3989bfa0e401f96ad730544e5\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:12:13-37
430
431        <service
431-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
432            android:name="androidx.room.MultiInstanceInvalidationService"
432-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
433            android:directBootAware="true"
433-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
434            android:exported="false" />
434-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8efcbcfc476a9d999a30200feb90f23b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
435
436        <receiver
436-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
437            android:name="androidx.profileinstaller.ProfileInstallReceiver"
437-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
438            android:directBootAware="false"
438-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
439            android:enabled="true"
439-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
440            android:exported="true"
440-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
441            android:permission="android.permission.DUMP" >
441-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
442            <intent-filter>
442-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
443                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
443-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
443-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
444            </intent-filter>
445            <intent-filter>
445-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
446                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
446-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
446-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
447            </intent-filter>
448            <intent-filter>
448-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
449                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
449-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
449-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
450            </intent-filter>
451            <intent-filter>
451-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
452                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
452-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
452-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\1449889ae430de6cbc0c1af09fc38476\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
453            </intent-filter>
454        </receiver>
455
456        <service
456-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
457            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
457-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
458            android:exported="false" >
458-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
459            <meta-data
459-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
460                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
460-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
461                android:value="cct" />
461-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ffc2df510d8936fda890da35f084dbee\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
462        </service>
463        <service
463-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
464            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
464-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
465            android:exported="false"
465-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
466            android:permission="android.permission.BIND_JOB_SERVICE" >
466-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
467        </service>
468
469        <receiver
469-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
470            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
470-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
471            android:exported="false" />
471-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\ccfa8e87bcff3e858370c9c4507bf378\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
472    </application>
473
474</manifest>
