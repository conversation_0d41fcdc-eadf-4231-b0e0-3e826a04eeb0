package drawing.jaraappskids.kidsdrawing.editor.data

import android.graphics.*
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.*

/**
 * Modern drawing data models for the new editor
 */

@Parcelize
data class DrawingPoint(
    val x: Float,
    val y: Float,
    val pressure: Float = 1.0f,
    val timestamp: Long = System.currentTimeMillis()
) : Parcelable

@Parcelize
data class DrawingStroke(
    val id: String = UUID.randomUUID().toString(),
    val points: List<DrawingPoint>,
    val brushType: BrushType,
    val color: Int,
    val strokeWidth: Float,
    val opacity: Float = 1.0f,
    val layerId: String,
    val timestamp: Long = System.currentTimeMillis()
) : Parcelable

@Parcelize
enum class BrushType : Parcelable {
    PENCIL,
    MARKER,
    AIRBRUSH,
    ERASER,
    HIGHLIGHTER
}

@Parcelize
data class DrawingLayer(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val isVisible: Boolean = true,
    val opacity: Float = 1.0f,
    val blendMode: BlendMode = BlendMode.NORMAL,
    val strokes: MutableList<DrawingStroke> = mutableListOf(),
    val order: Int = 0
) : Parcelable

@Parcelize
enum class BlendMode : Parcelable {
    NORMAL,
    MULTIPLY,
    SCREEN,
    OVERLAY,
    SOFT_LIGHT,
    HARD_LIGHT
}

@Parcelize
data class DrawingShape(
    val id: String = UUID.randomUUID().toString(),
    val type: ShapeType,
    val startPoint: DrawingPoint,
    val endPoint: DrawingPoint,
    val color: Int,
    val strokeWidth: Float,
    val fillColor: Int? = null,
    val layerId: String
) : Parcelable

@Parcelize
enum class ShapeType : Parcelable {
    RECTANGLE,
    CIRCLE,
    LINE,
    ARROW
}

@Parcelize
data class DrawingText(
    val id: String = UUID.randomUUID().toString(),
    val text: String,
    val position: DrawingPoint,
    val color: Int,
    val fontSize: Float,
    val fontFamily: String = "default",
    val isBold: Boolean = false,
    val isItalic: Boolean = false,
    val layerId: String
) : Parcelable

@Parcelize
data class DrawingSticker(
    val id: String = UUID.randomUUID().toString(),
    val resourceId: Int,
    val position: DrawingPoint,
    val scale: Float = 1.0f,
    val rotation: Float = 0.0f,
    val layerId: String
) : Parcelable

@Parcelize
data class DrawingCanvas(
    val width: Int,
    val height: Int,
    val backgroundColor: Int = Color.WHITE,
    val layers: MutableList<DrawingLayer> = mutableListOf(),
    val activeLayerId: String? = null
) : Parcelable {
    
    fun getActiveLayer(): DrawingLayer? {
        return layers.find { it.id == activeLayerId }
    }
    
    fun addLayer(layer: DrawingLayer) {
        layers.add(layer)
        if (activeLayerId == null) {
            activeLayerId = layer.id
        }
    }
    
    fun removeLayer(layerId: String) {
        layers.removeAll { it.id == layerId }
        if (activeLayerId == layerId) {
            activeLayerId = layers.firstOrNull()?.id
        }
    }
}

data class BrushSettings(
    val type: BrushType = BrushType.PENCIL,
    val color: Int = Color.BLACK,
    val size: Float = 10f,
    val opacity: Float = 1.0f,
    val pressureSensitive: Boolean = true,
    val smoothing: Float = 0.5f
)

data class CanvasSettings(
    val width: Int,
    val height: Int,
    val backgroundColor: Int = Color.WHITE,
    val showGrid: Boolean = false,
    val gridSize: Int = 50,
    val enablePressure: Boolean = true,
    val maxUndoSteps: Int = 50
)

sealed class DrawingAction {
    data class AddStroke(val stroke: DrawingStroke) : DrawingAction()
    data class AddShape(val shape: DrawingShape) : DrawingAction()
    data class AddText(val text: DrawingText) : DrawingAction()
    data class AddSticker(val sticker: DrawingSticker) : DrawingAction()
    data class RemoveElement(val elementId: String) : DrawingAction()
    data class UpdateLayer(val layer: DrawingLayer) : DrawingAction()
    data class AddLayer(val layer: DrawingLayer) : DrawingAction()
    data class RemoveLayer(val layerId: String) : DrawingAction()
    data class SetActiveLayer(val layerId: String) : DrawingAction()
    object Clear : DrawingAction()
}

data class DrawingState(
    val canvas: DrawingCanvas,
    val brushSettings: BrushSettings,
    val canvasSettings: CanvasSettings,
    val selectedTool: DrawingTool = DrawingTool.BRUSH,
    val isDrawing: Boolean = false,
    val undoStack: List<DrawingAction> = emptyList(),
    val redoStack: List<DrawingAction> = emptyList()
)

enum class DrawingTool {
    BRUSH,
    ERASER,
    SHAPE_RECTANGLE,
    SHAPE_CIRCLE,
    SHAPE_LINE,
    TEXT,
    STICKER,
    EYEDROPPER
}

data class ExportSettings(
    val format: ExportFormat,
    val quality: Int = 100,
    val includeBackground: Boolean = true,
    val transparentBackground: Boolean = false
)

enum class ExportFormat {
    PNG,
    JPEG,
    SVG,
    PDF
}
