package drawing.jaraappskids.kidsdrawing.activities;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u0004:\u0001,B\u0005\u00a2\u0006\u0002\u0010\u0005J\b\u0010\u0013\u001a\u00020\u0014H\u0016J\b\u0010\u0015\u001a\u00020\u0014H\u0016J\b\u0010\u0016\u001a\u00020\u0014H\u0002J\b\u0010\u0017\u001a\u00020\u0014H\u0002J\b\u0010\u0018\u001a\u00020\u0014H\u0002J\b\u0010\u0019\u001a\u00020\u0014H\u0002J\b\u0010\u001a\u001a\u00020\u0014H\u0002J\b\u0010\u001b\u001a\u00020\u0014H\u0002J\b\u0010\u001c\u001a\u00020\u0014H\u0002J\b\u0010\u001d\u001a\u00020\u0014H\u0002J\u0010\u0010\u001e\u001a\u00020\u00142\u0006\u0010\u001f\u001a\u00020\u000eH\u0016J\u0012\u0010 \u001a\u00020\u00142\b\u0010!\u001a\u0004\u0018\u00010\"H\u0016J\u0012\u0010#\u001a\u00020\u00142\b\u0010$\u001a\u0004\u0018\u00010%H\u0014J\b\u0010&\u001a\u00020\u0014H\u0016J\b\u0010\'\u001a\u00020\u0014H\u0014J\b\u0010(\u001a\u00020\u0014H\u0002J\b\u0010)\u001a\u00020\u0014H\u0002J\b\u0010*\u001a\u00020\u0014H\u0002J\b\u0010+\u001a\u00020\u0014H\u0016R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u0010\"\u0004\b\u0011\u0010\u0012\u00a8\u0006-"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/activities/BGImageListActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdapterItemCallback;", "Landroid/view/View$OnClickListener;", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdsCallback;", "()V", "binding", "Ldrawing/jaraappskids/kidsdrawing/databinding/ActivityBgImageListBinding;", "isEasy", "", "levelClassArrayList", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/pojo/BGImageClass;", "position", "", "getPosition", "()I", "setPosition", "(I)V", "adClose", "", "adLoadingFailed", "animateBackButton", "animateFloatingClouds", "animateSparkles", "animateTitleBounce", "checkAd", "initViews", "loadAdsIfOnline", "loadDataFromIntent", "onAdapterItemClick", "mPos", "onClick", "v", "Landroid/view/View;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onLoaded", "onResume", "showOfflineModeToast", "startEditorActivity", "startKidFriendlyAnimations", "startNextScreen", "LoadImageListAsync", "app_debug"})
public final class BGImageListActivity extends androidx.appcompat.app.AppCompatActivity implements drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback, android.view.View.OnClickListener, drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback {
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<drawing.jaraappskids.kidsdrawing.pojo.BGImageClass> levelClassArrayList;
    private boolean isEasy = true;
    private drawing.jaraappskids.kidsdrawing.databinding.ActivityBgImageListBinding binding;
    private int position = 0;
    
    public BGImageListActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    private final void initViews() {
    }
    
    /**
     * Fun animations to make the UI come alive for kids
     */
    private final void startKidFriendlyAnimations() {
    }
    
    private final void animateFloatingClouds() {
    }
    
    private final void animateSparkles() {
    }
    
    private final void animateTitleBounce() {
    }
    
    private final void animateBackButton() {
    }
    
    private final void loadDataFromIntent() {
    }
    
    @java.lang.Override()
    public void onClick(@org.jetbrains.annotations.Nullable()
    android.view.View v) {
    }
    
    public final int getPosition() {
        return 0;
    }
    
    public final void setPosition(int p0) {
    }
    
    @java.lang.Override()
    public void onAdapterItemClick(int mPos) {
    }
    
    private final void checkAd() {
    }
    
    private final void startEditorActivity() {
    }
    
    @java.lang.Override()
    public void adLoadingFailed() {
    }
    
    @java.lang.Override()
    public void adClose() {
    }
    
    @java.lang.Override()
    public void startNextScreen() {
    }
    
    @java.lang.Override()
    public void onLoaded() {
    }
    
    /**
     * Smart ad loading with internet connectivity check
     * Only loads ads if internet is available for better user experience
     */
    private final void loadAdsIfOnline() {
    }
    
    /**
     * Show offline mode indication to user (optional)
     */
    private final void showOfflineModeToast() {
    }
    
    @java.lang.Override()
    public void onAdClicked() {
    }
    
    @java.lang.Override()
    public void onAdFailedToShow(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    @java.lang.Override()
    public void onAdImpression() {
    }
    
    @java.lang.Override()
    public void onAdShown() {
    }
    
    @java.lang.Override()
    public void onRewardEarned(@org.jetbrains.annotations.NotNull()
    java.lang.String type, int amount) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0087\u0004\u0018\u00002\u0014\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u0001B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\'\u0010\f\u001a\u0004\u0018\u00010\u00022\u0016\u0010\r\u001a\f\u0012\b\b\u0001\u0012\u0004\u0018\u00010\u00020\u000e\"\u0004\u0018\u00010\u0002H\u0014\u00a2\u0006\u0002\u0010\u000fJ\u0012\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0002H\u0014J\b\u0010\u0013\u001a\u00020\u0011H\u0014R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000b\u00a8\u0006\u0014"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/activities/BGImageListActivity$LoadImageListAsync;", "Landroid/os/AsyncTask;", "Ljava/lang/Void;", "context", "Landroid/content/Context;", "(Ldrawing/jaraappskids/kidsdrawing/activities/BGImageListActivity;Landroid/content/Context;)V", "pDialog", "Landroid/app/ProgressDialog;", "getPDialog", "()Landroid/app/ProgressDialog;", "setPDialog", "(Landroid/app/ProgressDialog;)V", "doInBackground", "params", "", "([Ljava/lang/Void;)Ljava/lang/Void;", "onPostExecute", "", "result", "onPreExecute", "app_debug"})
    @android.annotation.SuppressLint(value = {"StaticFieldLeak"})
    public final class LoadImageListAsync extends android.os.AsyncTask<java.lang.Void, java.lang.Void, java.lang.Void> {
        @org.jetbrains.annotations.NotNull()
        private final android.content.Context context = null;
        public android.app.ProgressDialog pDialog;
        
        public LoadImageListAsync(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.app.ProgressDialog getPDialog() {
            return null;
        }
        
        public final void setPDialog(@org.jetbrains.annotations.NotNull()
        android.app.ProgressDialog p0) {
        }
        
        @java.lang.Override()
        protected void onPreExecute() {
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        protected java.lang.Void doInBackground(@org.jetbrains.annotations.NotNull()
        java.lang.Void... params) {
            return null;
        }
        
        @java.lang.Override()
        protected void onPostExecute(@org.jetbrains.annotations.Nullable()
        java.lang.Void result) {
        }
    }
}