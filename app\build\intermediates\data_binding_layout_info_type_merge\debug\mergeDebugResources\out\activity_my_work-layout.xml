<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_my_work" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_my_work.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_my_work_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="14"/></Target><Target id="@+id/rltMain" view="RelativeLayout"><Expressions/><location startLine="10" startOffset="4" endLine="75" endOffset="20"/></Target><Target id="@+id/rltTop" view="RelativeLayout"><Expressions/><location startLine="16" startOffset="8" endLine="54" endOffset="24"/></Target><Target id="@+id/ivBack" view="ImageView"><Expressions/><location startLine="30" startOffset="16" endLine="37" endOffset="62"/></Target><Target id="@+id/tvTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="40" startOffset="16" endLine="50" endOffset="63"/></Target><Target id="@+id/gridOfMyWork" view="GridView"><Expressions/><location startLine="56" startOffset="8" endLine="65" endOffset="46"/></Target><Target id="@+id/txtNoItem" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="73" endOffset="43"/></Target><Target id="@+id/llAdView" view="RelativeLayout"><Expressions/><location startLine="78" startOffset="4" endLine="84" endOffset="42"/></Target><Target id="@+id/llAdViewFacebook" view="LinearLayout"><Expressions/><location startLine="86" startOffset="4" endLine="91" endOffset="42"/></Target></Targets></Layout>