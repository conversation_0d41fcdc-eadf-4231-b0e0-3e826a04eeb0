package drawing.jaraappskids.kidsdrawing.editor.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import drawing.jaraappskids.kidsdrawing.editor.data.*
import kotlinx.coroutines.*
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * Modern drawing view with hardware acceleration and smooth performance
 */
class ModernDrawingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Drawing state
    private var drawingCanvas: DrawingCanvas? = null
    private var brushSettings = BrushSettings()
    private var selectedTool = DrawingTool.BRUSH
    
    // Current stroke being drawn
    private var currentStroke: MutableList<DrawingPoint> = mutableListOf()
    private var isDrawing = false
    
    // Paint objects for performance
    private val strokePaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeCap = Paint.Cap.ROUND
        strokeJoin = Paint.Join.ROUND
    }
    
    private val backgroundPaint = Paint().apply {
        style = Paint.Style.FILL
    }
    
    // Path for smooth drawing
    private val currentPath = Path()
    private var lastX = 0f
    private var lastY = 0f
    
    // Performance optimization
    private var canvasBitmap: Bitmap? = null
    private var bitmapCanvas: Canvas? = null
    private var isDirty = true
    
    // Callbacks
    var onStrokeCompleted: ((DrawingStroke) -> Unit)? = null
    var onDrawingStateChanged: ((Boolean) -> Unit)? = null
    
    init {
        // Enable hardware acceleration
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }
    
    fun setDrawingCanvas(canvas: DrawingCanvas) {
        this.drawingCanvas = canvas
        isDirty = true
        invalidate()
    }
    
    fun setBrushSettings(settings: BrushSettings) {
        this.brushSettings = settings
        updatePaintFromBrush()
    }
    
    fun setSelectedTool(tool: DrawingTool) {
        this.selectedTool = tool
        updatePaintFromBrush()
    }
    
    private fun updatePaintFromBrush() {
        strokePaint.apply {
            color = brushSettings.color
            strokeWidth = brushSettings.size
            alpha = (brushSettings.opacity * 255).toInt()
            
            when (brushSettings.type) {
                BrushType.PENCIL -> {
                    maskFilter = null
                    pathEffect = null
                }
                BrushType.MARKER -> {
                    maskFilter = BlurMaskFilter(2f, BlurMaskFilter.Blur.NORMAL)
                    pathEffect = null
                }
                BrushType.AIRBRUSH -> {
                    maskFilter = BlurMaskFilter(brushSettings.size * 0.3f, BlurMaskFilter.Blur.NORMAL)
                    pathEffect = null
                }
                BrushType.ERASER -> {
                    xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
                    maskFilter = null
                }
                BrushType.HIGHLIGHTER -> {
                    alpha = 128
                    xfermode = PorterDuffXfermode(PorterDuff.Mode.MULTIPLY)
                }
            }
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        // Create bitmap for drawing
        if (w > 0 && h > 0) {
            canvasBitmap?.recycle()
            canvasBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            bitmapCanvas = Canvas(canvasBitmap!!)
            isDirty = true
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val bitmap = canvasBitmap ?: return
        val bCanvas = bitmapCanvas ?: return
        
        // Redraw everything if dirty
        if (isDirty) {
            redrawCanvas(bCanvas)
            isDirty = false
        }
        
        // Draw the bitmap to screen
        canvas.drawBitmap(bitmap, 0f, 0f, null)
        
        // Draw current stroke being drawn
        if (isDrawing && currentPath.isEmpty.not()) {
            canvas.drawPath(currentPath, strokePaint)
        }
    }
    
    private fun redrawCanvas(canvas: Canvas) {
        val drawingCanvas = this.drawingCanvas ?: return
        
        // Clear canvas
        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        
        // Draw background
        backgroundPaint.color = drawingCanvas.backgroundColor
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), backgroundPaint)
        
        // Draw layers in order
        drawingCanvas.layers.sortedBy { it.order }.forEach { layer ->
            if (layer.isVisible) {
                drawLayer(canvas, layer)
            }
        }
    }
    
    private fun drawLayer(canvas: Canvas, layer: DrawingLayer) {
        layer.strokes.forEach { stroke ->
            drawStroke(canvas, stroke)
        }
    }
    
    private fun drawStroke(canvas: Canvas, stroke: DrawingStroke) {
        if (stroke.points.size < 2) return
        
        val paint = Paint(strokePaint).apply {
            color = stroke.color
            strokeWidth = stroke.strokeWidth
            alpha = (stroke.opacity * 255).toInt()
        }
        
        val path = createSmoothPath(stroke.points)
        canvas.drawPath(path, paint)
    }
    
    private fun createSmoothPath(points: List<DrawingPoint>): Path {
        val path = Path()
        if (points.isEmpty()) return path
        
        val firstPoint = points.first()
        path.moveTo(firstPoint.x, firstPoint.y)
        
        if (points.size == 1) {
            path.lineTo(firstPoint.x, firstPoint.y)
            return path
        }
        
        // Use quadratic curves for smoothness
        for (i in 1 until points.size) {
            val currentPoint = points[i]
            val previousPoint = points[i - 1]
            
            if (i == 1) {
                path.lineTo(currentPoint.x, currentPoint.y)
            } else {
                val midX = (previousPoint.x + currentPoint.x) / 2
                val midY = (previousPoint.y + currentPoint.y) / 2
                path.quadTo(previousPoint.x, previousPoint.y, midX, midY)
            }
        }
        
        return path
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (selectedTool == DrawingTool.BRUSH || selectedTool == DrawingTool.ERASER) {
            return handleDrawingTouch(event)
        }
        
        return super.onTouchEvent(event)
    }
    
    private fun handleDrawingTouch(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        val pressure = if (brushSettings.pressureSensitive) event.pressure else 1.0f
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startDrawing(x, y, pressure)
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                continueDrawing(x, y, pressure)
                return true
            }
            MotionEvent.ACTION_UP -> {
                finishDrawing(x, y, pressure)
                return true
            }
        }
        
        return false
    }
    
    private fun startDrawing(x: Float, y: Float, pressure: Float) {
        isDrawing = true
        currentStroke.clear()
        currentPath.reset()
        
        val point = DrawingPoint(x, y, pressure)
        currentStroke.add(point)
        
        currentPath.moveTo(x, y)
        lastX = x
        lastY = y
        
        onDrawingStateChanged?.invoke(true)
        invalidate()
    }
    
    private fun continueDrawing(x: Float, y: Float, pressure: Float) {
        if (!isDrawing) return
        
        val dx = abs(x - lastX)
        val dy = abs(y - lastY)
        
        // Only add point if moved enough (for performance)
        if (dx >= 4 || dy >= 4) {
            val point = DrawingPoint(x, y, pressure)
            currentStroke.add(point)
            
            // Use quadratic curve for smoothness
            val controlX = (lastX + x) / 2
            val controlY = (lastY + y) / 2
            currentPath.quadTo(lastX, lastY, controlX, controlY)
            
            lastX = x
            lastY = y
            
            invalidate()
        }
    }
    
    private fun finishDrawing(x: Float, y: Float, pressure: Float) {
        if (!isDrawing) return
        
        val point = DrawingPoint(x, y, pressure)
        currentStroke.add(point)
        
        // Create final stroke
        val activeLayer = drawingCanvas?.getActiveLayer()
        if (activeLayer != null && currentStroke.isNotEmpty()) {
            val stroke = DrawingStroke(
                points = currentStroke.toList(),
                brushType = if (selectedTool == DrawingTool.ERASER) BrushType.ERASER else brushSettings.type,
                color = brushSettings.color,
                strokeWidth = brushSettings.size,
                opacity = brushSettings.opacity,
                layerId = activeLayer.id
            )
            
            onStrokeCompleted?.invoke(stroke)
        }
        
        isDrawing = false
        currentStroke.clear()
        currentPath.reset()
        isDirty = true
        
        onDrawingStateChanged?.invoke(false)
        invalidate()
    }
    
    fun refresh() {
        isDirty = true
        invalidate()
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        canvasBitmap?.recycle()
        canvasBitmap = null
        bitmapCanvas = null
    }
}
