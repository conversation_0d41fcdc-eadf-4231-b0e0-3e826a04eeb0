package drawing.jaraappskids.kidsdrawing.magicbrush;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0014\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0004\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u000f\"\u0004\b\u0013\u0010\u0011R\u001a\u0010\u0014\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u000b\"\u0004\b\u0016\u0010\rR\u001a\u0010\u0017\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u000b\"\u0004\b\u0019\u0010\rR\u001a\u0010\u001a\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u000b\"\u0004\b\u001c\u0010\r\u00a8\u0006\u001d"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/GetRandomEffect;", "", "f11x", "", "f12y", "brushEffectLoad", "Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad;", "(FFLdrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad;)V", "color", "", "getColor", "()I", "setColor", "(I)V", "getF11x", "()F", "setF11x", "(F)V", "getF12y", "setF12y", "f1427a", "getF1427a", "setF1427a", "f1429c", "getF1429c", "setF1429c", "f1430d", "getF1430d", "setF1430d", "app_debug"})
public final class GetRandomEffect {
    private float f11x;
    private float f12y;
    private int color;
    private int f1427a = 0;
    private int f1429c = 0;
    private int f1430d = 0;
    
    public GetRandomEffect(float f11x, float f12y, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad brushEffectLoad) {
        super();
    }
    
    public final float getF11x() {
        return 0.0F;
    }
    
    public final void setF11x(float p0) {
    }
    
    public final float getF12y() {
        return 0.0F;
    }
    
    public final void setF12y(float p0) {
    }
    
    public final int getColor() {
        return 0;
    }
    
    public final void setColor(int p0) {
    }
    
    public final int getF1427a() {
        return 0;
    }
    
    public final void setF1427a(int p0) {
    }
    
    public final int getF1429c() {
        return 0;
    }
    
    public final void setF1429c(int p0) {
    }
    
    public final int getF1430d() {
        return 0;
    }
    
    public final void setF1430d(int p0) {
    }
}