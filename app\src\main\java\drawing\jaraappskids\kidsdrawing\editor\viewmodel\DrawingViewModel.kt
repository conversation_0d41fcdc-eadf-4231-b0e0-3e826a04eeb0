package drawing.jaraappskids.kidsdrawing.editor.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import drawing.jaraappskids.kidsdrawing.editor.data.*
import drawing.jaraappskids.kidsdrawing.editor.repository.DrawingRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the modern drawing editor
 */
@HiltViewModel
class DrawingViewModel @Inject constructor(
    private val repository: DrawingRepository
) : ViewModel() {

    // Expose drawing state
    val drawingState: StateFlow<DrawingState> = repository.drawingState
    
    // UI state
    private val _uiState = MutableStateFlow(DrawingUiState())
    val uiState: StateFlow<DrawingUiState> = _uiState.asStateFlow()
    
    // Events
    private val _events = MutableSharedFlow<DrawingEvent>()
    val events: SharedFlow<DrawingEvent> = _events.asSharedFlow()
    
    init {
        // Initialize with default layer
        if (drawingState.value.canvas.layers.isEmpty()) {
            addLayer("Background")
        }
    }
    
    fun setBrushType(type: BrushType) {
        val currentSettings = drawingState.value.brushSettings
        val newSettings = currentSettings.copy(type = type)
        repository.updateBrushSettings(newSettings)
    }
    
    fun setBrushColor(color: Int) {
        val currentSettings = drawingState.value.brushSettings
        val newSettings = currentSettings.copy(color = color)
        repository.updateBrushSettings(newSettings)
    }
    
    fun setBrushSize(size: Float) {
        val currentSettings = drawingState.value.brushSettings
        val newSettings = currentSettings.copy(size = size)
        repository.updateBrushSettings(newSettings)
    }
    
    fun setBrushOpacity(opacity: Float) {
        val currentSettings = drawingState.value.brushSettings
        val newSettings = currentSettings.copy(opacity = opacity)
        repository.updateBrushSettings(newSettings)
    }
    
    fun setSelectedTool(tool: DrawingTool) {
        repository.setSelectedTool(tool)
        _uiState.value = _uiState.value.copy(selectedTool = tool)
    }
    
    fun addStroke(stroke: DrawingStroke) {
        repository.executeAction(DrawingAction.AddStroke(stroke))
    }
    
    fun undo() {
        val success = repository.undo()
        if (success) {
            emitEvent(DrawingEvent.ActionCompleted("Undo"))
        } else {
            emitEvent(DrawingEvent.ShowMessage("Nothing to undo"))
        }
    }
    
    fun redo() {
        val success = repository.redo()
        if (success) {
            emitEvent(DrawingEvent.ActionCompleted("Redo"))
        } else {
            emitEvent(DrawingEvent.ShowMessage("Nothing to redo"))
        }
    }
    
    fun canUndo(): Boolean = repository.canUndo()
    fun canRedo(): Boolean = repository.canRedo()
    
    fun clearCanvas() {
        repository.clearCanvas()
        emitEvent(DrawingEvent.ActionCompleted("Canvas cleared"))
    }
    
    fun addLayer(name: String) {
        val layerId = repository.addLayer(name)
        emitEvent(DrawingEvent.LayerAdded(layerId, name))
    }
    
    fun removeLayer(layerId: String) {
        repository.removeLayer(layerId)
        emitEvent(DrawingEvent.LayerRemoved(layerId))
    }
    
    fun setActiveLayer(layerId: String) {
        repository.setActiveLayer(layerId)
        emitEvent(DrawingEvent.ActiveLayerChanged(layerId))
    }
    
    fun saveDrawing(filename: String, format: ExportFormat = ExportFormat.PNG) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true)
            
            repository.saveDrawing(filename, format)
                .onSuccess { path ->
                    emitEvent(DrawingEvent.DrawingSaved(path))
                }
                .onFailure { error ->
                    emitEvent(DrawingEvent.Error(error.message ?: "Failed to save drawing"))
                }
            
            _uiState.value = _uiState.value.copy(isSaving = false)
        }
    }
    
    fun showColorPicker() {
        _uiState.value = _uiState.value.copy(showColorPicker = true)
    }
    
    fun hideColorPicker() {
        _uiState.value = _uiState.value.copy(showColorPicker = false)
    }
    
    fun showBrushSettings() {
        _uiState.value = _uiState.value.copy(showBrushSettings = true)
    }
    
    fun hideBrushSettings() {
        _uiState.value = _uiState.value.copy(showBrushSettings = false)
    }
    
    fun showLayerPanel() {
        _uiState.value = _uiState.value.copy(showLayerPanel = true)
    }
    
    fun hideLayerPanel() {
        _uiState.value = _uiState.value.copy(showLayerPanel = false)
    }
    
    fun setDrawingState(isDrawing: Boolean) {
        repository.setDrawingState(isDrawing)
        _uiState.value = _uiState.value.copy(isDrawing = isDrawing)
    }
    
    private fun emitEvent(event: DrawingEvent) {
        viewModelScope.launch {
            _events.emit(event)
        }
    }
    
    // Handle shape drawing
    fun addShape(shape: DrawingShape) {
        repository.executeAction(DrawingAction.AddShape(shape))
    }
    
    // Handle text addition
    fun addText(text: DrawingText) {
        repository.executeAction(DrawingAction.AddText(text))
    }
    
    // Handle sticker addition
    fun addSticker(sticker: DrawingSticker) {
        repository.executeAction(DrawingAction.AddSticker(sticker))
    }
    
    // Performance monitoring
    fun getPerformanceStats(): PerformanceStats {
        val state = drawingState.value
        return PerformanceStats(
            totalStrokes = state.canvas.layers.sumOf { it.strokes.size },
            totalLayers = state.canvas.layers.size,
            undoStackSize = state.undoStack.size,
            redoStackSize = state.redoStack.size,
            canvasSize = "${state.canvas.width}x${state.canvas.height}"
        )
    }
}

data class DrawingUiState(
    val selectedTool: DrawingTool = DrawingTool.BRUSH,
    val showColorPicker: Boolean = false,
    val showBrushSettings: Boolean = false,
    val showLayerPanel: Boolean = false,
    val isDrawing: Boolean = false,
    val isSaving: Boolean = false,
    val isLoading: Boolean = false
)

sealed class DrawingEvent {
    data class ShowMessage(val message: String) : DrawingEvent()
    data class Error(val message: String) : DrawingEvent()
    data class ActionCompleted(val action: String) : DrawingEvent()
    data class DrawingSaved(val path: String) : DrawingEvent()
    data class LayerAdded(val layerId: String, val name: String) : DrawingEvent()
    data class LayerRemoved(val layerId: String) : DrawingEvent()
    data class ActiveLayerChanged(val layerId: String) : DrawingEvent()
}

data class PerformanceStats(
    val totalStrokes: Int,
    val totalLayers: Int,
    val undoStackSize: Int,
    val redoStackSize: Int,
    val canvasSize: String
)
