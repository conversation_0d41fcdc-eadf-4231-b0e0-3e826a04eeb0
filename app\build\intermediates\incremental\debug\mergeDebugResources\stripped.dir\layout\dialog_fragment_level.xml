<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/twenty_dp"
        app:cardElevation="@dimen/fifteen_dp"
        app:cardBackgroundColor="@color/colorWhite"
        android:layout_gravity="center"
        android:layout_margin="@dimen/twenty_dp"
        android:clipChildren="false"
        android:clipToPadding="false">

    <!-- Background gradient overlay for fun effect -->
    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/kids_bg_light_green"
            android:padding="@dimen/twenty_dp"
            android:clipChildren="false"
            android:clipToPadding="false">

        <!-- Header Section with Title -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="@dimen/twenty_dp"
                android:gravity="center_vertical">

            <!-- Fun Title with Emoji and Animation -->
            <TextView
                    android:id="@+id/tvDialogTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="🎨 Pick Your Adventure! 🌟"
                    android:textSize="@dimen/twenty_two_sp"
                    android:textColor="@color/kids_primary_purple"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:letterSpacing="0.1"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"/>

            <!-- Close Button with Fun Design -->
            <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="@dimen/twenty_dp"
                    app:cardElevation="@dimen/five_dp"
                    app:cardBackgroundColor="@color/kids_primary_pink">

                <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivClose"
                        android:layout_width="@dimen/forty_dp"
                        android:layout_height="@dimen/forty_dp"
                        android:src="@drawable/ic_cancel"
                        android:padding="@dimen/_10dp"
                        android:layout_gravity="center"
                        android:tint="@color/colorWhite"
                        android:background="?attr/selectableItemBackgroundBorderless"/>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Fun Description Text -->
        <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Choose how challenging you want your drawing to be! 🎯"
                android:textSize="16sp"
                android:textColor="@color/kids_text_secondary"
                android:gravity="center"
                android:layout_marginBottom="@dimen/twenty_dp"
                android:paddingStart="@dimen/ten_dp"
                android:paddingEnd="@dimen/ten_dp"/>

        <!-- Level Selection Container -->
        <LinearLayout
                android:id="@+id/llLevel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:paddingStart="@dimen/twenty_dp"
                android:paddingEnd="@dimen/twenty_dp"
                android:paddingBottom="@dimen/ten_dp">

            <!-- Easy Level Button with Enhanced Design -->
            <androidx.cardview.widget.CardView
                    android:layout_width="300dp"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="@dimen/twenty_dp"
                    app:cardElevation="@dimen/ten_dp"
                    app:cardBackgroundColor="@color/kids_primary_green"
                    android:layout_marginBottom="@dimen/twenty_dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                        android:id="@+id/btnEasy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/twenty_dp">

                    <!-- Easy Icon with Background -->
                    <androidx.cardview.widget.CardView
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            app:cardCornerRadius="30dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/kids_soft_green"
                            android:layout_marginEnd="@dimen/fifteen_dp">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="😊"
                                android:textSize="32sp"
                                android:layout_gravity="center"/>

                    </androidx.cardview.widget.CardView>

                    <!-- Easy Text Content -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="EASY PEASY! 🌱"
                                android:textSize="@dimen/twenty_four_sp"
                                android:textColor="@color/colorWhite"
                                android:textStyle="bold"
                                android:letterSpacing="0.1"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Perfect for little artists! Simple and fun! ✨"
                                android:textSize="@dimen/fourteen_sp"
                                android:textColor="@color/colorWhite"
                                android:alpha="0.9"
                                android:layout_marginTop="@dimen/five_dp"/>

                    </LinearLayout>

                    <!-- Arrow Icon -->
                    <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="▶"
                            android:textSize="@dimen/twenty_sp"
                            android:textColor="@color/colorWhite"
                            android:alpha="0.8"/>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Hard Level Button with Enhanced Design -->
            <androidx.cardview.widget.CardView
                    android:layout_width="300dp"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="@dimen/twenty_dp"
                    app:cardElevation="@dimen/ten_dp"
                    app:cardBackgroundColor="@color/kids_primary_orange"
                    android:layout_marginBottom="@dimen/fifteen_dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                        android:id="@+id/btnHard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/twenty_dp">

                    <!-- Hard Icon with Background -->
                    <androidx.cardview.widget.CardView
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            app:cardCornerRadius="30dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/kids_soft_orange"
                            android:layout_marginEnd="@dimen/fifteen_dp">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🔥"
                                android:textSize="32sp"
                                android:layout_gravity="center"/>

                    </androidx.cardview.widget.CardView>

                    <!-- Hard Text Content -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="SUPER CHALLENGE! 🚀"
                                android:textSize="@dimen/twenty_four_sp"
                                android:textColor="@color/colorWhite"
                                android:textStyle="bold"
                                android:letterSpacing="0.1"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="For brave artists ready for adventure! 🎪"
                                android:textSize="@dimen/fourteen_sp"
                                android:textColor="@color/colorWhite"
                                android:alpha="0.9"
                                android:layout_marginTop="@dimen/five_dp"/>

                    </LinearLayout>

                    <!-- Arrow Icon -->
                    <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="▶"
                            android:textSize="@dimen/twenty_sp"
                            android:textColor="@color/colorWhite"
                            android:alpha="0.8"/>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Fun Encouragement Text -->
            <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🌈 Don't worry, you can always change levels later! 🌈"
                    android:textSize="12sp"
                    android:textColor="@color/kids_text_hint"
                    android:gravity="center"
                    android:layout_marginTop="@dimen/ten_dp"
                    android:paddingStart="@dimen/ten_dp"
                    android:paddingEnd="@dimen/ten_dp"/>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>