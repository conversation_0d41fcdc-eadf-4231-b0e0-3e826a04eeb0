<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_bg_image_list" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_bg_image_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_bg_image_list_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="305" endOffset="14"/></Target><Target id="@+id/cloudLeft" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="32" endOffset="36"/></Target><Target id="@+id/cloudRight" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="43" endOffset="36"/></Target><Target id="@+id/ivBack" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="55" startOffset="12" endLine="62" endOffset="21"/></Target><Target id="@+id/tvTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="84" startOffset="12" endLine="97" endOffset="45"/></Target><Target id="@+id/sparkle1" view="TextView"><Expressions/><location startLine="109" startOffset="8" endLine="118" endOffset="36"/></Target><Target id="@+id/sparkle2" view="TextView"><Expressions/><location startLine="120" startOffset="8" endLine="129" endOffset="36"/></Target><Target id="@+id/rvBGImageList" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="220" startOffset="12" endLine="229" endOffset="51"/></Target><Target id="@+id/llAdView" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="273" startOffset="4" endLine="294" endOffset="39"/></Target><Target id="@+id/llAdViewFacebook" view="LinearLayout"><Expressions/><location startLine="297" startOffset="4" endLine="303" endOffset="42"/></Target></Targets></Layout>