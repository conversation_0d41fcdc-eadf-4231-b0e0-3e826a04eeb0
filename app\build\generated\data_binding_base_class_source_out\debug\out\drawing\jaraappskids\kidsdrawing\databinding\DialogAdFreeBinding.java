// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAdFreeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout btnClose;

  @NonNull
  public final LinearLayout btnWatchAd;

  @NonNull
  public final AppCompatImageView ivClose;

  @NonNull
  public final TextView tvAdFreeStatus;

  @NonNull
  public final TextView tvTimeRemaining;

  private DialogAdFreeBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout btnClose,
      @NonNull LinearLayout btnWatchAd, @NonNull AppCompatImageView ivClose,
      @NonNull TextView tvAdFreeStatus, @NonNull TextView tvTimeRemaining) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.btnWatchAd = btnWatchAd;
    this.ivClose = ivClose;
    this.tvAdFreeStatus = tvAdFreeStatus;
    this.tvTimeRemaining = tvTimeRemaining;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAdFreeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAdFreeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_ad_free, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAdFreeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClose;
      LinearLayout btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.btnWatchAd;
      LinearLayout btnWatchAd = ViewBindings.findChildViewById(rootView, id);
      if (btnWatchAd == null) {
        break missingId;
      }

      id = R.id.ivClose;
      AppCompatImageView ivClose = ViewBindings.findChildViewById(rootView, id);
      if (ivClose == null) {
        break missingId;
      }

      id = R.id.tvAdFreeStatus;
      TextView tvAdFreeStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvAdFreeStatus == null) {
        break missingId;
      }

      id = R.id.tvTimeRemaining;
      TextView tvTimeRemaining = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeRemaining == null) {
        break missingId;
      }

      return new DialogAdFreeBinding((LinearLayout) rootView, btnClose, btnWatchAd, ivClose,
          tvAdFreeStatus, tvTimeRemaining);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
