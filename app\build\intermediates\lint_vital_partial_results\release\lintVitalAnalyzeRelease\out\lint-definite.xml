<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.2" type="incidents">

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\bg_kids_canvas_border.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_kids_canvas_border.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_arrow_right.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_cloud.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cloud.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_edit_magic.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit_magic.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_edit_settings.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit_settings.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_edit_sticker.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit_sticker.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_edit_text.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit_text.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_no_ads.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_no_ads.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_play_circle.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_circle.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\ic_star_sparkle.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_star_sparkle.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\rounded_corner_button_blue.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_button_blue.xml"/>
    </incident>

    <incident
        id="LintError"
        severity="ignore"
        message="XML file is empty; not a valid document: D:\\Android\\KidsDrawingApp\\app\\src\\main\\res\\drawable\\rounded_corner_button_green.xml">
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_button_green.xml"/>
    </incident>

</incidents>
