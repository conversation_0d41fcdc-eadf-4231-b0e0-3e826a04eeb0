// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final CardView cardAppIcon;

  @NonNull
  public final AppCompatImageView imgSplash;

  @NonNull
  public final ImageView ivAppIcon;

  @NonNull
  public final View progressBar;

  @NonNull
  public final TextView tvLoadingText;

  @NonNull
  public final TextView tvLoadingTips;

  @NonNull
  public final TextView tvProgressPercent;

  @NonNull
  public final TextView tvSplashEmojis;

  @NonNull
  public final TextView tvSplashTitle1;

  @NonNull
  public final TextView tvSplashTitle2;

  private ActivitySplashBinding(@NonNull RelativeLayout rootView, @NonNull CardView cardAppIcon,
      @NonNull AppCompatImageView imgSplash, @NonNull ImageView ivAppIcon,
      @NonNull View progressBar, @NonNull TextView tvLoadingText, @NonNull TextView tvLoadingTips,
      @NonNull TextView tvProgressPercent, @NonNull TextView tvSplashEmojis,
      @NonNull TextView tvSplashTitle1, @NonNull TextView tvSplashTitle2) {
    this.rootView = rootView;
    this.cardAppIcon = cardAppIcon;
    this.imgSplash = imgSplash;
    this.ivAppIcon = ivAppIcon;
    this.progressBar = progressBar;
    this.tvLoadingText = tvLoadingText;
    this.tvLoadingTips = tvLoadingTips;
    this.tvProgressPercent = tvProgressPercent;
    this.tvSplashEmojis = tvSplashEmojis;
    this.tvSplashTitle1 = tvSplashTitle1;
    this.tvSplashTitle2 = tvSplashTitle2;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardAppIcon;
      CardView cardAppIcon = ViewBindings.findChildViewById(rootView, id);
      if (cardAppIcon == null) {
        break missingId;
      }

      id = R.id.imgSplash;
      AppCompatImageView imgSplash = ViewBindings.findChildViewById(rootView, id);
      if (imgSplash == null) {
        break missingId;
      }

      id = R.id.ivAppIcon;
      ImageView ivAppIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAppIcon == null) {
        break missingId;
      }

      id = R.id.progressBar;
      View progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tvLoadingText;
      TextView tvLoadingText = ViewBindings.findChildViewById(rootView, id);
      if (tvLoadingText == null) {
        break missingId;
      }

      id = R.id.tvLoadingTips;
      TextView tvLoadingTips = ViewBindings.findChildViewById(rootView, id);
      if (tvLoadingTips == null) {
        break missingId;
      }

      id = R.id.tvProgressPercent;
      TextView tvProgressPercent = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressPercent == null) {
        break missingId;
      }

      id = R.id.tvSplashEmojis;
      TextView tvSplashEmojis = ViewBindings.findChildViewById(rootView, id);
      if (tvSplashEmojis == null) {
        break missingId;
      }

      id = R.id.tvSplashTitle1;
      TextView tvSplashTitle1 = ViewBindings.findChildViewById(rootView, id);
      if (tvSplashTitle1 == null) {
        break missingId;
      }

      id = R.id.tvSplashTitle2;
      TextView tvSplashTitle2 = ViewBindings.findChildViewById(rootView, id);
      if (tvSplashTitle2 == null) {
        break missingId;
      }

      return new ActivitySplashBinding((RelativeLayout) rootView, cardAppIcon, imgSplash, ivAppIcon,
          progressBar, tvLoadingText, tvLoadingTips, tvProgressPercent, tvSplashEmojis,
          tvSplashTitle1, tvSplashTitle2);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
