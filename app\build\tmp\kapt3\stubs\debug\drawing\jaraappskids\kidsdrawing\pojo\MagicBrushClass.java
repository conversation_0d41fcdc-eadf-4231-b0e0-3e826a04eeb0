package drawing.jaraappskids.kidsdrawing.pojo;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\b\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0003\u0010\u0005\"\u0004\b\u0006\u0010\u0007R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\u000e\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u000b\"\u0004\b\u0010\u0010\r\u00a8\u0006\u0011"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/pojo/MagicBrushClass;", "", "()V", "isSelected", "", "()Z", "setSelected", "(Z)V", "mbImageName", "", "getMbImageName", "()Ljava/lang/String;", "setMbImageName", "(Ljava/lang/String;)V", "mbImagePath", "getMbImagePath", "setMbImagePath", "app_debug"})
public final class MagicBrushClass {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String mbImageName = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String mbImagePath = "";
    private boolean isSelected = false;
    
    public MagicBrushClass() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMbImageName() {
        return null;
    }
    
    public final void setMbImageName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMbImagePath() {
        return null;
    }
    
    public final void setMbImagePath(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
}