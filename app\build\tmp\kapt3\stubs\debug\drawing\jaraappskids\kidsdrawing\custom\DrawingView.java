package drawing.jaraappskids.kidsdrawing.custom;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 ;2\u00020\u0001:\u0001;B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020\"J\u0010\u0010$\u001a\u00020\"2\u0006\u0010%\u001a\u00020\u0011H\u0002J\u0010\u0010&\u001a\u00020\"2\u0006\u0010%\u001a\u00020\u0011H\u0002J\b\u0010\'\u001a\u00020\"H\u0002J\b\u0010(\u001a\u00020\"H\u0002J\b\u0010)\u001a\u00020\"H\u0014J\u0010\u0010*\u001a\u00020\"2\u0006\u0010%\u001a\u00020\u0011H\u0014J(\u0010+\u001a\u00020\"2\u0006\u0010,\u001a\u00020\f2\u0006\u0010-\u001a\u00020\f2\u0006\u0010.\u001a\u00020\f2\u0006\u0010/\u001a\u00020\fH\u0014J\u0010\u00100\u001a\u0002012\u0006\u00102\u001a\u000203H\u0016J\u0006\u00104\u001a\u00020\"J\u0010\u00105\u001a\u00020\"2\u0006\u00106\u001a\u00020\fH\u0016J\u000e\u00107\u001a\u00020\"2\u0006\u00106\u001a\u00020\fJ\u000e\u00108\u001a\u00020\"2\u0006\u00109\u001a\u00020\fJ\u0006\u0010:\u001a\u00020\"R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b8F\u00a2\u0006\u0006\u001a\u0004\b\t\u0010\nR\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00140\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001b\u001a\u00020\u001cX\u0080\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001e\"\u0004\b\u001f\u0010 \u00a8\u0006<"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/custom/DrawingView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "(Landroid/content/Context;Landroid/util/AttributeSet;)V", "bitmap", "Landroid/graphics/Bitmap;", "getBitmap", "()Landroid/graphics/Bitmap;", "mBackgroundColor", "", "mBackgroundPaint", "Landroid/graphics/Paint;", "mCanvasBitmap", "mDrawCanvas", "Landroid/graphics/Canvas;", "mDrawPaint", "mDrawPath", "Landroid/graphics/Path;", "mPaints", "Ljava/util/ArrayList;", "mPaths", "mStrokeWidth", "mUndonePaints", "mUndonePaths", "rectangle", "Landroid/graphics/Rect;", "getRectangle$app_debug", "()Landroid/graphics/Rect;", "setRectangle$app_debug", "(Landroid/graphics/Rect;)V", "cleanup", "", "clearCanvas", "drawBackground", "canvas", "drawPaths", "init", "initPaint", "onDetachedFromWindow", "onDraw", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "", "event", "Landroid/view/MotionEvent;", "redo", "setBackgroundColor", "color", "setPaintColor", "setPaintStrokeWidth", "strokeWidth", "undo", "Companion", "app_debug"})
public final class DrawingView extends android.view.View {
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Path mDrawPath;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Paint mBackgroundPaint;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Paint mDrawPaint;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Canvas mDrawCanvas;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap mCanvasBitmap;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<android.graphics.Path> mPaths = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<android.graphics.Paint> mPaints = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<android.graphics.Path> mUndonePaths = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<android.graphics.Paint> mUndonePaints = null;
    private int mBackgroundColor = android.graphics.Color.TRANSPARENT;
    private int mStrokeWidth = 10;
    @org.jetbrains.annotations.NotNull()
    private android.graphics.Rect rectangle;
    private static int mPaintColor = android.graphics.Color.TRANSPARENT;
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.custom.DrawingView.Companion Companion = null;
    
    public DrawingView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Rect getRectangle$app_debug() {
        return null;
    }
    
    public final void setRectangle$app_debug(@org.jetbrains.annotations.NotNull()
    android.graphics.Rect p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap getBitmap() {
        return null;
    }
    
    private final void init() {
    }
    
    private final void initPaint() {
    }
    
    private final void drawBackground(android.graphics.Canvas canvas) {
    }
    
    private final void drawPaths(android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    public final void clearCanvas() {
    }
    
    public final void setPaintColor(int color) {
    }
    
    public final void setPaintStrokeWidth(int strokeWidth) {
    }
    
    @java.lang.Override()
    public void setBackgroundColor(int color) {
    }
    
    public final void undo() {
    }
    
    public final void redo() {
    }
    
    /**
     * Clean up resources to prevent memory leaks
     */
    public final void cleanup() {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\b\u00a8\u0006\t"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/custom/DrawingView$Companion;", "", "()V", "mPaintColor", "", "getMPaintColor", "()I", "setMPaintColor", "(I)V", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final int getMPaintColor() {
            return 0;
        }
        
        public final void setMPaintColor(int p0) {
        }
    }
}