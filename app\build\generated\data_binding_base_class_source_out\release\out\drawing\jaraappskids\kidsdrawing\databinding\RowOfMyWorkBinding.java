// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import drawing.jaraappskids.kidsdrawing.custom.SquareImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class RowOfMyWorkBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final SquareImageView cellImgMyWork;

  @NonNull
  public final TextView cellTxtDate;

  private RowOfMyWorkBinding(@NonNull RelativeLayout rootView,
      @NonNull SquareImageView cellImgMyWork, @NonNull TextView cellTxtDate) {
    this.rootView = rootView;
    this.cellImgMyWork = cellImgMyWork;
    this.cellTxtDate = cellTxtDate;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static RowOfMyWorkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RowOfMyWorkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.row_of_my_work, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RowOfMyWorkBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cellImgMyWork;
      SquareImageView cellImgMyWork = ViewBindings.findChildViewById(rootView, id);
      if (cellImgMyWork == null) {
        break missingId;
      }

      id = R.id.cellTxtDate;
      TextView cellTxtDate = ViewBindings.findChildViewById(rootView, id);
      if (cellTxtDate == null) {
        break missingId;
      }

      return new RowOfMyWorkBinding((RelativeLayout) rootView, cellImgMyWork, cellTxtDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
