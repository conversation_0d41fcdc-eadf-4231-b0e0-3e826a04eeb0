<lint-module
    format="1"
    dir="D:\Android\KidsDrawingApp\app"
    name=":app"
    type="APP"
    maven="KidsDrawingApp:app:unspecified"
    agpVersion="8.5.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      baselineFile="lint-baseline.xml"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
