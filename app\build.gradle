apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
apply plugin: 'dagger.hilt.android.plugin'
//apply plugin: 'kotlin-android-extensions'
//apply plugin: 'com.google.gms.google-services'


android {
    compileSdk 34
    defaultConfig {
        applicationId "drawing.jaraappskids.kidsdrawing"
        minSdkVersion 23
        //noinspection EditedTargetSdkVersion
        targetSdkVersion 34
        versionCode 1
        versionName "1.4"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }

    // Test configuration
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        animationsDisabled = true
    }

    lint {
        baseline = file("lint-baseline.xml")
    }
    namespace 'drawing.jaraappskids.kidsdrawing'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation"org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    implementation 'androidx.browser:browser:1.8.0'
    implementation 'androidx.media:media:1.7.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'

   // implementation 'com.github.warkiz:IndicatorSeekBar:v2.1.0'
    implementation project(":indicatorseekbar")

    //Image Processing
    implementation 'com.github.bumptech.glide:glide:4.14.2'


//    implementation 'com.flying.xiaopo:sticker:1.6.0'
    implementation 'com.github.yukuku:ambilwarna:2.0.1'
    implementation 'com.intuit.sdp:sdp-android:1.1.0'
    implementation 'com.github.QuadFlask:colorpicker:0.0.13'
    implementation 'com.google.ads.interactivemedia.v3:interactivemedia:3.36.0'

    // Modern Drawing Editor Dependencies

    // Architecture Components
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'

    // Dependency Injection - Hilt
    implementation 'com.google.dagger:hilt-android:2.48'
    kapt 'com.google.dagger:hilt-compiler:2.48'

    // Material Design 3
    implementation 'com.google.android.material:material:1.11.0'

    // Color Picker
    implementation 'com.github.skydoves:colorpicker-compose:1.0.5'
    implementation 'com.github.QuadFlask:colorpicker:0.0.15'

    // Image Processing
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    // File Operations
    implementation 'androidx.documentfile:documentfile:1.0.1'

    // Modern Testing Framework Dependencies
    // JUnit 5 for modern unit testing
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-params:5.9.2'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine:5.9.2'

    // JUnit 4 for compatibility
    testImplementation 'junit:junit:4.13.2'

    // AndroidX Test framework
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test:runner:1.6.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.test:core:1.5.0'

    // Espresso for UI testing
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.6.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.6.1'
    androidTestImplementation 'androidx.test.espresso:espresso-accessibility:3.6.1'

    // Mockito for mocking
    testImplementation 'org.mockito:mockito-core:5.1.1'
    testImplementation 'org.mockito:mockito-inline:5.1.1'
    androidTestImplementation 'org.mockito:mockito-android:5.1.1'

    // Robolectric for unit tests that need Android context
    testImplementation 'org.robolectric:robolectric:4.9.2'

    // Truth for better assertions
    testImplementation 'com.google.truth:truth:1.1.3'
    androidTestImplementation 'com.google.truth:truth:1.1.3'

    // Memory leak detection
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.10'

    // Performance testing
    androidTestImplementation 'androidx.benchmark:benchmark-junit4:1.1.1'

    // Coroutines testing
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4'
    androidTestImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4'

    // Updated Ads Dependencies - Google AdMob Only (Firebase Analytics removed to prevent crashes)
    // implementation 'com.google.firebase:firebase-analytics:22.4.0'  // Disabled - causes crashes without proper firebase config
    // implementation 'com.google.firebase:firebase-messaging:24.1.1'  // Disabled - pulls in Analytics connector components
    implementation 'com.google.android.gms:play-services-ads:24.3.0'
    
    // Ads Mediation & Advanced Features
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    // implementation 'com.google.firebase:firebase-config:22.1.0'  // Disabled - pulls in Analytics connector components
    implementation 'androidx.lifecycle:lifecycle-process:2.8.7'
    implementation 'androidx.work:work-runtime-ktx:2.9.1'

    // Additional utility for ads
    implementation 'androidx.preference:preference-ktx:1.2.1'
}