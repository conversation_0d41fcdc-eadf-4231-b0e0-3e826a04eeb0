<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_modern_editor" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\activity_modern_editor.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_modern_editor_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="348" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="62"/></Target><Target id="@+id/drawingView" view="drawing.jaraappskids.kidsdrawing.editor.view.ModernDrawingView"><Expressions/><location startLine="22" startOffset="4" endLine="31" endOffset="60"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="34" startOffset="4" endLine="42" endOffset="61"/></Target><Target id="@+id/bottomToolbar" view="LinearLayout"><Expressions/><location startLine="45" startOffset="4" endLine="123" endOffset="18"/></Target><Target id="@+id/btnBrush" view="Button"><Expressions/><location startLine="57" startOffset="8" endLine="66" endOffset="37"/></Target><Target id="@+id/btnEraser" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="37"/></Target><Target id="@+id/btnUndo" view="Button"><Expressions/><location startLine="79" startOffset="8" endLine="88" endOffset="37"/></Target><Target id="@+id/btnRedo" view="Button"><Expressions/><location startLine="90" startOffset="8" endLine="99" endOffset="37"/></Target><Target id="@+id/btnClear" view="Button"><Expressions/><location startLine="101" startOffset="8" endLine="110" endOffset="37"/></Target><Target id="@+id/btnSave" view="Button"><Expressions/><location startLine="112" startOffset="8" endLine="121" endOffset="37"/></Target><Target id="@+id/colorPickerPanel" view="LinearLayout"><Expressions/><location startLine="126" startOffset="4" endLine="180" endOffset="18"/></Target><Target id="@+id/colorBlack" view="View"><Expressions/><location startLine="138" startOffset="8" endLine="143" endOffset="55"/></Target><Target id="@+id/colorRed" view="View"><Expressions/><location startLine="145" startOffset="8" endLine="150" endOffset="63"/></Target><Target id="@+id/colorGreen" view="View"><Expressions/><location startLine="152" startOffset="8" endLine="157" endOffset="65"/></Target><Target id="@+id/colorBlue" view="View"><Expressions/><location startLine="159" startOffset="8" endLine="164" endOffset="64"/></Target><Target id="@+id/colorYellow" view="View"><Expressions/><location startLine="166" startOffset="8" endLine="171" endOffset="66"/></Target><Target id="@+id/colorPurple" view="View"><Expressions/><location startLine="173" startOffset="8" endLine="178" endOffset="61"/></Target><Target id="@+id/brushSettingsPanel" view="LinearLayout"><Expressions/><location startLine="183" startOffset="4" endLine="222" endOffset="18"/></Target><Target id="@+id/brushSizeSlider" view="SeekBar"><Expressions/><location startLine="203" startOffset="8" endLine="210" endOffset="35"/></Target><Target id="@+id/btnBrushSettings" view="Button"><Expressions/><location startLine="212" startOffset="8" endLine="220" endOffset="37"/></Target><Target id="@+id/layerPanel" view="View"><Expressions/><location startLine="225" startOffset="4" endLine="229" endOffset="35"/></Target><Target id="@+id/selectedColorIndicator" view="View"><Expressions/><location startLine="231" startOffset="4" endLine="235" endOffset="35"/></Target><Target id="@+id/btnShapeRect" view="View"><Expressions/><location startLine="238" startOffset="4" endLine="242" endOffset="35"/></Target><Target id="@+id/btnShapeCircle" view="View"><Expressions/><location startLine="244" startOffset="4" endLine="248" endOffset="35"/></Target><Target id="@+id/btnText" view="View"><Expressions/><location startLine="250" startOffset="4" endLine="254" endOffset="35"/></Target><Target id="@+id/btnSticker" view="View"><Expressions/><location startLine="256" startOffset="4" endLine="260" endOffset="35"/></Target><Target id="@+id/btnColorPicker" view="View"><Expressions/><location startLine="262" startOffset="4" endLine="266" endOffset="35"/></Target><Target id="@+id/btnLayers" view="View"><Expressions/><location startLine="268" startOffset="4" endLine="272" endOffset="35"/></Target><Target id="@+id/btnPencil" view="View"><Expressions/><location startLine="275" startOffset="4" endLine="279" endOffset="35"/></Target><Target id="@+id/btnMarker" view="View"><Expressions/><location startLine="281" startOffset="4" endLine="285" endOffset="35"/></Target><Target id="@+id/btnAirbrush" view="View"><Expressions/><location startLine="287" startOffset="4" endLine="291" endOffset="35"/></Target><Target id="@+id/btnHighlighter" view="View"><Expressions/><location startLine="293" startOffset="4" endLine="297" endOffset="35"/></Target><Target id="@+id/colorWhite" view="View"><Expressions/><location startLine="300" startOffset="4" endLine="304" endOffset="35"/></Target><Target id="@+id/colorMagenta" view="View"><Expressions/><location startLine="306" startOffset="4" endLine="310" endOffset="35"/></Target><Target id="@+id/colorCyan" view="View"><Expressions/><location startLine="312" startOffset="4" endLine="316" endOffset="35"/></Target><Target id="@+id/colorGray" view="View"><Expressions/><location startLine="318" startOffset="4" endLine="322" endOffset="35"/></Target><Target id="@+id/colorDarkGray" view="View"><Expressions/><location startLine="324" startOffset="4" endLine="328" endOffset="35"/></Target><Target id="@+id/colorLightGray" view="View"><Expressions/><location startLine="330" startOffset="4" endLine="334" endOffset="35"/></Target><Target id="@+id/colorOrange" view="View"><Expressions/><location startLine="336" startOffset="4" endLine="340" endOffset="35"/></Target><Target id="@+id/brushOpacitySlider" view="View"><Expressions/><location startLine="342" startOffset="4" endLine="346" endOffset="35"/></Target></Targets></Layout>