<libraries>
  <library
      name="androidx.appcompat:appcompat:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d9edc78acc62fdda062f64d98c52a3de\transformed\appcompat-1.0.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d9edc78acc62fdda062f64d98c52a3de\transformed\appcompat-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\178a8e1bc1425007643cd0615d2f59be\transformed\fragment-1.0.0\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\178a8e1bc1425007643cd0615d2f59be\transformed\fragment-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\445a4188605654ca03b8a7e845bbf5ab\transformed\vectordrawable-animated-1.0.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\445a4188605654ca03b8a7e845bbf5ab\transformed\vectordrawable-animated-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5b1b095d795cdeffeb260251d6a4009e\transformed\vectordrawable-1.0.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5b1b095d795cdeffeb260251d6a4009e\transformed\vectordrawable-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0df2ae6a59b116fd30cbf642318f421b\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0df2ae6a59b116fd30cbf642318f421b\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a5de7656a602f5842203634b4131a591\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a5de7656a602f5842203634b4131a591\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b2e19079b92aeecf0f8d515331419f29\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b2e19079b92aeecf0f8d515331419f29\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f6aa290ccc3bc10b107df2e10e808ac6\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f6aa290ccc3bc10b107df2e10e808ac6\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\df647b992918a755d1c67a62d0a08319\transformed\core-1.0.0\jars\classes.jar"
      resolved="androidx.core:core:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\df647b992918a755d1c67a62d0a08319\transformed\core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\675e59845699296be58c4e1d08126b75\transformed\versionedparcelable-1.0.0\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\675e59845699296be58c4e1d08126b75\transformed\versionedparcelable-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.0.0\42858b26cafdaa69b6149f45dfc2894007bc2c7a\collection-1.0.0.jar"
      resolved="androidx.collection:collection:1.0.0"
      provided="true"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f914fdeef4fd3753e3ab965b1875df91\transformed\lifecycle-runtime-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f914fdeef4fd3753e3ab965b1875df91\transformed\lifecycle-runtime-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\dcd676eefd9d398e35d56dc3fb8dfcf9\transformed\lifecycle-viewmodel-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\dcd676eefd9d398e35d56dc3fb8dfcf9\transformed\lifecycle-viewmodel-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ccce0a91310c1babf901dc4936d73a85\transformed\lifecycle-livedata-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ccce0a91310c1babf901dc4936d73a85\transformed\lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\34fcf37810fe2e45c3a5219e766695eb\transformed\lifecycle-livedata-core-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\34fcf37810fe2e45c3a5219e766695eb\transformed\lifecycle-livedata-core-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.0.0\e070ffae07452331bc5684734fce6831d531785c\lifecycle-common-2.0.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.0.0"
      provided="true"/>
  <library
      name="androidx.arch.core:core-runtime:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\07b378793d7fb4c15d26dd034113ed9c\transformed\core-runtime-2.0.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\07b378793d7fb4c15d26dd034113ed9c\transformed\core-runtime-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.0.0\bb21b9a11761451b51624ac428d1f1bb5deeac38\core-common-2.0.0.jar"
      resolved="androidx.arch.core:core-common:2.0.0"
      provided="true"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.0.0\45599f2cd5965ac05a1488fa2a5c0cdd7c499ead\annotation-1.0.0.jar"
      resolved="androidx.annotation:annotation:1.0.0"
      provided="true"/>
</libraries>
