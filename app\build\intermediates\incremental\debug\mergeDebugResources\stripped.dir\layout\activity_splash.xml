<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/kids_bg_light_blue">

    <!-- Floating Background Elements temporarily disabled for build 
    <ImageView
        android:id="@+id/ivSplashCloud1"
        android:layout_width="60dp"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:layout_marginTop="80dp"
        android:layout_marginStart="40dp"
        android:src="@drawable/ic_celebration"
        android:alpha="0.6" />

    <ImageView
        android:id="@+id/ivSplashCloud2"
        android:layout_width="80dp"
        android:layout_height="50dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="120dp"
        android:layout_marginEnd="30dp"
        android:src="@drawable/ic_celebration"
        android:alpha="0.7" />

    <ImageView
        android:id="@+id/ivSplashStar1"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="150dp"
        android:layout_marginStart="100dp"
        android:src="@drawable/ic_star_fun"
        android:alpha="0.8" />

    <ImageView
        android:id="@+id/ivSplashStar2"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginTop="200dp"
        android:layout_marginEnd="80dp"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_star_fun"
        android:alpha="0.9" />

    <ImageView
        android:id="@+id/ivSplashStar3"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="100dp"
        android:src="@drawable/ic_star_fun"
        android:alpha="0.7" />
    -->

    <!-- Main Content Container with extra padding for animations -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="40dp"
        android:paddingStart="30dp"
        android:paddingEnd="30dp"
        android:paddingTop="50dp"
        android:paddingBottom="40dp"
        android:clipChildren="false"
        android:clipToPadding="false">

        <!-- App Logo Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="50dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:clipChildren="false"
            android:clipToPadding="false">

            <!-- App Icon with Animation -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardAppIcon"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="60dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/white">

                <ImageView
                    android:id="@+id/ivAppIcon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center"
                    android:src="@mipmap/ic_launcher"
                    android:scaleType="centerCrop" />

            </androidx.cardview.widget.CardView>

            <!-- App Title -->
            <TextView
                android:id="@+id/tvSplashTitle1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="DRAWING FOR"
                android:textSize="30sp"
                android:textColor="#4ECDC4"
                android:textStyle="bold"
                android:letterSpacing="0.1"
                android:elevation="4dp"
                android:gravity="center"
                android:layout_marginBottom="5dp" />

            <TextView
                android:id="@+id/tvSplashTitle2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="KIDS!"
                android:textSize="36sp"
                android:textColor="#FF6B9D"
                android:textStyle="bold"
                android:letterSpacing="0.15"
                android:elevation="4dp"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <!-- Fun Emoji Decoration -->
            <TextView
                android:id="@+id/tvSplashEmojis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎨 ✨ 🌈"
                android:textSize="26sp"
                android:layout_marginTop="10dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Progress Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="40dp">

            <!-- Loading Text -->
            <TextView
                android:id="@+id/tvLoadingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🚀 Getting Ready..."
                android:textSize="20sp"
                android:textColor="#666666"
                android:textStyle="bold"
                android:layout_marginBottom="20dp" />

            <!-- Custom Progress Bar Container -->
            <androidx.cardview.widget.CardView
                android:layout_width="300dp"
                android:layout_height="16dp"
                android:layout_marginBottom="15dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="#E0E0E0">

                <!-- Animated Progress Bar -->
                <View
                    android:id="@+id/progressBar"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:background="@color/kids_primary_orange" />

            </androidx.cardview.widget.CardView>

            <!-- Progress Percentage -->
            <TextView
                android:id="@+id/tvProgressPercent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0%"
                android:textSize="18sp"
                android:textColor="#4ECDC4"
                android:textStyle="bold" />

            <!-- Fun Loading Tips -->
            <TextView
                android:id="@+id/tvLoadingTips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="💡 Tip: Use different colors to make your drawings magical!"
                android:textSize="14sp"
                android:textColor="#888888"
                android:textAlignment="center"
                android:layout_marginTop="20dp"
                android:paddingHorizontal="20dp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- Hidden original splash image for fallback -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgSplash"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="#FFD1DC"
        android:visibility="gone" />

</RelativeLayout>