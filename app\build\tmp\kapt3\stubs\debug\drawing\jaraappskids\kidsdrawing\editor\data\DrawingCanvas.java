package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\bJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\nH\u00c6\u0003JC\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010#H\u00d6\u0003J\b\u0010$\u001a\u0004\u0018\u00010\bJ\t\u0010%\u001a\u00020\u0003H\u00d6\u0001J\u000e\u0010&\u001a\u00020\u00172\u0006\u0010\'\u001a\u00020\nJ\t\u0010(\u001a\u00020\nH\u00d6\u0001J\u0019\u0010)\u001a\u00020\u00172\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020\u0003H\u00d6\u0001R\u001c\u0010\t\u001a\u0004\u0018\u00010\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011\u00a8\u0006-"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingCanvas;", "Landroid/os/Parcelable;", "width", "", "height", "backgroundColor", "layers", "", "Ldrawing/jaraappskids/kidsdrawing/editor/data/DrawingLayer;", "activeLayerId", "", "(IIILjava/util/List;Ljava/lang/String;)V", "getActiveLayerId", "()Ljava/lang/String;", "setActiveLayerId", "(Ljava/lang/String;)V", "getBackgroundColor", "()I", "getHeight", "getLayers", "()Ljava/util/List;", "getWidth", "addLayer", "", "layer", "component1", "component2", "component3", "component4", "component5", "copy", "describeContents", "equals", "", "other", "", "getActiveLayer", "hashCode", "removeLayer", "layerId", "toString", "writeToParcel", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class DrawingCanvas implements android.os.Parcelable {
    private final int width = 0;
    private final int height = 0;
    private final int backgroundColor = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer> layers = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String activeLayerId;
    
    public DrawingCanvas(int width, int height, int backgroundColor, @org.jetbrains.annotations.NotNull()
    java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer> layers, @org.jetbrains.annotations.Nullable()
    java.lang.String activeLayerId) {
        super();
    }
    
    public final int getWidth() {
        return 0;
    }
    
    public final int getHeight() {
        return 0;
    }
    
    public final int getBackgroundColor() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer> getLayers() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getActiveLayerId() {
        return null;
    }
    
    public final void setActiveLayerId(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer getActiveLayer() {
        return null;
    }
    
    public final void addLayer(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer layer) {
    }
    
    public final void removeLayer(@org.jetbrains.annotations.NotNull()
    java.lang.String layerId) {
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.DrawingCanvas copy(int width, int height, int backgroundColor, @org.jetbrains.annotations.NotNull()
    java.util.List<drawing.jaraappskids.kidsdrawing.editor.data.DrawingLayer> layers, @org.jetbrains.annotations.Nullable()
    java.lang.String activeLayerId) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}