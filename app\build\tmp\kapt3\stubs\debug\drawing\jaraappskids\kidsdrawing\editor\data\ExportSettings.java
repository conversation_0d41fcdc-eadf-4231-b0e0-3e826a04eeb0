package drawing.jaraappskids.kidsdrawing.editor.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0012\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00072\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\r\u00a8\u0006\u001b"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportSettings;", "", "format", "Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;", "quality", "", "includeBackground", "", "transparentBackground", "(Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;IZZ)V", "getFormat", "()Ldrawing/jaraappskids/kidsdrawing/editor/data/ExportFormat;", "getIncludeBackground", "()Z", "getQuality", "()I", "getTransparentBackground", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
public final class ExportSettings {
    @org.jetbrains.annotations.NotNull()
    private final drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat format = null;
    private final int quality = 0;
    private final boolean includeBackground = false;
    private final boolean transparentBackground = false;
    
    public ExportSettings(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat format, int quality, boolean includeBackground, boolean transparentBackground) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat getFormat() {
        return null;
    }
    
    public final int getQuality() {
        return 0;
    }
    
    public final boolean getIncludeBackground() {
        return false;
    }
    
    public final boolean getTransparentBackground() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.editor.data.ExportSettings copy(@org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.editor.data.ExportFormat format, int quality, boolean includeBackground, boolean transparentBackground) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}