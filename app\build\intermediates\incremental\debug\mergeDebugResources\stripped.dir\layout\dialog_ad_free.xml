<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- Close Button -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentEnd="true"
            android:padding="5dp"
            android:src="@android:drawable/ic_delete"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

    </RelativeLayout>

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/label_ad_free_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#FF6B35"
        android:gravity="center"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp" />

    <!-- Current Status Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/colorVeryLightBlue"
        android:padding="15dp"
        android:layout_marginBottom="20dp">

        <!-- Ad-Free Status -->
        <TextView
            android:id="@+id/tvAdFreeStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/label_ads_watched"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- Time Remaining (shown only when ad-free is active) -->
        <TextView
            android:id="@+id/tvTimeRemaining"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/label_time_remaining"
            android:textSize="14sp"
            android:textColor="#666666"
            android:gravity="center"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/label_ad_free_description"
        android:textSize="16sp"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="25dp"
        android:lineSpacingExtra="4dp" />

    <!-- Watch Ad Button -->
    <LinearLayout
        android:id="@+id/btnWatchAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:drawable/btn_default"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="15dp"
        android:layout_marginBottom="10dp"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_watch_ad"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:drawableLeft="@android:drawable/ic_media_play"
            android:drawableTint="#FFFFFF"
            android:drawablePadding="10dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Close Button -->
    <LinearLayout
        android:id="@+id/btnClose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorTheme"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="15dp"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_close"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
