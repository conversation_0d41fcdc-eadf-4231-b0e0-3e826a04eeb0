package drawing.jaraappskids.kidsdrawing.base

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import org.robolectric.RuntimeEnvironment
import drawing.jaraappskids.kidsdrawing.drwaing.WTDrawingView
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.After
import org.junit.Before
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.mockito.MockitoAnnotations

/**
 * Base class for all drawing-related tests
 * Provides common setup, teardown, and utility methods
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
abstract class BaseDrawingTest {

    protected lateinit var context: Context
    protected lateinit var drawingView: WTDrawingView
    protected var testBitmaps = mutableListOf<Bitmap>()
    protected var initialMemory: TestUtils.MemoryInfo? = null

    @Before
    open fun setUp() {
        MockitoAnnotations.openMocks(this)
        context = RuntimeEnvironment.getApplication()
        
        // Record initial memory state
        initialMemory = TestUtils.measureMemoryUsage()
        
        // Initialize drawing view with test dimensions
        drawingView = WTDrawingView(context, null).apply {
            // Set test dimensions
            layout(0, 0, 200, 200)
        }
        
        // Clear any existing test data
        testBitmaps.clear()
    }

    @After
    open fun tearDown() {
        // Clean up test bitmaps to prevent memory leaks
        testBitmaps.forEach { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
        testBitmaps.clear()
        
        // Clean up drawing view
        drawingView.cleanup()
        
        // Check for memory leaks
        checkMemoryLeaks()
    }

    /**
     * Creates a test bitmap and adds it to cleanup list
     */
    protected fun createTestBitmap(
        width: Int = 100,
        height: Int = 100,
        color: Int = Color.WHITE
    ): Bitmap {
        val bitmap = TestUtils.createTestBitmap(width, height, color)
        testBitmaps.add(bitmap)
        return bitmap
    }

    /**
     * Simulates a drawing action on the test view
     */
    protected fun simulateDrawing(
        startX: Float = 10f,
        startY: Float = 10f,
        endX: Float = 50f,
        endY: Float = 50f,
        steps: Int = 10
    ) {
        val events = TestUtils.simulateDrawingStroke(drawingView, startX, startY, endX, endY, steps)
        events.forEach { event ->
            drawingView.onTouchEvent(event)
        }
    }

    /**
     * Sets up drawing view with specific configuration
     */
    protected fun setupDrawingView(
        strokeColor: Int = Color.BLACK,
        strokeWidth: Float = 10f,
        isEraserMode: Boolean = false
    ) {
        drawingView.setStrokeColor(strokeColor)
        drawingView.strokeWidth = strokeWidth
        drawingView.setEraserMode(isEraserMode)
    }

    /**
     * Validates the current state of the drawing view
     */
    protected fun validateDrawingViewState(
        expectedColor: Int,
        expectedWidth: Float,
        expectedEraserMode: Boolean
    ) {
        assert(drawingView.getStrokeColor() == expectedColor) {
            "Expected stroke color $expectedColor, got ${drawingView.getStrokeColor()}"
        }
        assert(drawingView.strokeWidth == expectedWidth) {
            "Expected stroke width $expectedWidth, got ${drawingView.strokeWidth}"
        }
        assert(drawingView.isDrawingMode() == expectedEraserMode) {
            "Expected eraser mode $expectedEraserMode, got ${drawingView.isDrawingMode()}"
        }
    }

    /**
     * Checks for memory leaks after test execution
     */
    private fun checkMemoryLeaks() {
        val currentMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = currentMemory.usedMemory - (initialMemory?.usedMemory ?: 0)
        
        // Allow for some memory increase but flag significant leaks
        val maxAllowedIncrease = 50 * 1024 * 1024 // 50MB
        
        if (memoryIncrease > maxAllowedIncrease) {
            println("WARNING: Potential memory leak detected. Memory increased by ${memoryIncrease / (1024 * 1024)}MB")
        }
    }

    /**
     * Waits for drawing operations to complete
     */
    protected fun waitForDrawingCompletion() {
        TestUtils.waitForUIThread(500)
    }

    /**
     * Creates a test scenario with multiple drawing operations
     */
    protected fun createComplexDrawingScenario() {
        // Draw with different colors and sizes
        setupDrawingView(Color.RED, 5f, false)
        simulateDrawing(10f, 10f, 30f, 30f)
        
        setupDrawingView(Color.BLUE, 15f, false)
        simulateDrawing(40f, 40f, 80f, 80f)
        
        setupDrawingView(Color.GREEN, 10f, false)
        simulateDrawing(20f, 60f, 60f, 20f)
        
        waitForDrawingCompletion()
    }

    /**
     * Stress test with rapid tool switching
     */
    protected fun performRapidToolSwitching(iterations: Int = 10) {
        val colors = TestUtils.getTestColorPalette()
        val sizes = TestUtils.getTestBrushSizes()
        
        repeat(iterations) { i ->
            val color = colors[i % colors.size]
            val size = sizes[i % sizes.size]
            val isEraser = i % 3 == 0
            
            setupDrawingView(color, size, isEraser)
            simulateDrawing(
                (i * 10f) % 100f,
                (i * 15f) % 100f,
                ((i + 1) * 10f) % 100f,
                ((i + 1) * 15f) % 100f
            )
        }
        
        waitForDrawingCompletion()
    }

    /**
     * Performance benchmark helper
     */
    protected fun measurePerformance(operation: () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        operation()
        return System.currentTimeMillis() - startTime
    }

    /**
     * Validates that no exceptions are thrown during operation
     */
    protected fun assertNoExceptions(operation: () -> Unit) {
        try {
            operation()
        } catch (e: Exception) {
            throw AssertionError("Unexpected exception: ${e.message}", e)
        }
    }
}
