package drawing.jaraappskids.kidsdrawing.magicbrush;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J8\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\rH\u0002J\u000e\u0010\u0014\u001a\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0003J\u000e\u0010\u0014\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0000X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/magicbrush/LoadBrush;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "loadBrush", "loadbrushlist", "Ljava/util/ArrayList;", "Ldrawing/jaraappskids/kidsdrawing/magicbrush/BrushEffectLoad;", "BrushLoad", "path", "", "row", "", "column", "rotate", "", "unknown", "", "alpha", "loadBrushInstance", "i", "app_debug"})
public final class LoadBrush {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private drawing.jaraappskids.kidsdrawing.magicbrush.LoadBrush loadBrush;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad> loadbrushlist = null;
    
    public LoadBrush(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.magicbrush.LoadBrush loadBrushInstance(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    private final drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad BrushLoad(java.lang.String path, int row, int column, boolean rotate, float unknown, int alpha) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final drawing.jaraappskids.kidsdrawing.magicbrush.BrushEffectLoad loadBrushInstance(int i) {
        return null;
    }
}