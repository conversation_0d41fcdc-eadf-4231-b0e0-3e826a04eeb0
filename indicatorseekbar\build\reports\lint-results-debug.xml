<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.5.2">

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdkVersion 34"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Android\KidsDrawingApp\indicatorseekbar\build.gradle"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.support:appcompat-v7 than 27.1.1 is available: 28.0.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    compileOnly &apos;com.android.support:appcompat-v7:27.1.1&apos;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Android\KidsDrawingApp\indicatorseekbar\build.gradle"
            line="48"
            column="17"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="                int progressTextViewId = mContext.getResources().getIdentifier(&quot;isb_progress&quot;, &quot;id&quot;, mContext.getApplicationContext().getPackageName());"
        errorLine2="                                                                 ~~~~~~~~~~~~~">
        <location
            file="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\java\drawing\warkiz\widget\Indicator.java"
            line="77"
            column="66"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="                    int progressTextViewId = mContext.getResources().getIdentifier(&quot;isb_progress&quot;, &quot;id&quot;, mContext.getApplicationContext().getPackageName());"
        errorLine2="                                                                     ~~~~~~~~~~~~~">
        <location
            file="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\java\drawing\warkiz\widget\Indicator.java"
            line="120"
            column="70"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Indicator&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Indicator&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Android\KidsDrawingApp\indicatorseekbar\src\main\res\layout\isb_indicator.xml"
            line="24"
            column="13"/>
    </issue>

</issues>
