<variant
    name="debug"
    package="com.warkiz.widget"
    minSdkVersion="14"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.5.2"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\ab7e709698b5a2baa7679309adbf25bf\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\intermediates\compile_r_class_jar\debug\generateDebugRFile\R.jar"
      type="MAIN"
      applicationId="com.warkiz.widget"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out"
      generatedResourceFolders="build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\ab7e709698b5a2baa7679309adbf25bf\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
