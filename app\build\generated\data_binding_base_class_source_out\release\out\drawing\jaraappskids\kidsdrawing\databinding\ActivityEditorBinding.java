// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import drawing.jaraappskids.kidsdrawing.R;
import drawing.jaraappskids.kidsdrawing.drwaing.WTDrawingView;
import drawing.jaraappskids.kidsdrawing.magicbrush.OverlayBrushView;
import drawing.jaraappskids.kidsdrawing.sticker.StickerView;
import drawing.warkiz.widget.IndicatorSeekBar;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEditorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final WTDrawingView drawingView;

  @NonNull
  public final OverlayBrushView drawingViewBitmap1;

  @NonNull
  public final RelativeLayout flMainLayout;

  @NonNull
  public final ImageView ivBackBrush;

  @NonNull
  public final ImageView ivBackMagicBrush;

  @NonNull
  public final ImageView ivBackPencil;

  @NonNull
  public final ImageView ivBackSticker;

  @NonNull
  public final ImageView ivBackTextView;

  @NonNull
  public final AppCompatImageView ivBackground;

  @NonNull
  public final AppCompatImageView ivClear;

  @NonNull
  public final AppCompatImageView ivEraser;

  @NonNull
  public final ImageView ivFontColor;

  @NonNull
  public final AppCompatImageView ivRedo;

  @NonNull
  public final ImageView ivSettingBrush;

  @NonNull
  public final AppCompatImageView ivUndo;

  @NonNull
  public final RelativeLayout llAdView;

  @NonNull
  public final LinearLayout llAdViewFacebook;

  @NonNull
  public final LinearLayout llAddText;

  @NonNull
  public final LinearLayout llBack;

  @NonNull
  public final LinearLayout llBrush;

  @NonNull
  public final LinearLayout llBrushControl;

  @NonNull
  public final LinearLayout llBrushView;

  @NonNull
  public final LinearLayout llButton;

  @NonNull
  public final LinearLayout llClear;

  @NonNull
  public final LinearLayout llEraser;

  @NonNull
  public final LinearLayout llFooter;

  @NonNull
  public final LinearLayout llHeader;

  @NonNull
  public final LinearLayout llMagicBrush;

  @NonNull
  public final LinearLayout llMagicBrushView;

  @NonNull
  public final LinearLayout llPencil;

  @NonNull
  public final LinearLayout llPencilView;

  @NonNull
  public final LinearLayout llRedo;

  @NonNull
  public final LinearLayout llSave;

  @NonNull
  public final LinearLayout llSticker;

  @NonNull
  public final LinearLayout llStickerView;

  @NonNull
  public final LinearLayout llTextView;

  @NonNull
  public final LinearLayout llUndo;

  @NonNull
  public final RelativeLayout rlMain;

  @NonNull
  public final RecyclerView rvFontStyle;

  @NonNull
  public final RecyclerView rvMGButton;

  @NonNull
  public final RecyclerView rvPaintBrush;

  @NonNull
  public final RecyclerView rvPencil;

  @NonNull
  public final IndicatorSeekBar sbBrushSize;

  @NonNull
  public final IndicatorSeekBar sbSmoothness;

  @NonNull
  public final StickerView stickerView;

  @NonNull
  public final TabLayout tabLayoutStickerCategories;

  @NonNull
  public final ViewPager2 viewPagerStickerCategories;

  private ActivityEditorBinding(@NonNull LinearLayout rootView, @NonNull WTDrawingView drawingView,
      @NonNull OverlayBrushView drawingViewBitmap1, @NonNull RelativeLayout flMainLayout,
      @NonNull ImageView ivBackBrush, @NonNull ImageView ivBackMagicBrush,
      @NonNull ImageView ivBackPencil, @NonNull ImageView ivBackSticker,
      @NonNull ImageView ivBackTextView, @NonNull AppCompatImageView ivBackground,
      @NonNull AppCompatImageView ivClear, @NonNull AppCompatImageView ivEraser,
      @NonNull ImageView ivFontColor, @NonNull AppCompatImageView ivRedo,
      @NonNull ImageView ivSettingBrush, @NonNull AppCompatImageView ivUndo,
      @NonNull RelativeLayout llAdView, @NonNull LinearLayout llAdViewFacebook,
      @NonNull LinearLayout llAddText, @NonNull LinearLayout llBack, @NonNull LinearLayout llBrush,
      @NonNull LinearLayout llBrushControl, @NonNull LinearLayout llBrushView,
      @NonNull LinearLayout llButton, @NonNull LinearLayout llClear, @NonNull LinearLayout llEraser,
      @NonNull LinearLayout llFooter, @NonNull LinearLayout llHeader,
      @NonNull LinearLayout llMagicBrush, @NonNull LinearLayout llMagicBrushView,
      @NonNull LinearLayout llPencil, @NonNull LinearLayout llPencilView,
      @NonNull LinearLayout llRedo, @NonNull LinearLayout llSave, @NonNull LinearLayout llSticker,
      @NonNull LinearLayout llStickerView, @NonNull LinearLayout llTextView,
      @NonNull LinearLayout llUndo, @NonNull RelativeLayout rlMain,
      @NonNull RecyclerView rvFontStyle, @NonNull RecyclerView rvMGButton,
      @NonNull RecyclerView rvPaintBrush, @NonNull RecyclerView rvPencil,
      @NonNull IndicatorSeekBar sbBrushSize, @NonNull IndicatorSeekBar sbSmoothness,
      @NonNull StickerView stickerView, @NonNull TabLayout tabLayoutStickerCategories,
      @NonNull ViewPager2 viewPagerStickerCategories) {
    this.rootView = rootView;
    this.drawingView = drawingView;
    this.drawingViewBitmap1 = drawingViewBitmap1;
    this.flMainLayout = flMainLayout;
    this.ivBackBrush = ivBackBrush;
    this.ivBackMagicBrush = ivBackMagicBrush;
    this.ivBackPencil = ivBackPencil;
    this.ivBackSticker = ivBackSticker;
    this.ivBackTextView = ivBackTextView;
    this.ivBackground = ivBackground;
    this.ivClear = ivClear;
    this.ivEraser = ivEraser;
    this.ivFontColor = ivFontColor;
    this.ivRedo = ivRedo;
    this.ivSettingBrush = ivSettingBrush;
    this.ivUndo = ivUndo;
    this.llAdView = llAdView;
    this.llAdViewFacebook = llAdViewFacebook;
    this.llAddText = llAddText;
    this.llBack = llBack;
    this.llBrush = llBrush;
    this.llBrushControl = llBrushControl;
    this.llBrushView = llBrushView;
    this.llButton = llButton;
    this.llClear = llClear;
    this.llEraser = llEraser;
    this.llFooter = llFooter;
    this.llHeader = llHeader;
    this.llMagicBrush = llMagicBrush;
    this.llMagicBrushView = llMagicBrushView;
    this.llPencil = llPencil;
    this.llPencilView = llPencilView;
    this.llRedo = llRedo;
    this.llSave = llSave;
    this.llSticker = llSticker;
    this.llStickerView = llStickerView;
    this.llTextView = llTextView;
    this.llUndo = llUndo;
    this.rlMain = rlMain;
    this.rvFontStyle = rvFontStyle;
    this.rvMGButton = rvMGButton;
    this.rvPaintBrush = rvPaintBrush;
    this.rvPencil = rvPencil;
    this.sbBrushSize = sbBrushSize;
    this.sbSmoothness = sbSmoothness;
    this.stickerView = stickerView;
    this.tabLayoutStickerCategories = tabLayoutStickerCategories;
    this.viewPagerStickerCategories = viewPagerStickerCategories;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEditorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEditorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_editor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEditorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drawing_view;
      WTDrawingView drawingView = ViewBindings.findChildViewById(rootView, id);
      if (drawingView == null) {
        break missingId;
      }

      id = R.id.drawingViewBitmap1;
      OverlayBrushView drawingViewBitmap1 = ViewBindings.findChildViewById(rootView, id);
      if (drawingViewBitmap1 == null) {
        break missingId;
      }

      id = R.id.flMainLayout;
      RelativeLayout flMainLayout = ViewBindings.findChildViewById(rootView, id);
      if (flMainLayout == null) {
        break missingId;
      }

      id = R.id.ivBackBrush;
      ImageView ivBackBrush = ViewBindings.findChildViewById(rootView, id);
      if (ivBackBrush == null) {
        break missingId;
      }

      id = R.id.ivBackMagicBrush;
      ImageView ivBackMagicBrush = ViewBindings.findChildViewById(rootView, id);
      if (ivBackMagicBrush == null) {
        break missingId;
      }

      id = R.id.ivBackPencil;
      ImageView ivBackPencil = ViewBindings.findChildViewById(rootView, id);
      if (ivBackPencil == null) {
        break missingId;
      }

      id = R.id.ivBackSticker;
      ImageView ivBackSticker = ViewBindings.findChildViewById(rootView, id);
      if (ivBackSticker == null) {
        break missingId;
      }

      id = R.id.ivBackTextView;
      ImageView ivBackTextView = ViewBindings.findChildViewById(rootView, id);
      if (ivBackTextView == null) {
        break missingId;
      }

      id = R.id.ivBackground;
      AppCompatImageView ivBackground = ViewBindings.findChildViewById(rootView, id);
      if (ivBackground == null) {
        break missingId;
      }

      id = R.id.ivClear;
      AppCompatImageView ivClear = ViewBindings.findChildViewById(rootView, id);
      if (ivClear == null) {
        break missingId;
      }

      id = R.id.ivEraser;
      AppCompatImageView ivEraser = ViewBindings.findChildViewById(rootView, id);
      if (ivEraser == null) {
        break missingId;
      }

      id = R.id.ivFontColor;
      ImageView ivFontColor = ViewBindings.findChildViewById(rootView, id);
      if (ivFontColor == null) {
        break missingId;
      }

      id = R.id.ivRedo;
      AppCompatImageView ivRedo = ViewBindings.findChildViewById(rootView, id);
      if (ivRedo == null) {
        break missingId;
      }

      id = R.id.ivSettingBrush;
      ImageView ivSettingBrush = ViewBindings.findChildViewById(rootView, id);
      if (ivSettingBrush == null) {
        break missingId;
      }

      id = R.id.ivUndo;
      AppCompatImageView ivUndo = ViewBindings.findChildViewById(rootView, id);
      if (ivUndo == null) {
        break missingId;
      }

      id = R.id.llAdView;
      RelativeLayout llAdView = ViewBindings.findChildViewById(rootView, id);
      if (llAdView == null) {
        break missingId;
      }

      id = R.id.llAdViewFacebook;
      LinearLayout llAdViewFacebook = ViewBindings.findChildViewById(rootView, id);
      if (llAdViewFacebook == null) {
        break missingId;
      }

      id = R.id.llAddText;
      LinearLayout llAddText = ViewBindings.findChildViewById(rootView, id);
      if (llAddText == null) {
        break missingId;
      }

      id = R.id.llBack;
      LinearLayout llBack = ViewBindings.findChildViewById(rootView, id);
      if (llBack == null) {
        break missingId;
      }

      id = R.id.llBrush;
      LinearLayout llBrush = ViewBindings.findChildViewById(rootView, id);
      if (llBrush == null) {
        break missingId;
      }

      id = R.id.llBrushControl;
      LinearLayout llBrushControl = ViewBindings.findChildViewById(rootView, id);
      if (llBrushControl == null) {
        break missingId;
      }

      id = R.id.llBrushView;
      LinearLayout llBrushView = ViewBindings.findChildViewById(rootView, id);
      if (llBrushView == null) {
        break missingId;
      }

      id = R.id.llButton;
      LinearLayout llButton = ViewBindings.findChildViewById(rootView, id);
      if (llButton == null) {
        break missingId;
      }

      id = R.id.llClear;
      LinearLayout llClear = ViewBindings.findChildViewById(rootView, id);
      if (llClear == null) {
        break missingId;
      }

      id = R.id.llEraser;
      LinearLayout llEraser = ViewBindings.findChildViewById(rootView, id);
      if (llEraser == null) {
        break missingId;
      }

      id = R.id.llFooter;
      LinearLayout llFooter = ViewBindings.findChildViewById(rootView, id);
      if (llFooter == null) {
        break missingId;
      }

      id = R.id.llHeader;
      LinearLayout llHeader = ViewBindings.findChildViewById(rootView, id);
      if (llHeader == null) {
        break missingId;
      }

      id = R.id.llMagicBrush;
      LinearLayout llMagicBrush = ViewBindings.findChildViewById(rootView, id);
      if (llMagicBrush == null) {
        break missingId;
      }

      id = R.id.llMagicBrushView;
      LinearLayout llMagicBrushView = ViewBindings.findChildViewById(rootView, id);
      if (llMagicBrushView == null) {
        break missingId;
      }

      id = R.id.llPencil;
      LinearLayout llPencil = ViewBindings.findChildViewById(rootView, id);
      if (llPencil == null) {
        break missingId;
      }

      id = R.id.llPencilView;
      LinearLayout llPencilView = ViewBindings.findChildViewById(rootView, id);
      if (llPencilView == null) {
        break missingId;
      }

      id = R.id.llRedo;
      LinearLayout llRedo = ViewBindings.findChildViewById(rootView, id);
      if (llRedo == null) {
        break missingId;
      }

      id = R.id.llSave;
      LinearLayout llSave = ViewBindings.findChildViewById(rootView, id);
      if (llSave == null) {
        break missingId;
      }

      id = R.id.llSticker;
      LinearLayout llSticker = ViewBindings.findChildViewById(rootView, id);
      if (llSticker == null) {
        break missingId;
      }

      id = R.id.llStickerView;
      LinearLayout llStickerView = ViewBindings.findChildViewById(rootView, id);
      if (llStickerView == null) {
        break missingId;
      }

      id = R.id.llTextView;
      LinearLayout llTextView = ViewBindings.findChildViewById(rootView, id);
      if (llTextView == null) {
        break missingId;
      }

      id = R.id.llUndo;
      LinearLayout llUndo = ViewBindings.findChildViewById(rootView, id);
      if (llUndo == null) {
        break missingId;
      }

      id = R.id.rlMain;
      RelativeLayout rlMain = ViewBindings.findChildViewById(rootView, id);
      if (rlMain == null) {
        break missingId;
      }

      id = R.id.rvFontStyle;
      RecyclerView rvFontStyle = ViewBindings.findChildViewById(rootView, id);
      if (rvFontStyle == null) {
        break missingId;
      }

      id = R.id.rvMGButton;
      RecyclerView rvMGButton = ViewBindings.findChildViewById(rootView, id);
      if (rvMGButton == null) {
        break missingId;
      }

      id = R.id.rvPaintBrush;
      RecyclerView rvPaintBrush = ViewBindings.findChildViewById(rootView, id);
      if (rvPaintBrush == null) {
        break missingId;
      }

      id = R.id.rvPencil;
      RecyclerView rvPencil = ViewBindings.findChildViewById(rootView, id);
      if (rvPencil == null) {
        break missingId;
      }

      id = R.id.sbBrushSize;
      IndicatorSeekBar sbBrushSize = ViewBindings.findChildViewById(rootView, id);
      if (sbBrushSize == null) {
        break missingId;
      }

      id = R.id.sbSmoothness;
      IndicatorSeekBar sbSmoothness = ViewBindings.findChildViewById(rootView, id);
      if (sbSmoothness == null) {
        break missingId;
      }

      id = R.id.sticker_view;
      StickerView stickerView = ViewBindings.findChildViewById(rootView, id);
      if (stickerView == null) {
        break missingId;
      }

      id = R.id.tabLayoutStickerCategories;
      TabLayout tabLayoutStickerCategories = ViewBindings.findChildViewById(rootView, id);
      if (tabLayoutStickerCategories == null) {
        break missingId;
      }

      id = R.id.viewPagerStickerCategories;
      ViewPager2 viewPagerStickerCategories = ViewBindings.findChildViewById(rootView, id);
      if (viewPagerStickerCategories == null) {
        break missingId;
      }

      return new ActivityEditorBinding((LinearLayout) rootView, drawingView, drawingViewBitmap1,
          flMainLayout, ivBackBrush, ivBackMagicBrush, ivBackPencil, ivBackSticker, ivBackTextView,
          ivBackground, ivClear, ivEraser, ivFontColor, ivRedo, ivSettingBrush, ivUndo, llAdView,
          llAdViewFacebook, llAddText, llBack, llBrush, llBrushControl, llBrushView, llButton,
          llClear, llEraser, llFooter, llHeader, llMagicBrush, llMagicBrushView, llPencil,
          llPencilView, llRedo, llSave, llSticker, llStickerView, llTextView, llUndo, rlMain,
          rvFontStyle, rvMGButton, rvPaintBrush, rvPencil, sbBrushSize, sbSmoothness, stickerView,
          tabLayoutStickerCategories, viewPagerStickerCategories);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
