package drawing.jaraappskids.kidsdrawing.drawing

import android.graphics.BlurMaskFilter
import android.graphics.Color
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Comprehensive unit tests for WTDrawingView
 * Tests core drawing functionality, brush settings, and performance
 */
@RunWith(RobolectricTestRunner::class)
class WTDrawingViewTest : BaseDrawingTest() {

    @Test
    fun `test drawing view initialization`() {
        // Given: A new drawing view
        // When: View is initialized
        // Then: View should be properly initialized
        assertThat(drawingView).isNotNull()

        // Test that we can call methods without exceptions
        assertNoExceptions {
            drawingView.setPaintColor(Color.BLACK)
            drawingView.setPaintStrokeWidth(10)
        }
    }

    @Test
    fun `test stroke color setting`() {
        // Given: Various test colors
        val testColors = TestUtils.getTestColorPalette()

        testColors.forEach { color ->
            // When: Setting stroke color
            assertNoExceptions {
                drawingView.setPaintColor(color)
            }

            // Then: Color should be set without exceptions
            // Note: DrawingView doesn't expose getter for color
        }
    }

    @Test
    fun `test stroke width adjustment within valid range`() {
        // Given: Various brush sizes
        val testSizes = TestUtils.getTestBrushSizes()

        testSizes.forEach { size ->
            // When: Setting stroke width
            assertNoExceptions {
                drawingView.setPaintStrokeWidth(size.toInt())
            }

            // Then: Width should be set without exceptions
            // Note: DrawingView doesn't expose getter for stroke width
        }
    }

    @Test
    fun `test drawing functionality`() {
        // Given: Drawing view ready for drawing
        // When: Setting up drawing parameters
        assertNoExceptions {
            drawingView.setPaintColor(Color.RED)
            drawingView.setPaintStrokeWidth(15)
        }

        // Then: Drawing setup should complete without errors
        // Note: DrawingView doesn't have eraser mode functionality
    }

    @Test
    fun `test thin stroke configuration`() {
        // When: Setting thin stroke (like pencil)
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(1)
        }

        // When: Setting normal stroke
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(10)
        }

        // Then: Both configurations should work without errors
        // Note: DrawingView doesn't have specific pencil mode
    }

    @Test
    fun `test drawing operations`() {
        // Given: Various stroke widths
        val strokeWidths = intArrayOf(1, 5, 10, 15, 20, 25, 30)

        strokeWidths.forEach { strokeWidth ->
            // When: Setting stroke width
            assertNoExceptions {
                drawingView.setPaintStrokeWidth(strokeWidth)
            }

            // Then: No exceptions should be thrown
        }
    }

    @Test
    fun `test undo functionality`() {
        // Given: Drawing view with some drawing operations
        setupDrawingView(Color.RED, 10f, false)
        simulateDrawing()
        waitForDrawingCompletion()
        
        // When: Performing undo
        assertNoExceptions {
            drawingView.undo()
        }
        
        // Then: Operation should complete without errors
    }

    @Test
    fun `test redo functionality`() {
        // Given: Drawing view with undo operation performed
        setupDrawingView(Color.BLUE, 15f, false)
        simulateDrawing()
        drawingView.undo()
        
        // When: Performing redo
        assertNoExceptions {
            drawingView.redo()
        }
        
        // Then: Operation should complete without errors
    }

    @Test
    fun `test clear functionality`() {
        // Given: Drawing view with multiple drawing operations
        createComplexDrawingScenario()

        // When: Clearing the canvas
        assertNoExceptions {
            drawingView.clearCanvas()
        }

        // Then: Canvas should be cleared without errors
    }

    @Test
    fun `test multiple touch events simulation`() {
        // Given: Drawing view ready for input
        setupDrawingView(Color.GREEN, 12f, false)
        
        // When: Simulating complex drawing pattern
        val points = TestUtils.createComplexDrawingPattern()
        
        assertNoExceptions {
            points.forEachIndexed { index, (x, y) ->
                val action = when (index) {
                    0 -> android.view.MotionEvent.ACTION_DOWN
                    points.size - 1 -> android.view.MotionEvent.ACTION_UP
                    else -> android.view.MotionEvent.ACTION_MOVE
                }
                
                val event = TestUtils.createMockMotionEvent(action, x, y)
                drawingView.onTouchEvent(event)
            }
        }
        
        // Then: All touch events should be processed without errors
    }

    @Test
    fun `test rapid tool switching performance`() {
        // Given: Drawing view ready for rapid changes
        
        // When: Performing rapid tool switching
        val executionTime = measurePerformance {
            performRapidToolSwitching(20)
        }
        
        // Then: Performance should be acceptable (under 2 seconds for 20 switches)
        assertThat(executionTime).isLessThan(2000L)
    }

    @Test
    fun `test memory management during intensive drawing`() {
        // Given: Initial memory state
        val initialMemory = TestUtils.measureMemoryUsage()
        
        // When: Performing intensive drawing operations
        repeat(50) { i ->
            setupDrawingView(
                Color.rgb(i * 5 % 255, (i * 7) % 255, (i * 11) % 255),
                (i % 20) + 5f,
                i % 3 == 0
            )
            simulateDrawing(
                (i * 3f) % 150f,
                (i * 5f) % 150f,
                ((i + 10) * 3f) % 150f,
                ((i + 10) * 5f) % 150f
            )
        }
        
        // Then: Memory usage should not increase excessively
        val finalMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
        val maxAllowedIncrease = 30 * 1024 * 1024 // 30MB
        
        assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
    }

    @Test
    fun `test edge case - zero dimensions`() {
        // When: Attempting to initialize with zero dimensions
        assertNoExceptions {
            drawingView.layout(0, 0, 0, 0)
        }

        // Then: Should handle gracefully without crashing
    }

    @Test
    fun `test edge case - negative stroke width`() {
        // When: Setting negative stroke width
        assertNoExceptions {
            drawingView.setPaintStrokeWidth(-5)
        }

        // Then: Should handle gracefully (implementation may clamp to minimum)
    }

    @Test
    fun `test edge case - extreme color values`() {
        // Given: Extreme color values
        val extremeColors = intArrayOf(
            Int.MIN_VALUE,
            Int.MAX_VALUE,
            0,
            -1,
            0xFFFFFFFF.toInt()
        )
        
        extremeColors.forEach { color ->
            // When: Setting extreme color values
            assertNoExceptions {
                drawingView.setPaintColor(color)
            }

            // Then: Should handle without crashing
        }
    }

    @Test
    fun `test cleanup functionality`() {
        // Given: Drawing view with operations performed
        createComplexDrawingScenario()
        
        // When: Performing cleanup
        assertNoExceptions {
            drawingView.cleanup()
        }
        
        // Then: Cleanup should complete without errors
    }
}
