package drawing.jaraappskids.kidsdrawing.custom;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u000f\b\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004B\u0017\b\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010(\u001a\u00020)J\b\u0010*\u001a\u00020)H\u0014J\u0010\u0010+\u001a\u00020)2\u0006\u0010,\u001a\u00020\u0011H\u0014J(\u0010-\u001a\u00020)2\u0006\u0010.\u001a\u00020 2\u0006\u0010/\u001a\u00020 2\u0006\u00100\u001a\u00020 2\u0006\u00101\u001a\u00020 H\u0014J\u0010\u00102\u001a\u0002032\u0006\u00104\u001a\u000205H\u0016J\u0016\u00106\u001a\u00020)2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u00107\u001a\u000208J\u0006\u00109\u001a\u00020)R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u00020\u000bX\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000fR\u001a\u0010\u0010\u001a\u00020\u0011X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015R\u001a\u0010\u0016\u001a\u00020\u000bX\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\r\"\u0004\b\u0018\u0010\u000fR\u001a\u0010\u0019\u001a\u00020\u001aX\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR\u001a\u0010\u001f\u001a\u00020 X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\"\"\u0004\b#\u0010$R\u001a\u0010%\u001a\u00020 X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\"\"\u0004\b\'\u0010$\u00a8\u0006:"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/custom/DrawingViewBitmap;", "Landroid/view/View;", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "attrs", "Landroid/util/AttributeSet;", "(Landroid/content/Context;Landroid/util/AttributeSet;)V", "canvasBitmap", "Landroid/graphics/Bitmap;", "canvasPaint", "Landroid/graphics/Paint;", "getCanvasPaint", "()Landroid/graphics/Paint;", "setCanvasPaint", "(Landroid/graphics/Paint;)V", "drawCanvas", "Landroid/graphics/Canvas;", "getDrawCanvas", "()Landroid/graphics/Canvas;", "setDrawCanvas", "(Landroid/graphics/Canvas;)V", "drawPaint", "getDrawPaint", "setDrawPaint", "drawPath", "Landroid/graphics/Path;", "getDrawPath", "()Landroid/graphics/Path;", "setDrawPath", "(Landroid/graphics/Path;)V", "paintColor", "", "getPaintColor", "()I", "setPaintColor", "(I)V", "strokeWidth", "getStrokeWidth", "setStrokeWidth", "cleanup", "", "onDetachedFromWindow", "onDraw", "canvas", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "", "event", "Landroid/view/MotionEvent;", "setPattern", "string", "", "setupDrawing", "app_debug"})
public final class DrawingViewBitmap extends android.view.View {
    public android.graphics.Path drawPath;
    public android.graphics.Paint drawPaint;
    public android.graphics.Paint canvasPaint;
    private int paintColor = android.graphics.Color.TRANSPARENT;
    private int strokeWidth = 0;
    public android.graphics.Canvas drawCanvas;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap canvasBitmap;
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Path getDrawPath() {
        return null;
    }
    
    public final void setDrawPath(@org.jetbrains.annotations.NotNull()
    android.graphics.Path p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Paint getDrawPaint() {
        return null;
    }
    
    public final void setDrawPaint(@org.jetbrains.annotations.NotNull()
    android.graphics.Paint p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Paint getCanvasPaint() {
        return null;
    }
    
    public final void setCanvasPaint(@org.jetbrains.annotations.NotNull()
    android.graphics.Paint p0) {
    }
    
    public final int getPaintColor() {
        return 0;
    }
    
    public final void setPaintColor(int p0) {
    }
    
    public final int getStrokeWidth() {
        return 0;
    }
    
    public final void setStrokeWidth(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Canvas getDrawCanvas() {
        return null;
    }
    
    public final void setDrawCanvas(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas p0) {
    }
    
    public DrawingViewBitmap(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    public DrawingViewBitmap(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    public final void setupDrawing() {
    }
    
    @java.lang.Override()
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    public final void setPattern(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String string) {
    }
    
    /**
     * Clean up resources to prevent memory leaks
     */
    public final void cleanup() {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
}