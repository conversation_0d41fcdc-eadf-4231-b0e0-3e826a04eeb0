package drawing.jaraappskids.kidsdrawing.utils;

/**
 * Ads utility object - renamed from CommonConstantAd to test compilation
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0006J \u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\t2\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u000bJ\u000e\u0010\f\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\tJ\"\u0010\r\u001a\u00020\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0012\u001a\u00020\u0013J\u000e\u0010\u0014\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\tJ\u0010\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0017H\u0016J\u0010\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0017H\u0016J\u0010\u0010\u0019\u001a\u00020\u00062\b\u0010\b\u001a\u0004\u0018\u00010\tJ\u0018\u0010\u001a\u001a\u00020\u00062\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u0012\u001a\u00020\u0013J\u0018\u0010\u001d\u001a\u00020\u00062\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u0012\u001a\u00020\u0013J\u0018\u0010\u001e\u001a\u00020\u00062\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u0012\u001a\u00020\u0013R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Ldrawing/jaraappskids/kidsdrawing/utils/AdsUtility;", "Landroidx/lifecycle/DefaultLifecycleObserver;", "()V", "TAG", "", "cleanup", "", "initializeAds", "context", "Landroid/content/Context;", "callback", "Lkotlin/Function0;", "loadAppOpenAd", "loadBannerAd", "adViewLayout", "Landroid/widget/LinearLayout;", "facebookAdViewLayout", "Landroid/widget/RelativeLayout;", "adsCallback", "Ldrawing/jaraappskids/kidsdrawing/interfaces/AdsCallback;", "loadGoogleRewardedAd", "onStart", "owner", "Landroidx/lifecycle/LifecycleOwner;", "onStop", "preloadInterstitialAds", "showAppOpenAd", "activity", "Landroid/app/Activity;", "showInterstitialAd", "showRewardedAd", "app_debug"})
public final class AdsUtility implements androidx.lifecycle.DefaultLifecycleObserver {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AdsUtility";
    @org.jetbrains.annotations.NotNull()
    public static final drawing.jaraappskids.kidsdrawing.utils.AdsUtility INSTANCE = null;
    
    private AdsUtility() {
        super();
    }
    
    public final void initializeAds(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    public final void preloadInterstitialAds(@org.jetbrains.annotations.Nullable()
    android.content.Context context) {
    }
    
    public final void loadGoogleRewardedAd(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final void loadAppOpenAd(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final void showInterstitialAd(@org.jetbrains.annotations.Nullable()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    public final void showRewardedAd(@org.jetbrains.annotations.Nullable()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    public final void showAppOpenAd(@org.jetbrains.annotations.Nullable()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    public final void loadBannerAd(@org.jetbrains.annotations.Nullable()
    android.widget.LinearLayout adViewLayout, @org.jetbrains.annotations.Nullable()
    android.widget.RelativeLayout facebookAdViewLayout, @org.jetbrains.annotations.NotNull()
    drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback adsCallback) {
    }
    
    public final void cleanup() {
    }
    
    @java.lang.Override()
    public void onStart(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.LifecycleOwner owner) {
    }
    
    @java.lang.Override()
    public void onStop(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.LifecycleOwner owner) {
    }
}