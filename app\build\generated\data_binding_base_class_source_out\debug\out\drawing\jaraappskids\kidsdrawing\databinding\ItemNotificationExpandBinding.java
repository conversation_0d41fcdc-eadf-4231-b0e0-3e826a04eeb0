// Generated by view binder compiler. Do not edit!
package drawing.jaraappskids.kidsdrawing.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import drawing.jaraappskids.kidsdrawing.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationExpandBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView bigIcon;

  @NonNull
  public final FrameLayout flImageView;

  @NonNull
  public final LinearLayout iconContainer;

  @NonNull
  public final LinearLayout llImageView;

  @NonNull
  public final ImageView notificationImg;

  @NonNull
  public final ImageView notificationImg2;

  @NonNull
  public final LinearLayout notificationMain;

  @NonNull
  public final TextView notificationMessage;

  @NonNull
  public final TextView timestamp;

  @NonNull
  public final TextView titleText;

  private ItemNotificationExpandBinding(@NonNull RelativeLayout rootView,
      @NonNull ImageView bigIcon, @NonNull FrameLayout flImageView,
      @NonNull LinearLayout iconContainer, @NonNull LinearLayout llImageView,
      @NonNull ImageView notificationImg, @NonNull ImageView notificationImg2,
      @NonNull LinearLayout notificationMain, @NonNull TextView notificationMessage,
      @NonNull TextView timestamp, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.bigIcon = bigIcon;
    this.flImageView = flImageView;
    this.iconContainer = iconContainer;
    this.llImageView = llImageView;
    this.notificationImg = notificationImg;
    this.notificationImg2 = notificationImg2;
    this.notificationMain = notificationMain;
    this.notificationMessage = notificationMessage;
    this.timestamp = timestamp;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationExpandBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationExpandBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification_expand, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationExpandBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.big_icon;
      ImageView bigIcon = ViewBindings.findChildViewById(rootView, id);
      if (bigIcon == null) {
        break missingId;
      }

      id = R.id.flImageView;
      FrameLayout flImageView = ViewBindings.findChildViewById(rootView, id);
      if (flImageView == null) {
        break missingId;
      }

      id = R.id.icon_container;
      LinearLayout iconContainer = ViewBindings.findChildViewById(rootView, id);
      if (iconContainer == null) {
        break missingId;
      }

      id = R.id.llImageView;
      LinearLayout llImageView = ViewBindings.findChildViewById(rootView, id);
      if (llImageView == null) {
        break missingId;
      }

      id = R.id.notification_img;
      ImageView notificationImg = ViewBindings.findChildViewById(rootView, id);
      if (notificationImg == null) {
        break missingId;
      }

      id = R.id.notification_img2;
      ImageView notificationImg2 = ViewBindings.findChildViewById(rootView, id);
      if (notificationImg2 == null) {
        break missingId;
      }

      id = R.id.notification_main;
      LinearLayout notificationMain = ViewBindings.findChildViewById(rootView, id);
      if (notificationMain == null) {
        break missingId;
      }

      id = R.id.notification_message;
      TextView notificationMessage = ViewBindings.findChildViewById(rootView, id);
      if (notificationMessage == null) {
        break missingId;
      }

      id = R.id.timestamp;
      TextView timestamp = ViewBindings.findChildViewById(rootView, id);
      if (timestamp == null) {
        break missingId;
      }

      id = R.id.title_text;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemNotificationExpandBinding((RelativeLayout) rootView, bigIcon, flImageView,
          iconContainer, llImageView, notificationImg, notificationImg2, notificationMain,
          notificationMessage, timestamp, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
