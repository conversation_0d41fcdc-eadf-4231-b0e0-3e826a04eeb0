{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeDebugResources-85:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6cb5cda3da2f0136ecb83c29cfb693f\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,15391", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,15469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8784e12cfdf22977c2421790741b565f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5709", "endColumns": "139", "endOffsets": "5844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e4138bbfd937e2ada2e439038e6cf17f\\transformed\\preference-1.2.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,267,343,481,650,733", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "175,262,338,476,645,728,806"}, "to": {"startLines": "69,78,151,166,182,186,187", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6929,7677,13465,14706,15891,16330,16413", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "6999,7759,13536,14839,16055,16408,16486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b608133b9f6151e8ab6ea0b248ed861e\\transformed\\jetified-tv-ads-1.0.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,9", "startColumns": "0,0,0,0", "startOffsets": "197,243,348,578", "endColumns": "45,104,62,75", "endOffsets": "242,347,410,653"}, "to": {"startLines": "33,76,77,80", "startColumns": "4,4,4,4", "startOffsets": "3038,7501,7610,7849", "endColumns": "49,108,66,79", "endOffsets": "3083,7605,7672,7924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2507dffaec27660ae19740e0267a928c\\transformed\\browser-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "70,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7004,7929,8029,8136", "endColumns": "99,99,106,98", "endOffsets": "7099,8024,8131,8230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d465a8673ed4187c6f67df5a33e6ac9a\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,289,343,405,471,573,636,738,838,947,997,1054,1158,1249,1293,1377,1411,1445,1492,1562,1601", "endColumns": "40,48,53,61,65,101,62,101,99,108,49,56,103,90,43,83,33,33,46,69,38,55", "endOffsets": "239,288,342,404,470,572,635,737,837,946,996,1053,1157,1248,1292,1376,1410,1444,1491,1561,1600,1656"}, "to": {"startLines": "146,147,148,152,153,154,156,157,158,159,160,161,162,163,169,170,171,172,173,174,175,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13134,13179,13232,13541,13607,13677,13851,13918,14024,14128,14241,14295,14356,14464,15011,15059,15147,15185,15223,15274,15348,16491", "endColumns": "44,52,57,65,69,105,66,105,103,112,53,60,107,94,47,87,37,37,50,73,42,59", "endOffsets": "13174,13227,13285,13602,13672,13778,13913,14019,14123,14236,14290,14351,14459,14554,15054,15142,15180,15218,15269,15343,15386,16546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\003d3540143f1bd8ca35f691a36398bc\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,74,75,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,165,177,178,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3168,3247,3332,3424,4240,4339,4456,7377,7437,7764,8235,8472,8536,8623,8687,8751,8810,8882,8946,9000,9119,9179,9240,9294,9367,9500,9584,9661,9754,9834,9927,10065,10145,10224,10350,10438,10517,10572,10623,10689,10762,10841,10912,10991,11064,11139,11213,11285,11398,11486,11563,11654,11746,11820,11894,11985,12039,12121,12190,12273,12359,12421,12485,12548,12616,12719,12822,12919,13020,13079,14625,15474,15563,15712", "endLines": "5,34,35,36,37,38,46,47,48,74,75,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,165,177,178,180", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "311,3163,3242,3327,3419,3506,4334,4451,4533,7432,7496,7844,8298,8531,8618,8682,8746,8805,8877,8941,8995,9114,9174,9235,9289,9362,9495,9579,9656,9749,9829,9922,10060,10140,10219,10345,10433,10512,10567,10618,10684,10757,10836,10907,10986,11059,11134,11208,11280,11393,11481,11558,11649,11741,11815,11889,11980,12034,12116,12185,12268,12354,12416,12480,12543,12611,12714,12817,12914,13015,13074,13129,14701,15558,15635,15785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6128e6b97d809c1b0cadcc023b8fe28\\transformed\\jetified-play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4711,4814,4976,5103,5211,5361,5490,5606,5849,6009,6117,6281,6413,6568,6713,6776,6841", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "4809,4971,5098,5206,5356,5485,5601,5704,6004,6112,6276,6408,6563,6708,6771,6836,6924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\61ab77ac73f341a2d1782fb97c5b4eb5\\transformed\\jetified-ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "49,50,71,72,73,85,86,149,150,155,164,167,168,179,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4538,4631,7104,7200,7295,8303,8381,13290,13381,13783,14559,14844,14926,15640,16060,16137,16208", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "4626,4706,7195,7290,7372,8376,8467,13376,13460,13846,14620,14921,15006,15707,16132,16203,16325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\af892568e4c6055353b79bd60cd12ea7\\transformed\\core-1.13.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3511,3606,3708,3806,3909,4015,4120,15790", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3601,3703,3801,3904,4010,4115,4235,15886"}}]}]}