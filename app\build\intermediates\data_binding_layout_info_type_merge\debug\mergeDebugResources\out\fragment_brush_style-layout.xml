<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_brush_style" modulePackage="drawing.jaraappskids.kidsdrawing" filePath="app\src\main\res\layout\fragment_brush_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_brush_style_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="223" endOffset="13"/></Target><Target id="@+id/ptn1" view="ImageButton"><Expressions/><location startLine="17" startOffset="12" endLine="27" endOffset="42"/></Target><Target id="@+id/ptn2" view="ImageButton"><Expressions/><location startLine="29" startOffset="12" endLine="39" endOffset="42"/></Target><Target id="@+id/ptn3" view="ImageButton"><Expressions/><location startLine="41" startOffset="12" endLine="51" endOffset="42"/></Target><Target id="@+id/ptn4" view="ImageButton"><Expressions/><location startLine="53" startOffset="12" endLine="63" endOffset="42"/></Target><Target id="@+id/ptn5" view="ImageButton"><Expressions/><location startLine="65" startOffset="12" endLine="75" endOffset="42"/></Target><Target id="@+id/ptn6" view="ImageButton"><Expressions/><location startLine="77" startOffset="12" endLine="87" endOffset="42"/></Target><Target id="@+id/ptn7" view="ImageButton"><Expressions/><location startLine="89" startOffset="12" endLine="99" endOffset="42"/></Target><Target id="@+id/ptn8" view="ImageButton"><Expressions/><location startLine="101" startOffset="12" endLine="111" endOffset="42"/></Target><Target id="@+id/ptn9" view="ImageButton"><Expressions/><location startLine="113" startOffset="12" endLine="123" endOffset="42"/></Target><Target id="@+id/ptn10" view="ImageButton"><Expressions/><location startLine="125" startOffset="12" endLine="135" endOffset="42"/></Target><Target id="@+id/ptn11" view="ImageButton"><Expressions/><location startLine="137" startOffset="12" endLine="147" endOffset="42"/></Target><Target id="@+id/ptn12" view="ImageButton"><Expressions/><location startLine="149" startOffset="12" endLine="159" endOffset="42"/></Target><Target id="@+id/HoriZontalImageButton" view="HorizontalScrollView"><Expressions/><location startLine="165" startOffset="4" endLine="174" endOffset="26"/></Target><Target id="@+id/llRecycleView" view="LinearLayout"><Expressions/><location startLine="176" startOffset="4" endLine="221" endOffset="18"/></Target><Target id="@+id/recycle" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="182" startOffset="8" endLine="188" endOffset="51"/></Target></Targets></Layout>